/**
 * 安全启动脚本
 * 确保所有依赖正确加载后再启动系统
 */

class SafeStartup {
    constructor() {
        this.loadingSteps = [
            { name: 'Three.js主库', check: () => typeof THREE !== 'undefined' },
            { name: 'GLTFLoader', check: () => typeof THREE.GLTFLoader !== 'undefined' },
            { name: 'OrbitControls', check: () => typeof THREE.OrbitControls !== 'undefined' },
            { name: 'DOM就绪', check: () => document.readyState === 'complete' }
        ];
        
        this.managers = {};
        this.isSystemReady = false;
        
        this.startSafeInitialization();
    }

    /**
     * 开始安全初始化
     */
    async startSafeInitialization() {
        console.log('🛡️ 开始安全启动流程...');
        
        try {
            // 等待所有依赖就绪
            await this.waitForDependencies();
            
            // 按顺序初始化管理器
            await this.initializeManagers();
            
            // 初始化UI
            await this.initializeUI();
            
            // 系统就绪
            this.isSystemReady = true;
            console.log('✅ 系统安全启动完成');
            
            this.showSuccessMessage();
            
        } catch (error) {
            console.error('❌ 安全启动失败:', error);
            this.showErrorMessage(error);
        }
    }

    /**
     * 等待依赖就绪
     */
    async waitForDependencies() {
        console.log('⏳ 等待依赖库加载...');
        
        return new Promise((resolve, reject) => {
            const checkInterval = setInterval(() => {
                const allReady = this.loadingSteps.every(step => {
                    const isReady = step.check();
                    console.log(`📋 ${step.name}: ${isReady ? '✅' : '❌'}`);
                    return isReady;
                });
                
                if (allReady) {
                    clearInterval(checkInterval);
                    console.log('✅ 所有依赖已就绪');
                    resolve();
                }
            }, 500);
            
            // 30秒超时
            setTimeout(() => {
                clearInterval(checkInterval);
                reject(new Error('依赖加载超时'));
            }, 30000);
        });
    }

    /**
     * 按顺序初始化管理器
     */
    async initializeManagers() {
        console.log('🔧 初始化管理器...');
        
        // 1. 初始化项目管理器
        await this.initProjectManager();
        
        // 2. 初始化场景管理器
        await this.initSceneManager();
        
        // 3. 初始化工具管理器
        await this.initToolManager();
        
        console.log('✅ 所有管理器初始化完成');
    }

    /**
     * 初始化项目管理器
     */
    async initProjectManager() {
        return new Promise((resolve, reject) => {
            try {
                console.log('📁 创建项目管理器...');
                this.managers.project = new ProjectManager();
                
                // 等待初始化完成
                const checkInit = () => {
                    if (this.managers.project.isInitialized) {
                        console.log('✅ 项目管理器就绪');
                        resolve();
                    } else {
                        setTimeout(checkInit, 100);
                    }
                };
                
                checkInit();
                
                // 超时保护
                setTimeout(() => {
                    console.warn('⚠️ 项目管理器初始化超时');
                    resolve();
                }, 10000);
                
            } catch (error) {
                console.error('❌ 项目管理器初始化失败:', error);
                reject(error);
            }
        });
    }

    /**
     * 初始化场景管理器
     */
    async initSceneManager() {
        return new Promise((resolve, reject) => {
            try {
                console.log('🎬 创建场景管理器...');
                
                // 首先尝试创建基础场景
                if (typeof BridgeScene !== 'undefined') {
                    this.managers.scene = new BridgeScene();
                    console.log('✅ 基础场景创建成功');
                    
                    // 尝试创建增强场景
                    if (typeof EnhancedBridgeScene !== 'undefined') {
                        this.managers.enhancedScene = new EnhancedBridgeScene();
                        
                        // 等待增强功能就绪
                        const checkEnhanced = () => {
                            if (this.managers.enhancedScene.isEnhancementsReady) {
                                console.log('✅ 增强场景就绪');
                                resolve();
                            } else {
                                setTimeout(checkEnhanced, 200);
                            }
                        };
                        
                        checkEnhanced();
                        
                        // 超时保护
                        setTimeout(() => {
                            console.warn('⚠️ 增强场景初始化超时，使用基础场景');
                            resolve();
                        }, 15000);
                        
                    } else {
                        console.warn('⚠️ 增强场景类未定义，使用基础场景');
                        resolve();
                    }
                } else {
                    throw new Error('BridgeScene类未定义');
                }
                
            } catch (error) {
                console.error('❌ 场景管理器初始化失败:', error);
                reject(error);
            }
        });
    }

    /**
     * 初始化工具管理器
     */
    async initToolManager() {
        try {
            console.log('🛠️ 创建工具管理器...');
            
            const scene = this.managers.enhancedScene || this.managers.scene;
            if (scene && typeof ToolManager !== 'undefined') {
                this.managers.tool = new ToolManager(scene.scene, scene.camera, scene.renderer);
                console.log('✅ 工具管理器创建成功');
            } else {
                console.warn('⚠️ 工具管理器创建跳过（依赖不满足）');
            }
            
        } catch (error) {
            console.error('❌ 工具管理器初始化失败:', error);
            // 不抛出错误，允许系统继续运行
        }
    }

    /**
     * 初始化UI
     */
    async initializeUI() {
        console.log('🎨 初始化用户界面...');
        
        try {
            const sceneManager = this.managers.enhancedScene || this.managers.scene;
            const projectManager = this.managers.project;
            
            // 创建基础UI
            if (sceneManager && sceneManager.controls) {
                this.managers.originalUI = new BridgeUI(sceneManager, sceneManager.controls);
                console.log('✅ 基础UI创建成功');
            }
            
            // 尝试创建增强UI
            if (projectManager && sceneManager && typeof ProjectUI !== 'undefined') {
                // 延迟创建，确保场景管理器事件系统就绪
                setTimeout(() => {
                    try {
                        this.managers.projectUI = new ProjectUI(projectManager, sceneManager);
                        console.log('✅ 项目UI创建成功');
                    } catch (error) {
                        console.warn('⚠️ 项目UI创建失败:', error);
                    }
                }, 1000);
            }
            
            // 创建进度UI
            if (sceneManager && sceneManager.progressManager && typeof ProgressUI !== 'undefined') {
                setTimeout(() => {
                    try {
                        this.managers.progressUI = new ProgressUI(sceneManager.progressManager);
                        console.log('✅ 进度UI创建成功');
                    } catch (error) {
                        console.warn('⚠️ 进度UI创建失败:', error);
                    }
                }, 1500);
            }
            
        } catch (error) {
            console.error('❌ UI初始化失败:', error);
            // 不抛出错误，允许基础功能运行
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage() {
        // 更新页面标题
        const projectNameElement = document.getElementById('current-project-name');
        const projectStatusElement = document.getElementById('current-project-status');
        
        if (projectNameElement) {
            projectNameElement.textContent = '系统就绪';
        }
        if (projectStatusElement) {
            projectStatusElement.textContent = '可以使用';
        }
        
        // 隐藏加载界面
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'none';
        }
        
        console.log('🎉 系统启动成功，可以开始使用');
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(error) {
        const projectNameElement = document.getElementById('current-project-name');
        const projectStatusElement = document.getElementById('current-project-status');
        
        if (projectNameElement) {
            projectNameElement.textContent = '启动失败';
        }
        if (projectStatusElement) {
            projectStatusElement.textContent = '请检查控制台';
        }
        
        console.error('🚨 系统启动失败，详细信息请查看控制台');
    }

    /**
     * 获取管理器
     */
    getManager(name) {
        return this.managers[name];
    }

    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            isReady: this.isSystemReady,
            managers: Object.keys(this.managers),
            dependencies: this.loadingSteps.map(step => ({
                name: step.name,
                ready: step.check()
            }))
        };
    }
}

// 全局启动函数
let safeStartup = null;

function startSafeSystem() {
    console.log('🛡️ 启动安全初始化系统...');
    safeStartup = new SafeStartup();
}

// 导出到全局作用域
window.SafeStartup = SafeStartup;
window.getSafeStartup = () => safeStartup;

// 页面加载完成后启动
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startSafeSystem);
} else {
    startSafeSystem();
}
