/**
 * 模型服务
 * 重构后的模型管理，支持高效加载、缓存和内存管理
 */

class ModelService {
    constructor(core) {
        this.core = core;
        
        // 模型加载器
        this.gltfLoader = null;
        this.dracoLoader = null;
        this.ktx2Loader = null;
        
        // 模型缓存和管理
        this.loadedModels = new Map();
        this.loadingPromises = new Map();
        this.modelInstances = new Map();
        
        // 性能统计
        this.stats = {
            totalModelsLoaded: 0,
            totalMemoryUsed: 0,
            loadTimes: [],
            cacheHits: 0,
            cacheMisses: 0
        };
        
        // 配置
        this.config = {
            enableCache: true,
            enableOptimization: true,
            maxCacheSize: 500 * 1024 * 1024, // 500MB
            enableInstancing: true,
            enableLOD: true
        };
    }

    /**
     * 初始化服务
     */
    async initialize() {
        console.log('🎯 初始化模型服务');
        
        await this.initializeLoaders();
        this.setupPerformanceMonitoring();
        
        console.log('✅ 模型服务初始化完成');
    }

    /**
     * 初始化加载器
     */
    async initializeLoaders() {
        return new Promise((resolve) => {
            const checkLoaders = () => {
                if (typeof THREE.GLTFLoader !== 'undefined') {
                    try {
                        this.setupGLTFLoader();
                        this.setupDracoLoader();
                        this.setupKTX2Loader();
                        resolve();
                    } catch (error) {
                        console.error('❌ 加载器初始化失败:', error);
                        this.setupFallbackLoader();
                        resolve();
                    }
                } else {
                    setTimeout(checkLoaders, 100);
                }
            };
            
            checkLoaders();
            
            // 10秒超时
            setTimeout(() => {
                if (!this.gltfLoader) {
                    console.warn('⚠️ GLTFLoader加载超时，使用后备方案');
                    this.setupFallbackLoader();
                    resolve();
                }
            }, 10000);
        });
    }

    /**
     * 设置GLTF加载器
     */
    setupGLTFLoader() {
        this.gltfLoader = new THREE.GLTFLoader();
        console.log('✅ GLTF加载器已设置');
    }

    /**
     * 设置Draco加载器
     */
    setupDracoLoader() {
        if (typeof THREE.DRACOLoader !== 'undefined') {
            this.dracoLoader = new THREE.DRACOLoader();
            this.dracoLoader.setDecoderPath('https://unpkg.com/three@0.158.0/examples/js/libs/draco/');
            this.gltfLoader.setDRACOLoader(this.dracoLoader);
            console.log('✅ Draco加载器已启用');
        } else {
            console.warn('⚠️ Draco加载器不可用');
        }
    }

    /**
     * 设置KTX2加载器
     */
    setupKTX2Loader() {
        if (typeof THREE.KTX2Loader !== 'undefined') {
            this.ktx2Loader = new THREE.KTX2Loader();
            this.ktx2Loader.setTranscoderPath('https://unpkg.com/three@0.158.0/examples/js/libs/basis/');
            this.gltfLoader.setKTX2Loader(this.ktx2Loader);
            console.log('✅ KTX2加载器已启用');
        } else {
            console.warn('⚠️ KTX2加载器不可用');
        }
    }

    /**
     * 设置后备加载器
     */
    setupFallbackLoader() {
        console.log('🔄 设置后备模型加载器');
        
        this.gltfLoader = {
            load: (url, onLoad, onProgress, onError) => {
                console.warn('⚠️ 使用后备加载器创建占位符模型');
                
                setTimeout(() => {
                    const placeholder = this.createPlaceholder(url);
                    onLoad(placeholder);
                }, 100);
            }
        };
    }

    /**
     * 创建占位符模型
     */
    createPlaceholder(url) {
        const geometry = new THREE.BoxGeometry(50, 50, 50);
        const material = new THREE.MeshStandardMaterial({ 
            color: 0x888888,
            wireframe: true
        });
        const mesh = new THREE.Mesh(geometry, material);
        mesh.name = `Placeholder_${url.split('/').pop()}`;
        
        const scene = new THREE.Group();
        scene.add(mesh);
        
        return {
            scene: scene,
            animations: [],
            placeholder: true
        };
    }

    /**
     * 加载模型
     */
    async loadModel(modelInfo, options = {}) {
        const startTime = performance.now();
        
        try {
            const {
                useCache = this.config.enableCache,
                onProgress = null,
                optimize = this.config.enableOptimization,
                createInstance = false
            } = options;

            // 生成缓存键
            const cacheKey = this.generateCacheKey(modelInfo);
            
            // 检查缓存
            if (useCache && this.loadedModels.has(cacheKey)) {
                this.stats.cacheHits++;
                const cached = this.loadedModels.get(cacheKey);
                
                if (createInstance) {
                    return this.createModelInstance(cached, modelInfo);
                }
                
                return this.cloneModel(cached);
            }
            
            this.stats.cacheMisses++;
            
            // 检查是否正在加载
            if (this.loadingPromises.has(cacheKey)) {
                return await this.loadingPromises.get(cacheKey);
            }

            // 开始加载
            const loadingPromise = this.performModelLoad(modelInfo, onProgress, optimize);
            this.loadingPromises.set(cacheKey, loadingPromise);

            const result = await loadingPromise;
            
            // 缓存结果
            if (useCache) {
                await this.cacheModel(cacheKey, result);
            }
            
            // 清理加载Promise
            this.loadingPromises.delete(cacheKey);
            
            // 记录统计信息
            const loadTime = performance.now() - startTime;
            this.stats.loadTimes.push(loadTime);
            this.stats.totalModelsLoaded++;
            
            console.log(`✅ 模型加载成功: ${modelInfo.name} (${loadTime.toFixed(2)}ms)`);
            
            if (createInstance) {
                return this.createModelInstance(result, modelInfo);
            }
            
            return result;
            
        } catch (error) {
            this.loadingPromises.delete(this.generateCacheKey(modelInfo));
            console.error(`❌ 模型加载失败: ${modelInfo.name}`, error);
            this.core.errorHandler.handleError('模型加载失败', error);
            throw error;
        }
    }

    /**
     * 执行模型加载
     */
    async performModelLoad(modelInfo, onProgress, optimize) {
        return new Promise((resolve, reject) => {
            console.log(`📥 开始加载模型: ${modelInfo.name} (${modelInfo.path})`);
            
            this.gltfLoader.load(
                modelInfo.path,
                (gltf) => {
                    // 设置模型信息
                    this.setupModelInfo(gltf, modelInfo);
                    
                    // 优化模型
                    if (optimize) {
                        this.optimizeModel(gltf, modelInfo);
                    }
                    
                    // 触发事件
                    this.core.eventBus.emit('model:loaded', {
                        modelInfo,
                        gltf,
                        optimized: optimize
                    });
                    
                    resolve(gltf);
                },
                (progress) => {
                    if (onProgress) {
                        const percent = progress.total ? (progress.loaded / progress.total) * 100 : 0;
                        onProgress(percent, progress);
                    }
                    
                    this.core.eventBus.emit('model:loadProgress', {
                        modelInfo,
                        progress: {
                            loaded: progress.loaded,
                            total: progress.total,
                            percent: progress.total ? (progress.loaded / progress.total) * 100 : 0
                        }
                    });
                },
                (error) => {
                    console.error(`❌ 模型加载失败: ${modelInfo.name}`, error);
                    this.core.eventBus.emit('model:loadError', { modelInfo, error });
                    reject(error);
                }
            );
        });
    }

    /**
     * 设置模型信息
     */
    setupModelInfo(gltf, modelInfo) {
        gltf.scene.name = modelInfo.name;
        gltf.scene.userData = {
            type: modelInfo.type,
            phase: modelInfo.phase,
            description: modelInfo.description,
            originalPath: modelInfo.path,
            loadedAt: Date.now()
        };
        
        // 应用变换
        if (modelInfo.position) {
            gltf.scene.position.fromArray(modelInfo.position);
        }
        if (modelInfo.rotation) {
            gltf.scene.rotation.fromArray(modelInfo.rotation);
        }
        if (modelInfo.scale) {
            gltf.scene.scale.fromArray(modelInfo.scale);
        }
    }

    /**
     * 优化模型
     */
    optimizeModel(gltf, modelInfo) {
        console.log(`🔧 优化模型: ${modelInfo.name}`);
        
        let triangleCount = 0;
        let materialCount = 0;
        
        gltf.scene.traverse((child) => {
            if (child.isMesh) {
                // 启用阴影
                child.castShadow = true;
                child.receiveShadow = true;
                
                // 优化几何体
                if (child.geometry) {
                    // 计算边界
                    child.geometry.computeBoundingBox();
                    child.geometry.computeBoundingSphere();
                    
                    // 计算法线
                    if (!child.geometry.attributes.normal) {
                        child.geometry.computeVertexNormals();
                    }
                    
                    // 统计三角形
                    if (child.geometry.index) {
                        triangleCount += child.geometry.index.count / 3;
                    } else {
                        triangleCount += child.geometry.attributes.position.count / 3;
                    }
                }
                
                // 优化材质
                if (child.material) {
                    this.optimizeMaterial(child.material, modelInfo);
                    materialCount++;
                }
            }
        });
        
        // 记录优化信息
        gltf.optimizationInfo = {
            triangles: Math.round(triangleCount),
            materials: materialCount,
            optimizedAt: Date.now()
        };
        
        console.log(`✅ 模型优化完成: ${triangleCount.toFixed(0)}个三角形, ${materialCount}个材质`);
    }

    /**
     * 优化材质
     */
    optimizeMaterial(material, modelInfo) {
        if (material.isMeshStandardMaterial) {
            // 设置环境贴图强度
            material.envMapIntensity = 1.0;
            
            // 优化透明材质
            if (material.transparent) {
                material.alphaTest = 0.1;
            }
            
            // 设置相位颜色（如果有的话）
            if (modelInfo.phaseColor) {
                const baseColor = material.color.clone();
                const phaseColor = new THREE.Color(modelInfo.phaseColor);
                material.color.lerp(phaseColor, 0.3);
            }
        }
        
        // 优化材质设置
        material.needsUpdate = true;
    }

    /**
     * 生成缓存键
     */
    generateCacheKey(modelInfo) {
        return `${modelInfo.path}_${modelInfo.phase || 'default'}`;
    }

    /**
     * 缓存模型
     */
    async cacheModel(cacheKey, gltf) {
        const modelSize = this.calculateModelSize(gltf);
        
        // 检查缓存大小限制
        if (this.stats.totalMemoryUsed + modelSize > this.config.maxCacheSize) {
            await this.cleanupCache(modelSize);
        }
        
        this.loadedModels.set(cacheKey, gltf);
        this.stats.totalMemoryUsed += modelSize;
        
        console.log(`💾 模型已缓存: ${cacheKey} (${(modelSize / 1024 / 1024).toFixed(2)}MB)`);
    }

    /**
     * 计算模型大小
     */
    calculateModelSize(gltf) {
        let size = 0;
        
        gltf.scene.traverse((child) => {
            if (child.isMesh && child.geometry) {
                const attributes = child.geometry.attributes;
                for (const name in attributes) {
                    size += attributes[name].array.byteLength;
                }
                
                if (child.geometry.index) {
                    size += child.geometry.index.array.byteLength;
                }
            }
        });
        
        return size;
    }

    /**
     * 清理缓存
     */
    async cleanupCache(requiredSize) {
        console.log('🧹 开始清理模型缓存');
        
        const entries = Array.from(this.loadedModels.entries());
        
        // 按最后使用时间排序
        entries.sort((a, b) => {
            const aTime = a[1].scene.userData.lastUsed || 0;
            const bTime = b[1].scene.userData.lastUsed || 0;
            return aTime - bTime;
        });
        
        let freedSpace = 0;
        
        for (const [key, gltf] of entries) {
            if (freedSpace >= requiredSize) break;
            
            const modelSize = this.calculateModelSize(gltf);
            this.disposeModel(gltf);
            this.loadedModels.delete(key);
            
            freedSpace += modelSize;
            this.stats.totalMemoryUsed -= modelSize;
            
            console.log(`🗑️ 已清理缓存模型: ${key}`);
        }
        
        console.log(`✅ 缓存清理完成，释放 ${(freedSpace / 1024 / 1024).toFixed(2)}MB`);
    }

    /**
     * 销毁模型资源
     */
    disposeModel(gltf) {
        gltf.scene.traverse((child) => {
            if (child.isMesh) {
                if (child.geometry) {
                    child.geometry.dispose();
                }
                
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => this.disposeMaterial(mat));
                    } else {
                        this.disposeMaterial(child.material);
                    }
                }
            }
        });
    }

    /**
     * 销毁材质
     */
    disposeMaterial(material) {
        // 销毁纹理
        for (const key in material) {
            const value = material[key];
            if (value && value.isTexture) {
                value.dispose();
            }
        }
        
        material.dispose();
    }

    /**
     * 克隆模型
     */
    cloneModel(gltf) {
        const cloned = {
            scene: gltf.scene.clone(),
            animations: gltf.animations ? [...gltf.animations] : [],
            optimizationInfo: gltf.optimizationInfo,
            placeholder: gltf.placeholder
        };
        
        // 深度克隆材质
        cloned.scene.traverse((child) => {
            if (child.isMesh && child.material) {
                if (Array.isArray(child.material)) {
                    child.material = child.material.map(mat => mat.clone());
                } else {
                    child.material = child.material.clone();
                }
            }
        });
        
        // 更新使用时间
        gltf.scene.userData.lastUsed = Date.now();
        
        return cloned;
    }

    /**
     * 创建模型实例
     */
    createModelInstance(gltf, modelInfo) {
        const instanceId = `${modelInfo.name}_${Date.now()}_${Math.random()}`;
        
        const instance = {
            id: instanceId,
            scene: gltf.scene.clone(),
            animations: gltf.animations ? [...gltf.animations] : [],
            modelInfo: modelInfo,
            createdAt: Date.now()
        };
        
        this.modelInstances.set(instanceId, instance);
        
        console.log(`🎯 模型实例已创建: ${instanceId}`);
        
        return instance;
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 每30秒输出统计信息
        setInterval(() => {
            this.logPerformanceStats();
        }, 30000);
    }

    /**
     * 输出性能统计
     */
    logPerformanceStats() {
        const avgLoadTime = this.stats.loadTimes.length > 0 
            ? this.stats.loadTimes.reduce((a, b) => a + b, 0) / this.stats.loadTimes.length 
            : 0;
            
        console.log(`📊 模型服务统计:
            总模型数: ${this.stats.totalModelsLoaded}
            缓存命中率: ${(this.stats.cacheHits / (this.stats.cacheHits + this.stats.cacheMisses) * 100).toFixed(1)}%
            平均加载时间: ${avgLoadTime.toFixed(2)}ms
            内存使用: ${(this.stats.totalMemoryUsed / 1024 / 1024).toFixed(2)}MB
            缓存模型数: ${this.loadedModels.size}
            实例数: ${this.modelInstances.size}`);
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.loadedModels.size,
            instanceCount: this.modelInstances.size,
            memoryUsageMB: Math.round(this.stats.totalMemoryUsed / 1024 / 1024)
        };
    }

    /**
     * 卸载模型
     */
    async unloadModel(modelInfo) {
        const cacheKey = this.generateCacheKey(modelInfo);
        
        if (this.loadedModels.has(cacheKey)) {
            const gltf = this.loadedModels.get(cacheKey);
            const modelSize = this.calculateModelSize(gltf);
            
            this.disposeModel(gltf);
            this.loadedModels.delete(cacheKey);
            this.stats.totalMemoryUsed -= modelSize;
            
            console.log(`🗑️ 模型已卸载: ${modelInfo.name}`);
            this.core.eventBus.emit('model:unloaded', { modelInfo });
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('🧹 清理模型服务资源');
        
        // 清理所有缓存模型
        for (const [key, gltf] of this.loadedModels) {
            this.disposeModel(gltf);
        }
        this.loadedModels.clear();
        
        // 清理所有实例
        this.modelInstances.clear();
        
        // 清理加载器
        if (this.dracoLoader) {
            this.dracoLoader.dispose();
        }
        if (this.ktx2Loader) {
            this.ktx2Loader.dispose();
        }
        
        // 重置统计信息
        this.stats = {
            totalModelsLoaded: 0,
            totalMemoryUsed: 0,
            loadTimes: [],
            cacheHits: 0,
            cacheMisses: 0
        };
        
        console.log('✅ 模型服务资源清理完成');
    }
}