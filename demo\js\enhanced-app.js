/**
 * 增强版路桥修建演示系统主应用程序
 * 整合项目管理、模型加载、进度展示等功能
 */

class EnhancedBridgeApp {
    constructor() {
        // 核心管理器
        this.projectManager = null;
        this.sceneManager = null;
        this.progressManager = null;
        
        // UI管理器
        this.projectUI = null;
        this.progressUI = null;
        this.originalUI = null;
        
        // 应用状态
        this.isInitialized = false;
        this.isRunning = false;
        this.currentProject = null;
        
        // 性能监控
        this.performanceMonitor = {
            frameCount: 0,
            lastTime: 0,
            fps: 0
        };
        
        this.init();
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('🌉 增强版路桥演示系统启动中...');
            
            // 检查浏览器兼容性
            if (!this.checkCompatibility()) {
                this.showCompatibilityError();
                return;
            }
            
            // 检查Three.js
            if (typeof THREE === 'undefined') {
                throw new Error('Three.js库未加载');
            }
            console.log('✅ Three.js已就绪，版本:', THREE.REVISION);

            // 显示加载进度
            this.updateLoadingProgress(10, '初始化项目管理器...');
            
            // 初始化项目管理器
            this.projectManager = new ProjectManager();
            await this.waitForManagerInit(this.projectManager);
            
            this.updateLoadingProgress(30, '初始化增强场景...');

            // 初始化增强场景管理器
            this.sceneManager = new EnhancedBridgeScene();

            // 等待增强功能初始化完成
            await this.waitForSceneEnhancements();

            this.progressManager = this.sceneManager.getProgressManager();
            
            this.updateLoadingProgress(50, '设置用户界面...');

            // 确保场景管理器完全就绪后再创建UI
            if (this.sceneManager && this.progressManager) {
                // 初始化UI管理器
                this.projectUI = new ProjectUI(this.projectManager, this.sceneManager);
                this.progressUI = new ProgressUI(this.progressManager);

                // 保持原有UI功能
                this.originalUI = new BridgeUI(this.sceneManager, this.sceneManager.controls);
            } else {
                console.warn('⚠️ 场景管理器或进度管理器未就绪，跳过UI初始化');

                // 创建简化的UI
                this.createFallbackUI();
            }
            
            this.updateLoadingProgress(70, '建立系统连接...');
            
            // 建立管理器之间的连接
            this.setupManagerConnections();
            
            this.updateLoadingProgress(90, '完成初始化...');
            
            // 设置全局事件监听器
            this.setupGlobalEventListeners();
            
            // 启动性能监控
            this.startPerformanceMonitoring();
            
            this.updateLoadingProgress(100, '系统就绪');
            
            // 标记为已初始化
            this.isInitialized = true;
            this.isRunning = true;
            
            console.log('✅ 增强版路桥演示系统启动完成');
            
            // 显示欢迎消息
            setTimeout(() => {
                if (this.originalUI) {
                    this.originalUI.showNotification('success', '系统启动', '增强版路桥演示系统已成功启动');
                }
            }, 2500);
            
        } catch (error) {
            console.error('❌ 系统启动失败:', error);
            this.showInitializationError(error);
        }
    }

    /**
     * 检查浏览器兼容性
     */
    checkCompatibility() {
        // 检查WebGL支持
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        if (!gl) {
            console.error('❌ 浏览器不支持WebGL');
            return false;
        }
        
        // 检查必要的Web API
        const requiredAPIs = ['fetch', 'Promise', 'Map', 'Set'];
        for (const api of requiredAPIs) {
            if (typeof window[api] === 'undefined') {
                console.error(`❌ 浏览器不支持 ${api} API`);
                return false;
            }
        }
        
        console.log('✅ 浏览器兼容性检查通过');
        return true;
    }

    /**
     * 等待管理器初始化
     */
    async waitForManagerInit(manager) {
        return new Promise((resolve) => {
            if (manager.isInitialized) {
                resolve();
                return;
            }

            manager.on('initialized', () => {
                resolve();
            });

            // 超时保护
            setTimeout(() => {
                console.warn('⚠️ 管理器初始化超时');
                resolve();
            }, 10000);
        });
    }

    /**
     * 等待场景增强功能初始化
     */
    async waitForSceneEnhancements() {
        return new Promise((resolve) => {
            const checkReady = () => {
                if (this.sceneManager.isEnhancementsReady) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };

            checkReady();

            // 超时保护
            setTimeout(() => {
                console.warn('⚠️ 场景增强功能初始化超时，继续启动');
                resolve();
            }, 15000);
        });
    }

    /**
     * 创建后备UI
     */
    createFallbackUI() {
        console.log('🔄 创建后备UI...');

        // 只保持原有UI功能
        if (this.sceneManager && this.sceneManager.controls) {
            this.originalUI = new BridgeUI(this.sceneManager, this.sceneManager.controls);
        }

        // 显示提示信息
        setTimeout(() => {
            if (this.originalUI) {
                this.originalUI.showNotification('warning', '功能受限', '部分增强功能不可用，系统运行在兼容模式');
            }
        }, 2000);

        console.log('✅ 后备UI创建完成');
    }

    /**
     * 等待场景增强功能初始化
     */
    async waitForSceneEnhancements() {
        return new Promise((resolve) => {
            const checkReady = () => {
                if (this.sceneManager.isEnhancementsReady) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };

            checkReady();

            // 超时保护
            setTimeout(() => {
                console.warn('⚠️ 场景增强功能初始化超时，继续启动');
                resolve();
            }, 15000);
        });
    }

    /**
     * 建立管理器连接
     */
    setupManagerConnections() {
        // 项目管理器 -> 场景管理器
        this.projectManager.on('projectLoaded', async (data) => {
            console.log('🔗 项目加载，更新场景...');
            this.currentProject = data.project;
            
            // 可选择是否自动加载模型
            const autoLoad = true; // 可以通过设置控制
            if (autoLoad) {
                await this.sceneManager.loadProjectModels(data.project);
            }
        });
        
        // 场景管理器 -> 进度UI
        this.sceneManager.on('projectModelsLoaded', (data) => {
            console.log('🔗 模型加载完成，更新进度UI...');
            if (this.progressUI && data.project.config) {
                this.progressUI.updateProgressData(data.project.config);
            }
        });
        
        // 进度管理器 -> 原有UI
        this.progressManager.on('phaseProgressUpdated', (data) => {
            // 更新原有的进度显示
            this.updateOriginalProgressDisplay(data);
        });
    }

    /**
     * 设置全局事件监听器
     */
    setupGlobalEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // 页面卸载清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * 窗口大小变化处理
     */
    onWindowResize() {
        if (this.sceneManager) {
            const camera = this.sceneManager.getCamera();
            const renderer = this.sceneManager.getRenderer();
            
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
    }

    /**
     * 键盘快捷键处理
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + S: 保存项目
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
            event.preventDefault();
            this.saveCurrentProject();
        }
        
        // Ctrl/Cmd + O: 打开项目
        if ((event.ctrlKey || event.metaKey) && event.key === 'o') {
            event.preventDefault();
            this.showProjectSelector();
        }
        
        // 空格键: 播放/暂停进度
        if (event.key === ' ' && !event.target.matches('input, textarea, select')) {
            event.preventDefault();
            if (this.progressUI) {
                this.progressUI.togglePlayback();
            }
        }
        
        // 数字键1-9: 快速切换阶段
        if (event.key >= '1' && event.key <= '9') {
            const phaseIndex = parseInt(event.key) - 1;
            if (this.currentProject && this.currentProject.config.phases[phaseIndex]) {
                const phase = this.currentProject.config.phases[phaseIndex];
                this.progressUI.selectPhase(phase.id);
            }
        }
    }

    /**
     * 保存当前项目
     */
    async saveCurrentProject() {
        if (!this.currentProject) {
            console.warn('⚠️ 没有当前项目可保存');
            return;
        }
        
        try {
            // 获取当前状态
            const currentState = {
                camera: {
                    position: this.sceneManager.getCamera().position.toArray(),
                    target: this.sceneManager.controls.getOrbitControls().target.toArray()
                },
                progress: this.progressManager.getProgressData(),
                settings: this.sceneManager.settings,
                timestamp: Date.now()
            };
            
            // 更新项目配置
            Object.assign(this.currentProject.config, currentState);
            
            // 保存配置
            await this.projectManager.saveProjectConfig(this.currentProject.config);
            
            if (this.originalUI) {
                this.originalUI.showNotification('success', '项目保存', '当前项目状态已保存');
            }
            
        } catch (error) {
            console.error('❌ 保存项目失败:', error);
            if (this.originalUI) {
                this.originalUI.showNotification('error', '保存失败', '无法保存项目状态');
            }
        }
    }

    /**
     * 显示项目选择器
     */
    showProjectSelector() {
        // 聚焦到项目下拉框
        if (this.projectUI && this.projectUI.elements.projectDropdown) {
            this.projectUI.elements.projectDropdown.focus();
        }
    }

    /**
     * 更新原有进度显示
     */
    updateOriginalProgressDisplay(data) {
        // 更新右侧面板的原有进度显示
        const progressItems = document.querySelectorAll('.progress-item');
        
        // 简单映射到原有的进度项
        if (progressItems.length > 0) {
            const phaseIndex = this.currentProject.config.phases.findIndex(p => p.id === data.phaseId);
            if (phaseIndex >= 0 && phaseIndex < progressItems.length) {
                const progressItem = progressItems[phaseIndex];
                const progressFill = progressItem.querySelector('.progress-fill');
                const progressText = progressItem.querySelector('.progress-text');
                
                if (progressFill && progressText) {
                    progressFill.style.width = `${data.newProgress}%`;
                    progressText.textContent = `${data.newProgress}%`;
                }
            }
        }
    }

    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        const monitor = () => {
            this.performanceMonitor.frameCount++;
            const now = performance.now();
            
            if (now >= this.performanceMonitor.lastTime + 1000) {
                this.performanceMonitor.fps = Math.round(
                    (this.performanceMonitor.frameCount * 1000) / (now - this.performanceMonitor.lastTime)
                );
                
                // 更新FPS显示
                const fpsElement = document.getElementById('fps-counter');
                if (fpsElement) {
                    fpsElement.textContent = `FPS: ${this.performanceMonitor.fps}`;
                }
                
                this.performanceMonitor.frameCount = 0;
                this.performanceMonitor.lastTime = now;
            }
            
            if (this.isRunning) {
                requestAnimationFrame(monitor);
            }
        };
        
        monitor();
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(progress, message) {
        const progressBar = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('.loading-text');
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        
        if (loadingText) {
            loadingText.textContent = message;
        }
        
        console.log(`📊 加载进度: ${progress}% - ${message}`);
    }

    /**
     * 显示兼容性错误
     */
    showCompatibilityError() {
        document.body.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                        background: #2c3e50; color: white; display: flex;
                        align-items: center; justify-content: center; z-index: 10000;">
                <div style="text-align: center; max-width: 600px; padding: 40px;">
                    <h2>❌ 浏览器兼容性问题</h2>
                    <p>您的浏览器不支持运行此应用程序所需的功能。</p>
                    <p>请使用现代浏览器（Chrome 90+、Firefox 88+、Safari 14+、Edge 90+）。</p>
                </div>
            </div>
        `;
    }

    /**
     * 显示初始化错误
     */
    showInitializationError(error) {
        document.body.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                        background: #2c3e50; color: white; display: flex;
                        align-items: center; justify-content: center; z-index: 10000;">
                <div style="text-align: center; max-width: 600px; padding: 40px;">
                    <h2>❌ 系统初始化失败</h2>
                    <p>系统启动过程中发生错误：</p>
                    <p style="color: #e74c3c; margin: 20px 0;">${error.message}</p>
                    <button onclick="location.reload()"
                            style="margin-top: 20px; padding: 10px 20px;
                                   background: #3498db; color: white; border: none;
                                   border-radius: 5px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 获取系统信息
     */
    getSystemInfo() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            currentProject: this.currentProject ? this.currentProject.name : null,
            performance: this.performanceMonitor,
            loadedModels: this.sceneManager ? this.sceneManager.getLoadedModelsInfo() : [],
            memoryUsage: this.getMemoryUsage()
        };
    }

    /**
     * 获取内存使用情况
     */
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: `${(performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
                total: `${(performance.memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
                limit: `${(performance.memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
            };
        }
        return null;
    }

    /**
     * 暂停应用程序
     */
    pause() {
        this.isRunning = false;
        console.log('⏸️ 应用程序已暂停');
    }

    /**
     * 恢复应用程序
     */
    resume() {
        this.isRunning = true;
        this.startPerformanceMonitoring();
        console.log('▶️ 应用程序已恢复');
    }

    /**
     * 重启应用程序
     */
    restart() {
        this.cleanup();
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    /**
     * 清理资源
     */
    cleanup() {
        console.log('🧹 清理应用程序资源...');
        
        this.isRunning = false;
        
        // 清理管理器
        if (this.sceneManager) {
            this.sceneManager.cleanup();
        }
        if (this.projectManager) {
            this.projectManager.cleanup();
        }
        
        // 清理UI
        if (this.projectUI) {
            this.projectUI.cleanup();
        }
        if (this.progressUI) {
            this.progressUI.cleanup();
        }
        if (this.originalUI) {
            this.originalUI.cleanup();
        }
        
        console.log('✅ 应用程序资源清理完成');
    }
}

// 全局变量
let enhancedBridgeApp = null;

// 等待DOM和Three.js加载完成后启动应用程序
function startEnhancedApp() {
    // 检查所有必要条件
    const threeLoaded = typeof THREE !== 'undefined';
    const extensionsLoaded = window.threeExtensionsLoaded || false;
    const domReady = document.readyState === 'complete';

    console.log(`🔍 启动条件检查: Three.js=${threeLoaded}, 扩展=${extensionsLoaded}, DOM=${domReady}`);

    if (threeLoaded && extensionsLoaded && domReady) {
        console.log('🚀 所有条件满足，启动增强版应用程序...');
        enhancedBridgeApp = new EnhancedBridgeApp();
    } else {
        // 等待条件满足
        setTimeout(startEnhancedApp, 200);
    }
}

// 启动应用程序
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startEnhancedApp);
} else {
    startEnhancedApp();
}

// 导出到全局作用域（用于调试）
window.EnhancedBridgeApp = EnhancedBridgeApp;
window.getEnhancedBridgeApp = () => enhancedBridgeApp;

// 开发模式调试功能
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.debugEnhancedBridge = {
        getSystemInfo: () => enhancedBridgeApp ? enhancedBridgeApp.getSystemInfo() : null,
        restart: () => enhancedBridgeApp ? enhancedBridgeApp.restart() : null,
        pause: () => enhancedBridgeApp ? enhancedBridgeApp.pause() : null,
        resume: () => enhancedBridgeApp ? enhancedBridgeApp.resume() : null,
        getCurrentProject: () => enhancedBridgeApp ? enhancedBridgeApp.currentProject : null
    };
    
    console.log('🔧 增强版调试模式已启用，使用 window.debugEnhancedBridge 访问调试功能');
}
