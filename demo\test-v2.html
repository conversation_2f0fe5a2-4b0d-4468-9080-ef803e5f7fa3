<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2架构测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .log-container {
            background: #34495e;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #27ae60; }
        .status.error { background: #e74c3c; }
        .status.warning { background: #f39c12; }
        .status.info { background: #3498db; }
        .test-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🌉 路桥演示系统 V2 - 架构测试</h1>
        
        <div class="status info">
            <strong>测试目的:</strong> 验证新架构的服务初始化和事件系统
        </div>
        
        <div>
            <button class="test-btn" onclick="testBasicInitialization()">测试基础初始化</button>
            <button class="test-btn" onclick="testServiceRegistration()">测试服务注册</button>
            <button class="test-btn" onclick="testEventSystem()">测试事件系统</button>
            <button class="test-btn" onclick="clearLog()">清空日志</button>
        </div>
        
        <div id="log-container" class="log-container">
            <div>日志将在这里显示...</div>
        </div>
        
        <div id="status-container"></div>
    </div>

    <!-- Three.js 核心库 -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    
    <!-- Three.js 扩展 -->
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/DRACOLoader.js"></script>
    
    <!-- 标记Three.js扩展已加载 -->
    <script>
        window.threeExtensionsLoaded = true;
    </script>
    
    <!-- 新架构核心 -->
    <script src="js/core/application-core.js"></script>
    
    <!-- 服务层 -->
    <script src="js/services/project-service.js"></script>
    <script src="js/services/model-service.js"></script>
    <script src="js/services/scene-service.js"></script>
    <script src="js/services/progress-service.js"></script>
    
    <script>
        let testCore = null;
        
        function log(message, type = 'info') {
            const container = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #95a5a6;">[${timestamp}]</span> ${message}`;
            
            switch(type) {
                case 'error':
                    logEntry.style.color = '#e74c3c';
                    break;
                case 'success':
                    logEntry.style.color = '#2ecc71';
                    break;
                case 'warning':
                    logEntry.style.color = '#f39c12';
                    break;
                default:
                    logEntry.style.color = '#ecf0f1';
            }
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
            
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.innerHTML = `<strong>${type.toUpperCase()}:</strong> ${message}`;
            container.appendChild(status);
            
            setTimeout(() => {
                status.remove();
            }, 5000);
        }
        
        function clearLog() {
            document.getElementById('log-container').innerHTML = '<div>日志已清空...</div>';
        }
        
        async function testBasicInitialization() {
            log('🚀 开始测试基础初始化', 'info');
            
            try {
                // 测试 ApplicationCore 创建
                testCore = new ApplicationCore();
                log('✅ ApplicationCore 创建成功', 'success');
                
                // 测试事件总线
                if (testCore.eventBus) {
                    log('✅ 事件总线已初始化', 'success');
                } else {
                    log('❌ 事件总线初始化失败', 'error');
                }
                
                // 测试配置管理器
                if (testCore.config) {
                    log('✅ 配置管理器已初始化', 'success');
                } else {
                    log('❌ 配置管理器初始化失败', 'error');
                }
                
                showStatus('基础初始化测试完成', 'success');
                
            } catch (error) {
                log(`❌ 基础初始化失败: ${error.message}`, 'error');
                showStatus('基础初始化测试失败', 'error');
            }
        }
        
        async function testServiceRegistration() {
            log('📦 开始测试服务注册', 'info');
            
            try {
                if (!testCore) {
                    await testBasicInitialization();
                }
                
                // 注册测试服务
                log('正在注册服务...', 'info');
                
                testCore.registerService('project', ProjectService);
                log('✅ ProjectService 注册成功', 'success');
                
                testCore.registerService('model', ModelService);
                log('✅ ModelService 注册成功', 'success');
                
                testCore.registerService('scene', SceneService, []);
                log('✅ SceneService 注册成功', 'success');
                
                testCore.registerService('progress', ProgressService);
                log('✅ ProgressService 注册成功', 'success');
                
                // 测试服务获取
                log('测试服务实例化...', 'info');
                
                const projectService = await testCore.getService('project');
                log('✅ ProjectService 实例化成功', 'success');
                
                const modelService = await testCore.getService('model');
                log('✅ ModelService 实例化成功', 'success');
                
                const sceneService = await testCore.getService('scene');
                log('✅ SceneService 实例化成功', 'success');
                
                const progressService = await testCore.getService('progress');
                log('✅ ProgressService 实例化成功', 'success');
                
                showStatus('服务注册测试完成', 'success');
                
            } catch (error) {
                log(`❌ 服务注册失败: ${error.message}`, 'error');
                console.error('详细错误:', error);
                showStatus('服务注册测试失败', 'error');
            }
        }
        
        async function testEventSystem() {
            log('📡 开始测试事件系统', 'info');
            
            try {
                if (!testCore) {
                    await testServiceRegistration();
                }
                
                // 测试事件监听和触发
                let eventReceived = false;
                
                testCore.eventBus.on('test:event', (data) => {
                    eventReceived = true;
                    log(`✅ 事件接收成功: ${data.message}`, 'success');
                });
                
                testCore.eventBus.emit('test:event', { message: 'Hello from event system!' });
                
                if (eventReceived) {
                    log('✅ 事件系统测试通过', 'success');
                    showStatus('事件系统测试完成', 'success');
                } else {
                    log('❌ 事件系统测试失败', 'error');
                    showStatus('事件系统测试失败', 'error');
                }
                
                // 测试系统状态
                const state = testCore.getState();
                log(`📊 系统状态: ${state.state}`, 'info');
                log(`📦 已注册服务: ${state.services.join(', ')}`, 'info');
                
            } catch (error) {
                log(`❌ 事件系统测试失败: ${error.message}`, 'error');
                showStatus('事件系统测试失败', 'error');
            }
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('DOMContentLoaded', () => {
            log('🌐 测试页面已加载', 'success');
            log('点击上方按钮开始测试各个功能模块', 'info');
            showStatus('测试页面准备就绪', 'info');
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            log(`❌ 全局错误: ${event.error.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ 未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>