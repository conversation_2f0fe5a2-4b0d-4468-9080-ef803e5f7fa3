# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a 3D Bridge and Highway Construction Visualization System built with Three.js. It's a web-based application that demonstrates construction phases for infrastructure projects through interactive 3D models.

## Development Commands

### Local Development Server
```bash
# Start local HTTP server (required for loading glTF models)
python -m http.server 8000

# Alternative using Node.js
npx http-server -p 8000
```

### Access the Application
- **Enhanced version**: `http://localhost:8000/enhanced-index.html` (recommended)
- **Original version**: `http://localhost:8000/index.html`
- **Test versions**: Various test HTML files for debugging

## Architecture

### Core Application Structure
- **Main Entry**: `enhanced-index.html` - Primary application page with enhanced features
- **Main App**: `js/enhanced-app.js` - Core application orchestrator (`EnhancedBridgeApp` class)

### Manager Pattern
The application uses a manager-based architecture:
- **ProjectManager** (`js/managers/project-manager.js`) - Handles project loading, switching, and configuration
- **EnhancedBridgeScene** (`js/managers/enhanced-scene.js`) - Scene management and Three.js setup
- **GLTFModelLoader** (`js/managers/model-loader.js`) - Model loading with caching and progress tracking
- **ProgressManager** (`js/managers/progress-manager.js`) - Construction phase and timeline management
- **ToolManager** (`js/managers/tool-manager.js`) - Tools coordination (measurement, etc.)

### UI Components
- **ProjectUI** (`js/ui/project-ui.js`) - Project selection and model management interface
- **ProgressUI** (`js/ui/progress-ui.js`) - Construction progress and phase control interface

### Tools System
- **MeasurementTool** (`js/tools/measurement-tool.js`) - Distance, area, volume, and angle measurements

## Project Configuration

### Project Structure
Projects are stored in `projects/` directory:
```
projects/
├── bridge-demo/           # Bridge construction project
│   ├── config.json       # Project configuration
│   └── models/           # glTF model files
└── highway-project/      # Highway construction project
    ├── config.json
    └── models/
```

### Configuration Format
Each project has a `config.json` with:
- **Basic info**: name, description, type (bridge/highway)
- **Specifications**: dimensions, design parameters
- **Phases**: Construction phases with progress, models, and tasks
- **Camera/Lighting**: 3D scene settings
- **Equipment**: Construction equipment positioning
- **Viewpoints**: Predefined camera positions

## Key Technologies

### 3D Graphics
- **Three.js**: Core 3D engine (loaded via CDN)
- **glTF 2.0**: 3D model format with Draco compression support
- **WebGL**: Hardware-accelerated 3D rendering

### Browser Compatibility
- Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- Requires WebGL support

## Development Guidelines

### Working with Models
- Models must be in glTF 2.0 format (.gltf or .glb)
- Place models in `projects/{project-name}/models/` directory
- Reference models in project config.json phases
- Use Draco compression for optimization

### Adding New Projects
1. Create project directory in `projects/`
2. Add `config.json` following existing project structure
3. Add glTF models to `models/` subdirectory
4. Project will appear automatically in project selector

### Modifying Construction Phases
- Edit `phases` array in project config.json
- Each phase needs: id, name, progress, color, models, tasks
- Progress values: 0-100 (percentage complete)
- Colors: hex values for UI theming

### Performance Considerations
- glTF models with Draco compression are preferred
- Model caching is implemented to avoid reloading
- Monitor performance via built-in FPS counter
- Large models (>50MB) may impact loading times

## Debugging

### Debug Console Access
```javascript
// Get system information
window.debugEnhancedBridge.getSystemInfo()

// Control system
window.debugEnhancedBridge.restart()
window.debugEnhancedBridge.pause()
window.debugEnhancedBridge.resume()

// Get current project
window.debugEnhancedBridge.getCurrentProject()
```

### Common Issues
- **Models not loading**: Check file paths and ensure local server is running
- **Performance issues**: Reduce model complexity or disable shadows
- **Browser compatibility**: Ensure WebGL is supported and enabled

## File Organization

### Legacy Files (Still Used)
- `js/main.js`, `js/scene.js`, `js/ui.js`, `js/controls.js` - Original system components
- `js/materials.js`, `js/geometry.js` - Material and geometry utilities
- Various HTML test files for debugging specific features

### Styling
- `styles/enhanced-ui.css` - Enhanced UI styling
- Responsive design with CSS Grid and Flexbox
- Dark theme optimized for 3D visualization