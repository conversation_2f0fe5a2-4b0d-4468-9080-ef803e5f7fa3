/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: #1a1a1a;
    color: #ffffff;
    overflow: hidden;
    user-select: none;
}

/* 主容器 */
#container {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 3D画布 */
#three-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    cursor: grab;
}

#three-canvas:active {
    cursor: grabbing;
}

/* 顶部导航栏 */
.top-nav {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-bottom: 2px solid #3498db;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.nav-left h1 {
    font-size: 20px;
    font-weight: 500;
    color: #ecf0f1;
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-left h1 i {
    color: #3498db;
    font-size: 24px;
}

.nav-center {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.project-name {
    font-size: 16px;
    font-weight: 500;
    color: #ecf0f1;
}

.project-status {
    font-size: 12px;
    color: #2ecc71;
    background: rgba(46, 204, 113, 0.2);
    padding: 2px 8px;
    border-radius: 10px;
    margin-top: 2px;
}

.nav-right {
    display: flex;
    gap: 10px;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #3498db;
}

/* 侧边面板通用样式 */
.left-panel,
.right-panel {
    position: absolute;
    top: 60px;
    width: 280px;
    bottom: 40px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 900;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #3498db #2c3e50;
}

.left-panel {
    left: 0;
    border-right: 2px solid #3498db;
}

.right-panel {
    right: 0;
    border-left: 2px solid #3498db;
}

/* 自定义滚动条 */
.left-panel::-webkit-scrollbar,
.right-panel::-webkit-scrollbar {
    width: 6px;
}

.left-panel::-webkit-scrollbar-track,
.right-panel::-webkit-scrollbar-track {
    background: #2c3e50;
}

.left-panel::-webkit-scrollbar-thumb,
.right-panel::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 3px;
}

/* 面板区块 */
.panel-section {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-section:last-child {
    border-bottom: none;
}

.panel-section h3 {
    font-size: 14px;
    font-weight: 500;
    color: #3498db;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 视图控制按钮 */
.view-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.view-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
    text-align: left;
}

.view-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
    transform: translateX(2px);
}

.view-btn.active {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border-color: #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.view-btn i {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

/* 图层控制 */
.layer-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.layer-item {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 8px 0;
    transition: all 0.3s ease;
}

.layer-item:hover {
    color: #3498db;
}

.layer-item input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.layer-item input[type="checkbox"]:checked + .checkmark {
    background: #3498db;
    border-color: #3498db;
}

.layer-item input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 渲染控制 */
.render-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.control-group label {
    font-size: 12px;
    color: #bdc3c7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-group input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    outline: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.control-group select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
}

/* 底部状态栏 */
.bottom-status {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 40px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-top: 2px solid #3498db;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    z-index: 1000;
    font-size: 12px;
}

.status-left,
.status-right {
    display: flex;
    gap: 20px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #bdc3c7;
}

.status-item i {
    color: #3498db;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .left-panel,
    .right-panel {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .left-panel,
    .right-panel {
        width: 100%;
        height: 50%;
        position: fixed;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .right-panel {
        transform: translateX(100%);
    }
    
    .left-panel.active,
    .right-panel.active {
        transform: translateX(0);
    }
}
