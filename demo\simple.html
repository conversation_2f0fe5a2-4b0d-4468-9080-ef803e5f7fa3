<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路桥数字孪生系统 - 简化版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #2c3e50;
            color: white;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
        }
        
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 2px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background: #2980b9;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 200;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        
        <div id="loading">
            <div class="spinner"></div>
            <h2>路桥数字孪生系统</h2>
            <p id="loading-text">正在初始化...</p>
        </div>
        
        <div id="info" style="display: none;">
            <h3>🌉 路桥数字孪生系统</h3>
            <p>浏览器: <span id="browser-info"></span></p>
            <p>WebGL: <span id="webgl-info"></span></p>
            <p>Three.js: <span id="threejs-info"></span></p>
            <p>FPS: <span id="fps">0</span></p>
        </div>
        
        <div id="controls" style="display: none;">
            <h4>视角控制</h4>
            <button onclick="setView('overview')">总览</button>
            <button onclick="setView('bridge')">桥梁</button>
            <button onclick="setView('road')">道路</button>
            <br>
            <h4>功能</h4>
            <button onclick="takeScreenshot()">截图</button>
            <button onclick="toggleWireframe()">线框</button>
            <button onclick="resetCamera()">重置</button>
        </div>
    </div>

    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r158/controls/OrbitControls.js"></script>

    <script>
        // 全局变量
        let scene, camera, renderer, controls;
        let bridge, isWireframe = false;
        let frameCount = 0, lastTime = 0, fps = 0;

        // 更新加载文本
        function updateLoading(text) {
            document.getElementById('loading-text').textContent = text;
        }

        // 隐藏加载界面
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('info').style.display = 'block';
            document.getElementById('controls').style.display = 'block';
        }

        // 显示错误
        function showError(message) {
            document.getElementById('loading').innerHTML = `
                <h2 style="color: #e74c3c;">❌ 启动失败</h2>
                <p>${message}</p>
                <button onclick="location.reload()">重新加载</button>
            `;
        }

        // 检查兼容性
        function checkCompatibility() {
            updateLoading('检查浏览器兼容性...');
            
            // 检查WebGL
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (!gl) {
                    throw new Error('WebGL不受支持');
                }
                document.getElementById('webgl-info').textContent = '✅ 支持';
            } catch (e) {
                document.getElementById('webgl-info').textContent = '❌ 不支持';
                showError('您的浏览器不支持WebGL，无法运行3D应用程序。请更新浏览器或启用硬件加速。');
                return false;
            }

            // 检查Three.js
            if (typeof THREE === 'undefined') {
                showError('Three.js库加载失败，请检查网络连接。');
                return false;
            }
            document.getElementById('threejs-info').textContent = `✅ r${THREE.REVISION}`;

            // 浏览器信息
            const userAgent = navigator.userAgent;
            let browser = 'Unknown';
            if (userAgent.includes('Chrome')) browser = 'Chrome';
            else if (userAgent.includes('Firefox')) browser = 'Firefox';
            else if (userAgent.includes('Safari')) browser = 'Safari';
            else if (userAgent.includes('Edge')) browser = 'Edge';
            document.getElementById('browser-info').textContent = browser;

            return true;
        }

        // 初始化Three.js
        function initThree() {
            updateLoading('初始化3D引擎...');

            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x87ceeb);
            scene.fog = new THREE.Fog(0x87ceeb, 1000, 5000);

            // 创建相机
            camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 10000);
            camera.position.set(-800, 200, 800);

            // 创建渲染器
            const canvas = document.getElementById('canvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            // 创建控制器
            controls = new THREE.OrbitControls(camera, canvas);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.target.set(0, 50, 0);
            controls.update();
        }

        // 创建光照
        function createLights() {
            updateLoading('设置光照系统...');

            // 环境光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
            scene.add(ambientLight);

            // 方向光
            const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            directionalLight.position.set(1000, 1000, 500);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            directionalLight.shadow.camera.near = 0.5;
            directionalLight.shadow.camera.far = 3000;
            directionalLight.shadow.camera.left = -1000;
            directionalLight.shadow.camera.right = 1000;
            directionalLight.shadow.camera.top = 1000;
            directionalLight.shadow.camera.bottom = -1000;
            scene.add(directionalLight);
        }

        // 创建简单的桥梁模型
        function createBridge() {
            updateLoading('创建桥梁模型...');

            bridge = new THREE.Group();

            // 创建材质
            const concreteMaterial = new THREE.MeshLambertMaterial({ color: 0xc0c0c0 });
            const steelMaterial = new THREE.MeshLambertMaterial({ color: 0x708090 });
            const asphaltMaterial = new THREE.MeshLambertMaterial({ color: 0x2f2f2f });

            // 桥墩
            for (let i = -2; i <= 2; i++) {
                const pierGeometry = new THREE.CylinderGeometry(8, 10, 60, 12);
                const pier = new THREE.Mesh(pierGeometry, concreteMaterial);
                pier.position.set(i * 200, 30, 0);
                pier.castShadow = true;
                pier.receiveShadow = true;
                bridge.add(pier);
            }

            // 主梁
            const beamGeometry = new THREE.BoxGeometry(1200, 6, 12);
            const beam = new THREE.Mesh(beamGeometry, concreteMaterial);
            beam.position.set(0, 65, 0);
            beam.castShadow = true;
            beam.receiveShadow = true;
            bridge.add(beam);

            // 桥面
            const deckGeometry = new THREE.BoxGeometry(1200, 0.3, 32);
            const deck = new THREE.Mesh(deckGeometry, asphaltMaterial);
            deck.position.set(0, 68, 0);
            deck.castShadow = true;
            deck.receiveShadow = true;
            bridge.add(deck);

            // 护栏
            for (let side of [-16, 16]) {
                const railGeometry = new THREE.BoxGeometry(1200, 0.2, 0.8);
                const rail = new THREE.Mesh(railGeometry, steelMaterial);
                rail.position.set(0, 70, side);
                rail.castShadow = true;
                bridge.add(rail);
            }

            scene.add(bridge);
        }

        // 创建地面
        function createGround() {
            updateLoading('创建地形...');

            const groundGeometry = new THREE.PlaneGeometry(3000, 2000);
            const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x8b7355 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = -5;
            ground.receiveShadow = true;
            scene.add(ground);

            // 河流
            const riverGeometry = new THREE.PlaneGeometry(3000, 200);
            const riverMaterial = new THREE.MeshLambertMaterial({ 
                color: 0x006994, 
                transparent: true, 
                opacity: 0.8 
            });
            const river = new THREE.Mesh(riverGeometry, riverMaterial);
            river.rotation.x = -Math.PI / 2;
            river.position.y = -4;
            scene.add(river);
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            // 更新控制器
            controls.update();

            // 计算FPS
            frameCount++;
            const currentTime = performance.now();
            if (currentTime - lastTime >= 1000) {
                fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                document.getElementById('fps').textContent = fps;
                frameCount = 0;
                lastTime = currentTime;
            }

            // 渲染
            renderer.render(scene, camera);
        }

        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // 设置视角
        function setView(viewName) {
            const views = {
                overview: { position: [-800, 200, 800], target: [0, 50, 0] },
                bridge: { position: [0, 100, 300], target: [0, 70, 0] },
                road: { position: [-600, 50, 100], target: [-400, 30, 0] }
            };
            
            const view = views[viewName];
            if (view) {
                camera.position.set(...view.position);
                controls.target.set(...view.target);
                controls.update();
            }
        }

        // 截图
        function takeScreenshot() {
            const link = document.createElement('a');
            link.download = `bridge_screenshot_${Date.now()}.png`;
            link.href = renderer.domElement.toDataURL();
            link.click();
        }

        // 切换线框模式
        function toggleWireframe() {
            isWireframe = !isWireframe;
            bridge.traverse((child) => {
                if (child.isMesh) {
                    child.material.wireframe = isWireframe;
                }
            });
        }

        // 重置相机
        function resetCamera() {
            setView('overview');
        }

        // 主初始化函数
        async function init() {
            try {
                // 等待Three.js加载
                let attempts = 0;
                while (typeof THREE === 'undefined' && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }

                if (typeof THREE === 'undefined') {
                    throw new Error('Three.js加载超时');
                }

                // 检查兼容性
                if (!checkCompatibility()) {
                    return;
                }

                // 初始化
                initThree();
                createLights();
                createBridge();
                createGround();

                // 设置事件监听器
                window.addEventListener('resize', onWindowResize);

                // 开始动画
                animate();

                // 隐藏加载界面
                setTimeout(hideLoading, 1000);

                console.log('✅ 路桥数字孪生系统启动成功');

            } catch (error) {
                console.error('❌ 启动失败:', error);
                showError(error.message);
            }
        }

        // 页面加载完成后启动
        window.addEventListener('load', init);
    </script>
</body>
</html>
