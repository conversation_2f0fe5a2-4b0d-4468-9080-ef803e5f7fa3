# 增强版路桥修建演示系统 - 使用说明书

## 1. 系统概述

增强版路桥修建演示系统是基于Three.js的Web 3D应用程序，专门用于展示路桥建设项目的施工进度和过程。系统支持加载glTF模型，提供丰富的交互功能和可视化效果。

## 2. 快速开始

### 2.1 系统要求
- **浏览器**：Chrome 90+、Firefox 88+、Safari 14+、Edge 90+
- **网络**：需要互联网连接以加载Three.js库
- **硬件**：支持WebGL的显卡，建议4GB以上内存

### 2.2 启动系统
1. 打开 `enhanced-index.html` 文件
2. 等待系统加载完成（约10-30秒）
3. 在左侧面板选择项目
4. 开始使用各项功能

## 3. 主要功能

### 3.1 项目管理

#### 项目选择
- 在左侧面板顶部的"项目选择"区域
- 从下拉菜单中选择可用项目
- 点击刷新按钮更新项目列表
- 项目信息会自动显示在下方

#### 模型管理
- 查看当前项目的所有可用模型
- 单独加载/卸载特定模型
- 批量加载/卸载所有模型
- 实时显示加载进度

### 3.2 施工进度展示

#### 总体进度控制
- 右侧面板顶部显示总体进度
- 使用时间轴滑块控制整体进度
- 支持播放/暂停/重置/快进操作

#### 阶段管理
- 查看所有施工阶段
- 点击阶段标题切换到该阶段
- 使用阶段滑块精确控制单个阶段进度
- 不同阶段使用不同颜色标识

#### 进度可视化
- 已完成部分：完全不透明显示
- 进行中部分：半透明显示，带发光效果
- 未开始部分：高透明度显示
- 平滑的进度变化动画

### 3.3 3D场景交互

#### 视图控制
- **总览视图**：项目整体鸟瞰
- **桥梁视图**：聚焦桥梁结构
- **道路视图**：聚焦道路系统
- **施工视图**：施工现场视角

#### 相机操作
- **旋转**：鼠标左键拖拽
- **缩放**：鼠标滚轮
- **平移**：鼠标右键拖拽（如果支持）

#### 图层管理
- 独立控制各个图层的显示/隐藏
- 支持的图层：桥梁结构、道路系统、地形地貌、施工设备、基础结构

### 3.4 测量工具

#### 激活测量
1. 点击右侧"快速操作"中的"测量"按钮
2. 选择测量模式（距离/面积/体积/角度）
3. 在3D场景中点击测量点

#### 距离测量
1. 点击起始点
2. 点击结束点
3. 系统自动显示距离结果

#### 面积测量
1. 依次点击多边形的各个顶点
2. 按Enter键完成测量
3. 系统显示面积结果

#### 角度测量
1. 点击第一条边的起点
2. 点击角的顶点
3. 点击第二条边的终点
4. 系统显示角度结果

### 3.5 快速操作

#### 截图功能
- 点击"截图"按钮
- 自动保存当前3D视图为PNG图片
- 文件自动下载到默认下载文件夹

#### 录制功能
- 点击"录制"按钮开始/停止录制
- 支持录制3D场景动画（需要额外库支持）

#### 导出功能
- 支持多种导出格式
- 可导出场景模型、测量数据、项目报告

## 4. 键盘快捷键

- **Ctrl+S**：保存当前项目状态
- **Ctrl+O**：打开项目选择器
- **空格键**：播放/暂停进度动画
- **数字键1-9**：快速切换到对应施工阶段
- **F11**：切换全屏模式
- **Escape**：取消当前操作（如测量）
- **Enter**：完成当前操作（如面积测量）
- **Delete**：删除最后一个测量点

## 5. 高级功能

### 5.1 渲染设置
- **光照强度**：调整场景光照亮度
- **阴影质量**：选择阴影渲染质量（低/中/高）
- **雾效强度**：调整大气透视效果

### 5.2 性能监控
- 底部状态栏显示实时FPS
- 显示场景对象数量
- 显示内存使用情况（支持的浏览器）

### 5.3 项目配置
- 每个项目都有独立的配置文件
- 支持自定义相机位置、光照设置
- 可配置施工阶段和进度数据

## 6. 故障排除

### 6.1 常见问题

#### 系统无法启动
- 检查浏览器是否支持WebGL
- 检查网络连接是否正常
- 尝试刷新页面重新加载

#### 模型加载失败
- 检查模型文件是否存在
- 确认模型文件格式为glTF 2.0
- 检查文件路径是否正确

#### 性能问题
- 降低阴影质量设置
- 关闭不必要的图层
- 使用较小的模型文件

### 6.2 调试功能

在开发环境中，可以使用以下调试命令：

```javascript
// 获取系统信息
window.debugEnhancedBridge.getSystemInfo()

// 重启系统
window.debugEnhancedBridge.restart()

// 暂停/恢复系统
window.debugEnhancedBridge.pause()
window.debugEnhancedBridge.resume()

// 获取当前项目
window.debugEnhancedBridge.getCurrentProject()
```

## 7. 项目配置

### 7.1 添加新项目

1. 在 `projects/` 文件夹下创建新的项目文件夹
2. 创建 `config.json` 配置文件
3. 创建 `models/` 文件夹并放入glTF模型文件
4. 重启系统或刷新项目列表

### 7.2 配置文件格式

参考 `projects/bridge-demo/config.json` 的格式：

```json
{
  "name": "项目名称",
  "description": "项目描述",
  "phases": [
    {
      "id": "phase1",
      "name": "阶段名称",
      "progress": 0,
      "color": "#颜色代码",
      "models": ["模型文件名.gltf"]
    }
  ]
}
```

### 7.3 模型文件要求

- **格式**：glTF 2.0 (.gltf 或 .glb)
- **大小**：建议单个文件不超过50MB
- **坐标系**：Y轴向上，原点为项目中心
- **材质**：使用PBR材质以获得最佳效果

## 8. 技术支持

### 8.1 浏览器控制台
- 按F12打开开发者工具
- 查看控制台输出了解系统状态
- 错误信息会显示在控制台中

### 8.2 性能优化建议
- 定期清理不需要的模型
- 避免同时加载过多大型模型
- 在低性能设备上降低渲染质量

### 8.3 数据备份
- 定期保存项目状态（Ctrl+S）
- 导出重要的测量数据
- 备份项目配置文件

## 9. 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持项目管理和模型加载
- 实现施工进度可视化
- 添加基础测量工具

## 10. 联系方式

如有问题或建议，请联系开发团队。

---

*本说明书将随系统更新而持续完善。*
