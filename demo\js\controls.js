/**
 * 路桥数字孪生控制系统
 * 处理用户交互、相机控制和场景操作
 */

class BridgeControls {
    constructor(scene, camera, renderer) {
        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;
        this.canvas = renderer.domElement;
        
        this.orbitControls = null;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        
        this.selectedObject = null;
        this.hoveredObject = null;
        
        this.isMouseDown = false;
        this.isDragging = false;
        
        this.init();
    }

    /**
     * 初始化控制系统
     */
    init() {
        this.initOrbitControls();
        this.setupEventListeners();
        this.setupTooltips();
    }

    /**
     * 初始化轨道控制器
     */
    initOrbitControls() {
        try {
            // 检查OrbitControls是否可用
            if (typeof THREE.OrbitControls === 'undefined') {
                console.warn('⚠️ OrbitControls未加载，使用基础相机控制');
                this.initBasicControls();
                return;
            }

            this.orbitControls = new THREE.OrbitControls(this.camera, this.canvas);

            // 配置控制器参数
            this.orbitControls.enableDamping = true;
            this.orbitControls.dampingFactor = 0.05;
            this.orbitControls.screenSpacePanning = false;

            // 设置距离限制
            this.orbitControls.minDistance = 10;
            this.orbitControls.maxDistance = 2000;

            // 设置角度限制
            this.orbitControls.maxPolarAngle = Math.PI * 0.8;
            this.orbitControls.minPolarAngle = Math.PI * 0.1;

            // 设置平移功能（拖拽功能）
            this.orbitControls.enablePan = true;
            this.orbitControls.panSpeed = 1.2;
            this.orbitControls.keyPanSpeed = 7.0;

            // 设置缩放参数
            this.orbitControls.enableZoom = true;
            this.orbitControls.zoomSpeed = 1.0;

            // 设置旋转参数
            this.orbitControls.enableRotate = true;
            this.orbitControls.rotateSpeed = 0.5;

            // 设置鼠标按键映射（确保拖拽功能正常）
            this.orbitControls.mouseButtons = {
                LEFT: THREE.MOUSE.ROTATE,
                MIDDLE: THREE.MOUSE.DOLLY,
                RIGHT: THREE.MOUSE.PAN
            };

            // 设置目标点（适合gddwroad项目）
            this.orbitControls.target.set(0, 0, 0);

            // 更新控制器
            this.orbitControls.update();

            console.log('✅ OrbitControls初始化成功');
        } catch (error) {
            console.warn('⚠️ OrbitControls初始化失败:', error);
            this.initBasicControls();
        }
    }

    /**
     * 初始化基础控制器（OrbitControls的简单替代）
     */
    initBasicControls() {
        console.log('🔧 初始化基础相机控制');

        // 创建一个简单的控制器对象
        this.orbitControls = {
            target: new THREE.Vector3(0, 50, 0),
            enableDamping: true,
            dampingFactor: 0.05,
            update: function() {},
            dispose: function() {},
            reset: function() {}
        };

        // 添加基础的鼠标控制
        let isMouseDown = false;
        let mouseX = 0, mouseY = 0;
        let isRightClick = false;

        this.canvas.addEventListener('mousedown', (event) => {
            isMouseDown = true;
            isRightClick = event.button === 2;
            mouseX = event.clientX;
            mouseY = event.clientY;
            this.canvas.style.cursor = 'grabbing';
        });

        this.canvas.addEventListener('mousemove', (event) => {
            if (!isMouseDown) return;

            const deltaX = event.clientX - mouseX;
            const deltaY = event.clientY - mouseY;

            if (isRightClick) {
                // 右键平移
                const panSpeed = 0.002;
                const offset = new THREE.Vector3();
                offset.copy(this.camera.position).sub(this.orbitControls.target);
                const targetDistance = offset.length();

                const panLeft = new THREE.Vector3();
                panLeft.setFromMatrixColumn(this.camera.matrix, 0);
                panLeft.multiplyScalar(-deltaX * panSpeed * targetDistance);

                const panUp = new THREE.Vector3();
                panUp.setFromMatrixColumn(this.camera.matrix, 1);
                panUp.multiplyScalar(deltaY * panSpeed * targetDistance);

                this.camera.position.add(panLeft).add(panUp);
                this.orbitControls.target.add(panLeft).add(panUp);
            } else {
                // 左键旋转
                const spherical = new THREE.Spherical();
                spherical.setFromVector3(this.camera.position.clone().sub(this.orbitControls.target));
                spherical.theta -= deltaX * 0.01;
                spherical.phi += deltaY * 0.01;
                spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));

                this.camera.position.setFromSpherical(spherical).add(this.orbitControls.target);
            }

            this.camera.lookAt(this.orbitControls.target);

            mouseX = event.clientX;
            mouseY = event.clientY;
        });

        this.canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            this.canvas.style.cursor = 'grab';
        });

        // 缩放控制
        this.canvas.addEventListener('wheel', (event) => {
            event.preventDefault();
            const scale = event.deltaY > 0 ? 1.1 : 0.9;
            const offset = this.camera.position.clone().sub(this.orbitControls.target);
            offset.multiplyScalar(scale);

            // 限制缩放距离
            const distance = offset.length();
            if (distance > 10 && distance < 2000) {
                this.camera.position.copy(this.orbitControls.target).add(offset);
            }
        });

        console.log('✅ 基础相机控制初始化完成');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 鼠标事件
        this.canvas.addEventListener('mousemove', (event) => {
            this.onMouseMove(event);
        });
        
        this.canvas.addEventListener('mousedown', (event) => {
            this.onMouseDown(event);
        });
        
        this.canvas.addEventListener('mouseup', (event) => {
            this.onMouseUp(event);
        });
        
        this.canvas.addEventListener('click', (event) => {
            this.onClick(event);
        });
        
        this.canvas.addEventListener('dblclick', (event) => {
            this.onDoubleClick(event);
        });
        
        // 触摸事件（移动设备支持）
        this.canvas.addEventListener('touchstart', (event) => {
            this.onTouchStart(event);
        });
        
        this.canvas.addEventListener('touchmove', (event) => {
            this.onTouchMove(event);
        });
        
        this.canvas.addEventListener('touchend', (event) => {
            this.onTouchEnd(event);
        });
        
        // 键盘事件
        window.addEventListener('keydown', (event) => {
            this.onKeyDown(event);
        });
        
        // 阻止右键菜单
        this.canvas.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
    }

    /**
     * 设置工具提示系统
     */
    setupTooltips() {
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'tooltip';
        this.tooltip.style.position = 'absolute';
        this.tooltip.style.pointerEvents = 'none';
        this.tooltip.style.zIndex = '10000';
        document.body.appendChild(this.tooltip);
    }

    /**
     * 更新鼠标位置
     */
    updateMousePosition(event) {
        if (!this.canvas) {
            console.warn('⚠️ Canvas not available for mouse position update');
            return;
        }
        
        const rect = this.canvas.getBoundingClientRect();
        if (!rect) {
            console.warn('⚠️ Cannot get bounding client rect');
            return;
        }
        
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    /**
     * 鼠标移动事件
     */
    onMouseMove(event) {
        this.updateMousePosition(event);
        
        // 更新光线投射
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // 检测悬停对象
        this.checkHover();
        
        // 更新工具提示位置
        this.updateTooltipPosition(event);
        
        // 更新鼠标位置显示
        this.updateCursorPosition();
    }

    /**
     * 鼠标按下事件
     */
    onMouseDown(event) {
        this.isMouseDown = true;
        this.updateMousePosition(event);
    }

    /**
     * 鼠标释放事件
     */
    onMouseUp(event) {
        this.isMouseDown = false;
        this.isDragging = false;
    }

    /**
     * 鼠标点击事件
     */
    onClick(event) {
        if (this.isDragging) return;
        
        this.updateMousePosition(event);
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        // 检测点击的对象
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        
        if (intersects.length > 0) {
            const clickedObject = intersects[0].object;
            this.selectObject(clickedObject);
        } else {
            this.deselectObject();
        }
    }

    /**
     * 双击事件
     */
    onDoubleClick(event) {
        this.updateMousePosition(event);
        this.raycaster.setFromCamera(this.mouse, this.camera);
        
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        
        if (intersects.length > 0) {
            const targetObject = intersects[0].object;
            this.focusOnObject(targetObject);
        }
    }

    /**
     * 触摸开始事件
     */
    onTouchStart(event) {
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            this.updateMousePosition(touch);
        }
    }

    /**
     * 触摸移动事件
     */
    onTouchMove(event) {
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            this.updateMousePosition(touch);
            this.checkHover();
        }
    }

    /**
     * 触摸结束事件
     */
    onTouchEnd(event) {
        // 处理触摸结束逻辑
    }

    /**
     * 键盘按下事件
     */
    onKeyDown(event) {
        switch (event.code) {
            case 'Escape':
                this.deselectObject();
                break;
            case 'Delete':
                if (this.selectedObject) {
                    this.deleteObject(this.selectedObject);
                }
                break;
            case 'KeyW':
                this.moveCamera('forward');
                break;
            case 'KeyS':
                this.moveCamera('backward');
                break;
            case 'KeyA':
                this.moveCamera('left');
                break;
            case 'KeyD':
                this.moveCamera('right');
                break;
            case 'KeyQ':
                this.moveCamera('up');
                break;
            case 'KeyE':
                this.moveCamera('down');
                break;
            case 'Digit1':
                this.setCameraView('overview');
                break;
            case 'Digit2':
                this.setCameraView('bridge');
                break;
            case 'Digit3':
                this.setCameraView('road');
                break;
            case 'Digit4':
                this.setCameraView('construction');
                break;
        }
    }

    /**
     * 检测悬停对象
     */
    checkHover() {
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        
        if (intersects.length > 0) {
            const hoveredObject = intersects[0].object;
            
            if (this.hoveredObject !== hoveredObject) {
                // 移除之前的悬停效果
                if (this.hoveredObject) {
                    this.removeHoverEffect(this.hoveredObject);
                }
                
                // 添加新的悬停效果
                this.hoveredObject = hoveredObject;
                this.addHoverEffect(hoveredObject);
                this.showTooltip(hoveredObject);
            }
        } else {
            // 没有悬停对象
            if (this.hoveredObject) {
                this.removeHoverEffect(this.hoveredObject);
                this.hoveredObject = null;
                this.hideTooltip();
            }
        }
    }

    /**
     * 添加悬停效果
     */
    addHoverEffect(object) {
        if (object.material) {
            // 保存原始材质
            object.userData.originalMaterial = object.material;
            
            // 创建悬停材质
            const hoverMaterial = object.material.clone();
            hoverMaterial.emissive = new THREE.Color(0x444444);
            object.material = hoverMaterial;
        }
        
        // 改变鼠标样式
        this.canvas.style.cursor = 'pointer';
    }

    /**
     * 移除悬停效果
     */
    removeHoverEffect(object) {
        if (object.userData.originalMaterial) {
            object.material = object.userData.originalMaterial;
            delete object.userData.originalMaterial;
        }
        
        // 恢复鼠标样式
        this.canvas.style.cursor = 'grab';
    }

    /**
     * 选择对象
     */
    selectObject(object) {
        // 取消之前的选择
        this.deselectObject();
        
        this.selectedObject = object;
        
        // 添加选择效果
        this.addSelectionEffect(object);
        
        // 显示对象信息
        this.showObjectInfo(object);
        
        // 触发选择事件
        this.dispatchEvent('objectSelected', { object: object });
    }

    /**
     * 取消选择对象
     */
    deselectObject() {
        if (this.selectedObject) {
            this.removeSelectionEffect(this.selectedObject);
            this.selectedObject = null;
            this.hideObjectInfo();
            
            // 触发取消选择事件
            this.dispatchEvent('objectDeselected');
        }
    }

    /**
     * 添加选择效果
     */
    addSelectionEffect(object) {
        // 创建选择框
        const box = new THREE.BoxHelper(object, 0x00ff00);
        box.name = 'selectionBox';
        this.scene.add(box);
        
        object.userData.selectionBox = box;
    }

    /**
     * 移除选择效果
     */
    removeSelectionEffect(object) {
        if (object.userData.selectionBox) {
            this.scene.remove(object.userData.selectionBox);
            delete object.userData.selectionBox;
        }
    }

    /**
     * 聚焦到对象
     */
    focusOnObject(object) {
        // 计算对象的包围盒
        const box = new THREE.Box3().setFromObject(object);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        
        // 计算合适的相机距离
        const maxDim = Math.max(size.x, size.y, size.z);
        const distance = maxDim * 2;
        
        // 设置相机位置
        const direction = this.camera.position.clone().sub(center).normalize();
        this.camera.position.copy(center).add(direction.multiplyScalar(distance));
        
        // 设置控制器目标
        this.orbitControls.target.copy(center);
        this.orbitControls.update();
    }

    /**
     * 移动相机
     */
    moveCamera(direction) {
        const moveSpeed = 10;
        const cameraDirection = new THREE.Vector3();
        this.camera.getWorldDirection(cameraDirection);
        
        const right = new THREE.Vector3();
        right.crossVectors(cameraDirection, this.camera.up).normalize();
        
        switch (direction) {
            case 'forward':
                this.camera.position.add(cameraDirection.multiplyScalar(moveSpeed));
                break;
            case 'backward':
                this.camera.position.add(cameraDirection.multiplyScalar(-moveSpeed));
                break;
            case 'left':
                this.camera.position.add(right.multiplyScalar(-moveSpeed));
                break;
            case 'right':
                this.camera.position.add(right.multiplyScalar(moveSpeed));
                break;
            case 'up':
                this.camera.position.y += moveSpeed;
                break;
            case 'down':
                this.camera.position.y -= moveSpeed;
                break;
        }
        
        this.orbitControls.update();
    }

    /**
     * 设置相机视角
     */
    setCameraView(viewName) {
        const views = {
            overview: { position: [-800, 200, 800], target: [0, 50, 0] },
            bridge: { position: [0, 100, 300], target: [0, 70, 0] },
            road: { position: [-600, 50, 100], target: [-400, 30, 0] },
            construction: { position: [200, 80, 200], target: [200, 50, 200] }
        };
        
        const view = views[viewName];
        if (view) {
            // 平滑过渡到新视角
            this.animateCameraTo(view.position, view.target);
        }
    }

    /**
     * 相机动画过渡
     */
    animateCameraTo(targetPosition, targetLookAt, duration = 1000) {
        const startPosition = this.camera.position.clone();
        const startTarget = this.orbitControls.target.clone();
        
        const endPosition = new THREE.Vector3(...targetPosition);
        const endTarget = new THREE.Vector3(...targetLookAt);
        
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeProgress = this.easeInOutCubic(progress);
            
            // 插值相机位置
            this.camera.position.lerpVectors(startPosition, endPosition, easeProgress);
            this.orbitControls.target.lerpVectors(startTarget, endTarget, easeProgress);
            
            this.orbitControls.update();
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }

    /**
     * 缓动函数
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }

    /**
     * 显示工具提示
     */
    showTooltip(object) {
        const objectInfo = this.getObjectInfo(object);
        this.tooltip.innerHTML = objectInfo;
        this.tooltip.classList.add('show');
    }

    /**
     * 隐藏工具提示
     */
    hideTooltip() {
        this.tooltip.classList.remove('show');
    }

    /**
     * 更新工具提示位置
     */
    updateTooltipPosition(event) {
        this.tooltip.style.left = event.clientX + 10 + 'px';
        this.tooltip.style.top = event.clientY - 10 + 'px';
    }

    /**
     * 获取对象信息
     */
    getObjectInfo(object) {
        const parent = object.parent;
        const parentName = parent ? parent.name : '未知';
        
        return `
            <div><strong>对象:</strong> ${object.name || '未命名'}</div>
            <div><strong>类型:</strong> ${object.type}</div>
            <div><strong>父级:</strong> ${parentName}</div>
            <div><strong>材质:</strong> ${object.material ? object.material.type : '无'}</div>
        `;
    }

    /**
     * 显示对象详细信息
     */
    showObjectInfo(object) {
        // 在右侧面板显示详细信息
        const infoPanel = document.querySelector('.right-panel .panel-section:first-child');
        if (infoPanel) {
            const infoGrid = infoPanel.querySelector('.info-grid');
            if (infoGrid) {
                infoGrid.innerHTML = `
                    <div class="info-item">
                        <span class="label">对象名称</span>
                        <span class="value">${object.name || '未命名'}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">对象类型</span>
                        <span class="value">${object.type}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">位置 X</span>
                        <span class="value">${object.position.x.toFixed(2)}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">位置 Y</span>
                        <span class="value">${object.position.y.toFixed(2)}</span>
                    </div>
                    <div class="info-item">
                        <span class="label">位置 Z</span>
                        <span class="value">${object.position.z.toFixed(2)}</span>
                    </div>
                `;
            }
        }
    }

    /**
     * 隐藏对象信息
     */
    hideObjectInfo() {
        // 恢复默认信息显示
        const infoPanel = document.querySelector('.right-panel .panel-section:first-child');
        if (infoPanel) {
            const infoGrid = infoPanel.querySelector('.info-grid');
            if (infoGrid) {
                infoGrid.innerHTML = `
                    <div class="info-item">
                        <span class="label">桥梁长度</span>
                        <span class="value">1,200m</span>
                    </div>
                    <div class="info-item">
                        <span class="label">桥面宽度</span>
                        <span class="value">32m</span>
                    </div>
                    <div class="info-item">
                        <span class="label">主跨长度</span>
                        <span class="value">400m</span>
                    </div>
                    <div class="info-item">
                        <span class="label">桥墩数量</span>
                        <span class="value">8个</span>
                    </div>
                `;
            }
        }
    }

    /**
     * 更新鼠标位置显示
     */
    updateCursorPosition() {
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        
        if (intersects.length > 0) {
            const point = intersects[0].point;
            const cursorElement = document.getElementById('cursor-position');
            if (cursorElement) {
                cursorElement.textContent = 
                    `X: ${point.x.toFixed(1)}, Y: ${point.y.toFixed(1)}, Z: ${point.z.toFixed(1)}`;
            }
        }
    }

    /**
     * 删除对象
     */
    deleteObject(object) {
        if (object.parent) {
            object.parent.remove(object);
        }
        
        // 清理资源
        if (object.geometry) {
            object.geometry.dispose();
        }
        
        if (object.material) {
            if (Array.isArray(object.material)) {
                object.material.forEach(material => material.dispose());
            } else {
                object.material.dispose();
            }
        }
        
        this.deselectObject();
    }

    /**
     * 派发自定义事件
     */
    dispatchEvent(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        window.dispatchEvent(event);
    }

    /**
     * 更新控制器
     */
    update() {
        if (this.orbitControls) {
            this.orbitControls.update();
        }
    }

    /**
     * 获取轨道控制器
     */
    getOrbitControls() {
        return this.orbitControls;
    }

    /**
     * 销毁控制器
     */
    dispose() {
        if (this.orbitControls) {
            this.orbitControls.dispose();
        }
        
        if (this.tooltip && this.tooltip.parentNode) {
            this.tooltip.parentNode.removeChild(this.tooltip);
        }
    }
}
