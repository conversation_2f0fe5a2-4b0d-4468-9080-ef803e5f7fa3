/**
 * 路桥建设专用材质系统
 * 提供高质量的PBR材质，专门针对路桥工程领域
 */

class BridgeMaterials {
    constructor() {
        this.textureLoader = new THREE.TextureLoader();
        this.materials = {};
        this.initializeMaterials();
    }

    /**
     * 初始化所有材质
     */
    initializeMaterials() {
        // 混凝土材质
        this.materials.concrete = this.createConcreteMaterial();
        
        // 钢材材质
        this.materials.steel = this.createSteelMaterial();
        
        // 沥青路面材质
        this.materials.asphalt = this.createAsphaltMaterial();
        
        // 护栏材质
        this.materials.guardrail = this.createGuardrailMaterial();
        
        // 桥梁缆索材质
        this.materials.cable = this.createCableMaterial();
        
        // 地面材质
        this.materials.ground = this.createGroundMaterial();
        
        // 水面材质
        this.materials.water = this.createWaterMaterial();
        
        // 玻璃材质（用于建筑物）
        this.materials.glass = this.createGlassMaterial();
        
        // 标线材质
        this.materials.roadMarking = this.createRoadMarkingMaterial();
        
        // 施工设备材质
        this.materials.equipment = this.createEquipmentMaterial();
    }

    /**
     * 创建混凝土材质
     */
    createConcreteMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0xc0c0c0,
            roughness: 0.8,
            metalness: 0.1,
            normalScale: new THREE.Vector2(0.5, 0.5),
            // 添加程序化纹理
            map: this.createConcreteTexture(),
            normalMap: this.createConcreteNormalMap(),
            roughnessMap: this.createConcreteRoughnessMap()
        });
    }

    /**
     * 创建钢材材质
     */
    createSteelMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0x708090,
            roughness: 0.3,
            metalness: 0.9,
            envMapIntensity: 1.0,
            // 添加金属反射效果
            map: this.createSteelTexture(),
            normalMap: this.createSteelNormalMap(),
            metalnessMap: this.createSteelMetalnessMap()
        });
    }

    /**
     * 创建沥青路面材质
     */
    createAsphaltMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0x2f2f2f,
            roughness: 0.9,
            metalness: 0.0,
            normalScale: new THREE.Vector2(0.3, 0.3),
            map: this.createAsphaltTexture(),
            normalMap: this.createAsphaltNormalMap(),
            roughnessMap: this.createAsphaltRoughnessMap()
        });
    }

    /**
     * 创建护栏材质
     */
    createGuardrailMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.4,
            metalness: 0.7,
            envMapIntensity: 0.8
        });
    }

    /**
     * 创建缆索材质
     */
    createCableMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0x4a4a4a,
            roughness: 0.6,
            metalness: 0.8,
            envMapIntensity: 0.9
        });
    }

    /**
     * 创建地面材质
     */
    createGroundMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0x8b7355,
            roughness: 0.95,
            metalness: 0.0,
            map: this.createGroundTexture(),
            normalMap: this.createGroundNormalMap()
        });
    }

    /**
     * 创建水面材质
     */
    createWaterMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0x006994,
            roughness: 0.1,
            metalness: 0.0,
            transparent: true,
            opacity: 0.8,
            envMapIntensity: 1.0,
            normalScale: new THREE.Vector2(0.5, 0.5)
        });
    }

    /**
     * 创建玻璃材质
     */
    createGlassMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0x87ceeb,
            roughness: 0.0,
            metalness: 0.0,
            transparent: true,
            opacity: 0.3,
            envMapIntensity: 1.0
        });
    }

    /**
     * 创建道路标线材质
     */
    createRoadMarkingMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0xffffff,
            roughness: 0.7,
            metalness: 0.0,
            emissive: 0x222222,
            emissiveIntensity: 0.1
        });
    }

    /**
     * 创建施工设备材质
     */
    createEquipmentMaterial() {
        return new THREE.MeshStandardMaterial({
            color: 0xffa500,
            roughness: 0.6,
            metalness: 0.4,
            envMapIntensity: 0.7
        });
    }

    /**
     * 程序化生成混凝土纹理
     */
    createConcreteTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // 基础颜色
        ctx.fillStyle = '#c0c0c0';
        ctx.fillRect(0, 0, 512, 512);

        // 添加噪声和变化
        for (let i = 0; i < 1000; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 3 + 1;
            const brightness = Math.random() * 0.3 - 0.15;
            
            ctx.fillStyle = `rgb(${192 + brightness * 255}, ${192 + brightness * 255}, ${192 + brightness * 255})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        return texture;
    }

    /**
     * 程序化生成混凝土法线贴图
     */
    createConcreteNormalMap() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // 生成法线贴图
        ctx.fillStyle = '#8080ff';
        ctx.fillRect(0, 0, 512, 512);

        // 添加细节
        for (let i = 0; i < 500; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 2 + 1;
            const variation = Math.random() * 40 - 20;
            
            ctx.fillStyle = `rgb(${128 + variation}, ${128 + variation}, ${255})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        return texture;
    }

    /**
     * 程序化生成混凝土粗糙度贴图
     */
    createConcreteRoughnessMap() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // 基础粗糙度
        ctx.fillStyle = '#cccccc';
        ctx.fillRect(0, 0, 512, 512);

        // 添加变化
        for (let i = 0; i < 300; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 5 + 2;
            const roughness = Math.random() * 100 + 100;
            
            ctx.fillStyle = `rgb(${roughness}, ${roughness}, ${roughness})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(4, 4);
        return texture;
    }

    /**
     * 程序化生成钢材纹理
     */
    createSteelTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // 基础金属色
        ctx.fillStyle = '#708090';
        ctx.fillRect(0, 0, 256, 256);

        // 添加金属条纹
        for (let i = 0; i < 256; i += 2) {
            const brightness = Math.random() * 0.1 - 0.05;
            ctx.fillStyle = `rgb(${112 + brightness * 255}, ${128 + brightness * 255}, ${144 + brightness * 255})`;
            ctx.fillRect(0, i, 256, 1);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(2, 2);
        return texture;
    }

    /**
     * 程序化生成钢材法线贴图
     */
    createSteelNormalMap() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        ctx.fillStyle = '#8080ff';
        ctx.fillRect(0, 0, 256, 256);

        // 添加金属表面细节
        for (let i = 0; i < 256; i += 1) {
            const variation = Math.random() * 20 - 10;
            ctx.fillStyle = `rgb(${128 + variation}, ${128 + variation}, ${255})`;
            ctx.fillRect(0, i, 256, 1);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(2, 2);
        return texture;
    }

    /**
     * 程序化生成钢材金属度贴图
     */
    createSteelMetalnessMap() {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');

        // 高金属度
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, 256, 256);

        // 添加轻微变化
        for (let i = 0; i < 100; i++) {
            const x = Math.random() * 256;
            const y = Math.random() * 256;
            const size = Math.random() * 3 + 1;
            const metalness = Math.random() * 50 + 200;
            
            ctx.fillStyle = `rgb(${metalness}, ${metalness}, ${metalness})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(2, 2);
        return texture;
    }

    /**
     * 程序化生成沥青纹理
     */
    createAsphaltTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // 深色基础
        ctx.fillStyle = '#2f2f2f';
        ctx.fillRect(0, 0, 512, 512);

        // 添加沥青颗粒
        for (let i = 0; i < 2000; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 2 + 0.5;
            const brightness = Math.random() * 0.2 - 0.1;
            
            ctx.fillStyle = `rgb(${47 + brightness * 255}, ${47 + brightness * 255}, ${47 + brightness * 255})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(8, 8);
        return texture;
    }

    /**
     * 程序化生成沥青法线贴图
     */
    createAsphaltNormalMap() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        ctx.fillStyle = '#8080ff';
        ctx.fillRect(0, 0, 512, 512);

        // 添加表面细节
        for (let i = 0; i < 1000; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 3 + 1;
            const variation = Math.random() * 30 - 15;
            
            ctx.fillStyle = `rgb(${128 + variation}, ${128 + variation}, ${255})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(8, 8);
        return texture;
    }

    /**
     * 程序化生成沥青粗糙度贴图
     */
    createAsphaltRoughnessMap() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // 高粗糙度基础
        ctx.fillStyle = '#e0e0e0';
        ctx.fillRect(0, 0, 512, 512);

        // 添加变化
        for (let i = 0; i < 500; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 4 + 2;
            const roughness = Math.random() * 50 + 180;
            
            ctx.fillStyle = `rgb(${roughness}, ${roughness}, ${roughness})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(8, 8);
        return texture;
    }

    /**
     * 程序化生成地面纹理
     */
    createGroundTexture() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        // 土壤基色
        ctx.fillStyle = '#8b7355';
        ctx.fillRect(0, 0, 512, 512);

        // 添加土壤颗粒和变化
        for (let i = 0; i < 1500; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 4 + 1;
            const brightness = Math.random() * 0.3 - 0.15;
            
            ctx.fillStyle = `rgb(${139 + brightness * 255}, ${115 + brightness * 255}, ${85 + brightness * 255})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(16, 16);
        return texture;
    }

    /**
     * 程序化生成地面法线贴图
     */
    createGroundNormalMap() {
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');

        ctx.fillStyle = '#8080ff';
        ctx.fillRect(0, 0, 512, 512);

        // 添加地面起伏
        for (let i = 0; i < 800; i++) {
            const x = Math.random() * 512;
            const y = Math.random() * 512;
            const size = Math.random() * 6 + 2;
            const variation = Math.random() * 40 - 20;
            
            ctx.fillStyle = `rgb(${128 + variation}, ${128 + variation}, ${255})`;
            ctx.fillRect(x, y, size, size);
        }

        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        texture.repeat.set(16, 16);
        return texture;
    }

    /**
     * 获取材质
     */
    getMaterial(name) {
        return this.materials[name] || this.materials.concrete;
    }

    /**
     * 获取所有材质
     */
    getAllMaterials() {
        return this.materials;
    }

    /**
     * 更新材质属性
     */
    updateMaterial(name, properties) {
        if (this.materials[name]) {
            Object.assign(this.materials[name], properties);
        }
    }
}
