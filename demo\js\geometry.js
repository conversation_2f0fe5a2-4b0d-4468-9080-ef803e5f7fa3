/**
 * 路桥几何体建模系统
 * 创建专业的桥梁、道路和相关基础设施的3D模型
 */

class BridgeGeometry {
    constructor(materials) {
        this.materials = materials;
        this.models = {};
    }

    /**
     * 创建完整的桥梁系统
     */
    createBridgeSystem() {
        const bridgeGroup = new THREE.Group();
        bridgeGroup.name = 'BridgeSystem';

        // 创建桥墩
        const piers = this.createBridgePiers();
        bridgeGroup.add(piers);

        // 创建主梁
        const mainBeams = this.createMainBeams();
        bridgeGroup.add(mainBeams);

        // 创建桥面
        const deck = this.createBridgeDeck();
        bridgeGroup.add(deck);

        // 创建护栏
        const guardrails = this.createGuardrails();
        bridgeGroup.add(guardrails);

        // 创建缆索（如果是斜拉桥）
        const cables = this.createCables();
        bridgeGroup.add(cables);

        // 创建桥塔
        const towers = this.createBridgeTowers();
        bridgeGroup.add(towers);

        return bridgeGroup;
    }

    /**
     * 创建桥墩
     */
    createBridgePiers() {
        const piersGroup = new THREE.Group();
        piersGroup.name = 'BridgePiers';

        const pierPositions = [
            { x: -400, z: 0 },
            { x: -200, z: 0 },
            { x: 0, z: 0 },
            { x: 200, z: 0 },
            { x: 400, z: 0 }
        ];

        pierPositions.forEach((pos, index) => {
            const pier = this.createSinglePier(index);
            pier.position.set(pos.x, 0, pos.z);
            piersGroup.add(pier);
        });

        return piersGroup;
    }

    /**
     * 创建单个桥墩
     */
    createSinglePier(index) {
        const pierGroup = new THREE.Group();
        pierGroup.name = `Pier_${index}`;

        // 桥墩基础
        const foundationGeometry = new THREE.CylinderGeometry(12, 15, 8, 16);
        const foundation = new THREE.Mesh(foundationGeometry, this.materials.getMaterial('concrete'));
        foundation.position.y = 4;
        pierGroup.add(foundation);

        // 桥墩主体
        const pierHeight = index === 2 ? 80 : 60; // 中间桥墩更高
        const pierGeometry = new THREE.CylinderGeometry(8, 10, pierHeight, 12);
        const pierMesh = new THREE.Mesh(pierGeometry, this.materials.getMaterial('concrete'));
        pierMesh.position.y = 8 + pierHeight / 2;
        pierGroup.add(pierMesh);

        // 桥墩顶部承台
        const capGeometry = new THREE.BoxGeometry(20, 4, 16);
        const cap = new THREE.Mesh(capGeometry, this.materials.getMaterial('concrete'));
        cap.position.y = 8 + pierHeight + 2;
        pierGroup.add(cap);

        // 添加细节装饰
        this.addPierDetails(pierGroup, pierHeight);

        return pierGroup;
    }

    /**
     * 为桥墩添加细节
     */
    addPierDetails(pierGroup, height) {
        // 添加钢筋细节
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            const x = Math.cos(angle) * 9;
            const z = Math.sin(angle) * 9;
            
            const rebarGeometry = new THREE.CylinderGeometry(0.2, 0.2, height - 10, 8);
            const rebar = new THREE.Mesh(rebarGeometry, this.materials.getMaterial('steel'));
            rebar.position.set(x, 8 + height / 2, z);
            pierGroup.add(rebar);
        }
    }

    /**
     * 创建主梁
     */
    createMainBeams() {
        const beamsGroup = new THREE.Group();
        beamsGroup.name = 'MainBeams';

        // 创建连续梁段
        const spans = [
            { start: -600, end: -400, height: 65 },
            { start: -400, end: -200, height: 68 },
            { start: -200, end: 0, height: 70 },
            { start: 0, end: 200, height: 70 },
            { start: 200, end: 400, height: 68 },
            { start: 400, end: 600, height: 65 }
        ];

        spans.forEach((span, index) => {
            const beam = this.createBeamSpan(span.start, span.end, span.height);
            beamsGroup.add(beam);
        });

        return beamsGroup;
    }

    /**
     * 创建梁段
     */
    createBeamSpan(startX, endX, height) {
        const spanGroup = new THREE.Group();
        const length = endX - startX;
        const centerX = (startX + endX) / 2;

        // 主梁几何体 - 使用箱梁截面
        const beamGeometry = new THREE.BoxGeometry(length, 6, 12);
        const beam = new THREE.Mesh(beamGeometry, this.materials.getMaterial('concrete'));
        beam.position.set(centerX, height, 0);
        spanGroup.add(beam);

        // 添加预应力钢筋
        this.addPreStressedRebars(spanGroup, startX, endX, height);

        return spanGroup;
    }

    /**
     * 添加预应力钢筋
     */
    addPreStressedRebars(spanGroup, startX, endX, height) {
        const length = endX - startX;
        const centerX = (startX + endX) / 2;

        // 底部预应力钢筋
        for (let i = 0; i < 6; i++) {
            const rebarGeometry = new THREE.CylinderGeometry(0.3, 0.3, length, 8);
            const rebar = new THREE.Mesh(rebarGeometry, this.materials.getMaterial('steel'));
            rebar.rotation.z = Math.PI / 2;
            rebar.position.set(centerX, height - 2, -4 + i * 1.6);
            spanGroup.add(rebar);
        }
    }

    /**
     * 创建桥面
     */
    createBridgeDeck() {
        const deckGroup = new THREE.Group();
        deckGroup.name = 'BridgeDeck';

        // 主桥面
        const deckGeometry = new THREE.BoxGeometry(1200, 0.3, 32);
        const deck = new THREE.Mesh(deckGeometry, this.materials.getMaterial('asphalt'));
        deck.position.set(0, 73, 0);
        deckGroup.add(deck);

        // 人行道
        const sidewalkGeometry = new THREE.BoxGeometry(1200, 0.5, 3);
        const leftSidewalk = new THREE.Mesh(sidewalkGeometry, this.materials.getMaterial('concrete'));
        leftSidewalk.position.set(0, 73.5, -14.5);
        deckGroup.add(leftSidewalk);

        const rightSidewalk = new THREE.Mesh(sidewalkGeometry, this.materials.getMaterial('concrete'));
        rightSidewalk.position.set(0, 73.5, 14.5);
        deckGroup.add(rightSidewalk);

        // 道路标线
        this.addRoadMarkings(deckGroup);

        return deckGroup;
    }

    /**
     * 添加道路标线
     */
    addRoadMarkings(deckGroup) {
        // 中央分隔线
        const centerLineGeometry = new THREE.BoxGeometry(1200, 0.01, 0.3);
        const centerLine = new THREE.Mesh(centerLineGeometry, this.materials.getMaterial('roadMarking'));
        centerLine.position.set(0, 73.31, 0);
        deckGroup.add(centerLine);

        // 车道分隔线
        for (let i = -2; i <= 2; i++) {
            if (i !== 0) {
                const laneLineGeometry = new THREE.BoxGeometry(1200, 0.01, 0.2);
                const laneLine = new THREE.Mesh(laneLineGeometry, this.materials.getMaterial('roadMarking'));
                laneLine.position.set(0, 73.31, i * 4);
                deckGroup.add(laneLine);
            }
        }
    }

    /**
     * 创建护栏
     */
    createGuardrails() {
        const guardrailsGroup = new THREE.Group();
        guardrailsGroup.name = 'Guardrails';

        // 左侧护栏
        const leftGuardrail = this.createSingleGuardrail(-16);
        guardrailsGroup.add(leftGuardrail);

        // 右侧护栏
        const rightGuardrail = this.createSingleGuardrail(16);
        guardrailsGroup.add(rightGuardrail);

        return guardrailsGroup;
    }

    /**
     * 创建单侧护栏
     */
    createSingleGuardrail(zPosition) {
        const guardrailGroup = new THREE.Group();

        // 护栏立柱
        for (let x = -600; x <= 600; x += 20) {
            const postGeometry = new THREE.CylinderGeometry(0.3, 0.3, 2, 8);
            const post = new THREE.Mesh(postGeometry, this.materials.getMaterial('steel'));
            post.position.set(x, 74.5, zPosition);
            guardrailGroup.add(post);
        }

        // 护栏横梁
        const railGeometry = new THREE.BoxGeometry(1200, 0.2, 0.8);
        const rail = new THREE.Mesh(railGeometry, this.materials.getMaterial('guardrail'));
        rail.position.set(0, 75, zPosition);
        guardrailGroup.add(rail);

        return guardrailGroup;
    }

    /**
     * 创建缆索系统
     */
    createCables() {
        const cablesGroup = new THREE.Group();
        cablesGroup.name = 'Cables';

        // 主缆
        const mainCablePositions = [
            { x: -8, z: 0 },
            { x: 8, z: 0 }
        ];

        mainCablePositions.forEach(pos => {
            const cable = this.createMainCable(pos.x, pos.z);
            cablesGroup.add(cable);
        });

        // 斜拉索
        this.createStayCables(cablesGroup);

        return cablesGroup;
    }

    /**
     * 创建主缆
     */
    createMainCable(x, z) {
        const points = [];
        for (let i = -600; i <= 600; i += 10) {
            const y = 85 + Math.pow(i / 300, 2) * 15; // 抛物线形状
            points.push(new THREE.Vector3(i, y, z));
        }

        const curve = new THREE.CatmullRomCurve3(points);
        const tubeGeometry = new THREE.TubeGeometry(curve, 120, 0.5, 8, false);
        const cable = new THREE.Mesh(tubeGeometry, this.materials.getMaterial('cable'));
        cable.position.x = x;

        return cable;
    }

    /**
     * 创建斜拉索
     */
    createStayCables(cablesGroup) {
        const towerPositions = [-200, 0, 200];
        
        towerPositions.forEach(towerX => {
            // 从塔顶到桥面的斜拉索
            for (let i = -150; i <= 150; i += 30) {
                if (Math.abs(i) > 20) { // 避免在塔附近创建缆索
                    const cableGeometry = new THREE.CylinderGeometry(0.1, 0.1, 
                        Math.sqrt(Math.pow(i, 2) + Math.pow(25, 2)), 8);
                    
                    const cable = new THREE.Mesh(cableGeometry, this.materials.getMaterial('cable'));
                    
                    // 计算角度和位置
                    const angle = Math.atan2(25, i);
                    cable.rotation.z = -angle;
                    cable.position.set(towerX + i/2, 85 + 12.5, 0);
                    
                    cablesGroup.add(cable);
                }
            }
        });
    }

    /**
     * 创建桥塔
     */
    createBridgeTowers() {
        const towersGroup = new THREE.Group();
        towersGroup.name = 'BridgeTowers';

        const towerPositions = [-200, 0, 200];
        
        towerPositions.forEach((x, index) => {
            const tower = this.createSingleTower();
            tower.position.set(x, 0, 0);
            towersGroup.add(tower);
        });

        return towersGroup;
    }

    /**
     * 创建单个桥塔
     */
    createSingleTower() {
        const towerGroup = new THREE.Group();

        // 塔基
        const baseGeometry = new THREE.BoxGeometry(16, 10, 20);
        const base = new THREE.Mesh(baseGeometry, this.materials.getMaterial('concrete'));
        base.position.y = 5;
        towerGroup.add(base);

        // 塔身 - 双柱式
        const towerHeight = 120;
        
        [-6, 6].forEach(zOffset => {
            const towerGeometry = new THREE.BoxGeometry(4, towerHeight, 4);
            const towerColumn = new THREE.Mesh(towerGeometry, this.materials.getMaterial('concrete'));
            towerColumn.position.set(0, 10 + towerHeight/2, zOffset);
            towerGroup.add(towerColumn);
        });

        // 塔顶横梁
        const crossBeamGeometry = new THREE.BoxGeometry(4, 4, 16);
        const crossBeam = new THREE.Mesh(crossBeamGeometry, this.materials.getMaterial('concrete'));
        crossBeam.position.y = 10 + towerHeight;
        towerGroup.add(crossBeam);

        return towerGroup;
    }

    /**
     * 创建道路系统
     */
    createRoadSystem() {
        const roadGroup = new THREE.Group();
        roadGroup.name = 'RoadSystem';

        // 引桥
        const approachRoads = this.createApproachRoads();
        roadGroup.add(approachRoads);

        // 匝道
        const ramps = this.createRamps();
        roadGroup.add(ramps);

        return roadGroup;
    }

    /**
     * 创建引桥
     */
    createApproachRoads() {
        const approachGroup = new THREE.Group();

        // 左侧引桥
        const leftApproachGeometry = new THREE.BoxGeometry(400, 0.5, 32);
        const leftApproach = new THREE.Mesh(leftApproachGeometry, this.materials.getMaterial('asphalt'));
        leftApproach.position.set(-800, 30, 0);
        approachGroup.add(leftApproach);

        // 右侧引桥
        const rightApproachGeometry = new THREE.BoxGeometry(400, 0.5, 32);
        const rightApproach = new THREE.Mesh(rightApproachGeometry, this.materials.getMaterial('asphalt'));
        rightApproach.position.set(800, 30, 0);
        approachGroup.add(rightApproach);

        return approachGroup;
    }

    /**
     * 创建匝道
     */
    createRamps() {
        const rampsGroup = new THREE.Group();

        // 创建弯曲的匝道
        const rampCurve = new THREE.QuadraticBezierCurve3(
            new THREE.Vector3(-600, 0, 50),
            new THREE.Vector3(-500, 15, 100),
            new THREE.Vector3(-400, 30, 150)
        );

        const rampGeometry = new THREE.TubeGeometry(rampCurve, 20, 8, 8, false);
        const ramp = new THREE.Mesh(rampGeometry, this.materials.getMaterial('asphalt'));
        rampsGroup.add(ramp);

        return rampsGroup;
    }

    /**
     * 创建地形
     */
    createTerrain() {
        const terrainGroup = new THREE.Group();
        terrainGroup.name = 'Terrain';

        // 基础地面
        const groundGeometry = new THREE.PlaneGeometry(3000, 2000, 50, 50);
        const ground = new THREE.Mesh(groundGeometry, this.materials.getMaterial('ground'));
        ground.rotation.x = -Math.PI / 2;
        ground.position.y = -5;
        terrainGroup.add(ground);

        // 河流
        const riverGeometry = new THREE.PlaneGeometry(3000, 200);
        const river = new THREE.Mesh(riverGeometry, this.materials.getMaterial('water'));
        river.rotation.x = -Math.PI / 2;
        river.position.y = -4;
        terrainGroup.add(river);

        // 添加地形起伏
        this.addTerrainVariation(groundGeometry);

        return terrainGroup;
    }

    /**
     * 添加地形变化
     */
    addTerrainVariation(geometry) {
        const vertices = geometry.attributes.position.array;
        
        for (let i = 0; i < vertices.length; i += 3) {
            const x = vertices[i];
            const z = vertices[i + 2];
            
            // 使用噪声函数创建自然的地形起伏
            vertices[i + 1] = Math.sin(x * 0.01) * Math.cos(z * 0.01) * 10 +
                             Math.sin(x * 0.005) * Math.cos(z * 0.005) * 20;
        }
        
        geometry.attributes.position.needsUpdate = true;
        geometry.computeVertexNormals();
    }

    /**
     * 创建施工设备
     */
    createConstructionEquipment() {
        const equipmentGroup = new THREE.Group();
        equipmentGroup.name = 'ConstructionEquipment';

        // 塔吊
        const crane = this.createTowerCrane();
        crane.position.set(-300, 0, 100);
        equipmentGroup.add(crane);

        // 混凝土泵车
        const pumpTruck = this.createConcretePump();
        pumpTruck.position.set(100, 73, 50);
        equipmentGroup.add(pumpTruck);

        return equipmentGroup;
    }

    /**
     * 创建塔吊
     */
    createTowerCrane() {
        const craneGroup = new THREE.Group();

        // 塔身
        const mastGeometry = new THREE.BoxGeometry(2, 80, 2);
        const mast = new THREE.Mesh(mastGeometry, this.materials.getMaterial('equipment'));
        mast.position.y = 40;
        craneGroup.add(mast);

        // 横臂
        const jibGeometry = new THREE.BoxGeometry(60, 1, 1);
        const jib = new THREE.Mesh(jibGeometry, this.materials.getMaterial('equipment'));
        jib.position.set(15, 80, 0);
        craneGroup.add(jib);

        // 配重
        const counterweightGeometry = new THREE.BoxGeometry(8, 4, 4);
        const counterweight = new THREE.Mesh(counterweightGeometry, this.materials.getMaterial('steel'));
        counterweight.position.set(-20, 80, 0);
        craneGroup.add(counterweight);

        return craneGroup;
    }

    /**
     * 创建混凝土泵车
     */
    createConcretePump() {
        const pumpGroup = new THREE.Group();

        // 车身
        const bodyGeometry = new THREE.BoxGeometry(12, 3, 3);
        const body = new THREE.Mesh(bodyGeometry, this.materials.getMaterial('equipment'));
        body.position.y = 1.5;
        pumpGroup.add(body);

        // 泵臂
        const armGeometry = new THREE.CylinderGeometry(0.3, 0.3, 25, 8);
        const arm = new THREE.Mesh(armGeometry, this.materials.getMaterial('steel'));
        arm.rotation.z = Math.PI / 4;
        arm.position.set(8, 15, 0);
        pumpGroup.add(arm);

        return pumpGroup;
    }

    /**
     * 获取所有模型
     */
    getAllModels() {
        return {
            bridge: this.createBridgeSystem(),
            road: this.createRoadSystem(),
            terrain: this.createTerrain(),
            equipment: this.createConstructionEquipment()
        };
    }
}
