/**
 * 应用程序核心架构
 * 提供统一的生命周期管理、事件系统和服务注册
 */

class ApplicationCore {
    constructor() {
        // 核心服务容器
        this.services = new Map();
        this.modules = new Map();
        
        // 生命周期状态
        this.state = 'initializing';
        this.initializationPromises = new Map();
        
        // 统一事件总线
        this.eventBus = new EventBus();
        
        // 错误处理
        this.errorHandler = new ErrorHandler();
        
        // 配置管理
        this.config = new ConfigManager();
        
        // 资源管理
        this.resourceManager = new ResourceManager();
        
        console.log('🏗️ 应用程序核心初始化');
    }

    /**
     * 注册服务
     */
    registerService(name, serviceClass, dependencies = []) {
        if (this.services.has(name)) {
            throw new Error(`服务 ${name} 已存在`);
        }

        this.services.set(name, {
            serviceClass,
            dependencies,
            instance: null,
            initialized: false
        });

        console.log(`📦 注册服务: ${name}`);
    }

    /**
     * 获取服务实例
     */
    async getService(name) {
        if (!this.services.has(name)) {
            throw new Error(`服务 ${name} 未注册`);
        }

        const service = this.services.get(name);
        
        if (!service.instance) {
            // 检查依赖
            const dependencies = await Promise.all(
                service.dependencies.map(dep => this.getService(dep))
            );
            
            // 创建实例 - core 始终是第一个参数
            service.instance = new service.serviceClass(this, ...dependencies);
            
            // 初始化
            if (service.instance.initialize) {
                await service.instance.initialize();
            }
            
            service.initialized = true;
        }

        return service.instance;
    }

    /**
     * 初始化应用程序
     */
    async initialize() {
        try {
            this.state = 'initializing';
            
            console.log('🚀 开始应用程序初始化');
            
            // 初始化核心组件
            await this.initializeCore();
            
            // 按依赖顺序初始化服务
            await this.initializeServices();
            
            // 启动模块
            await this.startModules();
            
            this.state = 'running';
            
            console.log('✅ 应用程序初始化完成');
            this.eventBus.emit('app:initialized');
            
        } catch (error) {
            this.state = 'error';
            this.errorHandler.handleError('应用程序初始化失败', error);
            throw error;
        }
    }

    /**
     * 初始化核心组件
     */
    async initializeCore() {
        // 初始化配置管理器
        await this.config.load();
        
        // 初始化资源管理器
        await this.resourceManager.initialize();
        
        // 设置全局错误处理
        this.setupGlobalErrorHandling();
    }

    /**
     * 初始化服务
     */
    async initializeServices() {
        const serviceNames = Array.from(this.services.keys());
        
        // 拓扑排序解决依赖关系
        const sortedServices = this.topologicalSort(serviceNames);
        
        for (const serviceName of sortedServices) {
            await this.getService(serviceName);
        }
    }

    /**
     * 拓扑排序
     */
    topologicalSort(serviceNames) {
        const visited = new Set();
        const result = [];
        
        const visit = (name) => {
            if (visited.has(name)) return;
            
            const service = this.services.get(name);
            if (service) {
                service.dependencies.forEach(dep => visit(dep));
            }
            
            visited.add(name);
            result.push(name);
        };
        
        serviceNames.forEach(name => visit(name));
        return result;
    }

    /**
     * 启动模块
     */
    async startModules() {
        for (const [name, module] of this.modules) {
            if (module.start) {
                await module.start();
                console.log(`▶️ 模块已启动: ${name}`);
            }
        }
    }

    /**
     * 设置全局错误处理
     */
    setupGlobalErrorHandling() {
        window.addEventListener('error', (event) => {
            this.errorHandler.handleError('未捕获的JavaScript错误', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            this.errorHandler.handleError('未捕获的Promise拒绝', event.reason);
        });
    }

    /**
     * 关闭应用程序
     */
    async shutdown() {
        try {
            this.state = 'shutting_down';
            
            console.log('🛑 关闭应用程序');
            
            // 关闭模块
            for (const [name, module] of this.modules) {
                if (module.stop) {
                    await module.stop();
                    console.log(`⏹️ 模块已关闭: ${name}`);
                }
            }
            
            // 清理服务
            for (const [name, service] of this.services) {
                if (service.instance && service.instance.cleanup) {
                    await service.instance.cleanup();
                }
            }
            
            // 清理资源
            await this.resourceManager.cleanup();
            
            this.state = 'stopped';
            console.log('✅ 应用程序已关闭');
            
        } catch (error) {
            this.errorHandler.handleError('应用程序关闭失败', error);
        }
    }

    /**
     * 获取应用状态
     */
    getState() {
        return {
            state: this.state,
            services: Array.from(this.services.keys()),
            modules: Array.from(this.modules.keys()),
            memoryUsage: this.getMemoryUsage()
        };
    }

    /**
     * 获取事件总线
     */
    getEventBus() {
        return this.eventBus;
    }

    /**
     * 获取错误处理器
     */
    getErrorHandler() {
        return this.errorHandler;
    }

    /**
     * 获取配置管理器
     */
    getConfigManager() {
        return this.config;
    }

    /**
     * 获取资源管理器
     */
    getResourceManager() {
        return this.resourceManager;
    }

    /**
     * 获取内存使用情况
     */
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }
}

/**
 * 事件总线
 */
class EventBus {
    constructor() {
        this.listeners = new Map();
    }

    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    clear() {
        this.listeners.clear();
    }
}

/**
 * 错误处理器
 */
class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 100;
    }

    handleError(message, error) {
        const errorInfo = {
            message,
            error: error?.message || error,
            stack: error?.stack,
            timestamp: new Date().toISOString()
        };

        this.errors.push(errorInfo);
        
        // 限制错误记录数量
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }

        console.error(`❌ ${message}:`, error);
        
        // 可以在这里添加错误上报逻辑
    }

    getErrors() {
        return [...this.errors];
    }

    clearErrors() {
        this.errors = [];
    }
}

/**
 * 配置管理器
 */
class ConfigManager {
    constructor() {
        this.config = new Map();
        this.defaultConfig = new Map();
    }

    async load() {
        // 加载默认配置
        this.setDefaults();
        
        // 从localStorage加载用户配置
        this.loadUserConfig();
        
        console.log('⚙️ 配置管理器已加载');
    }

    setDefaults() {
        this.defaultConfig.set('performance.maxFPS', 60);
        this.defaultConfig.set('graphics.shadows', true);
        this.defaultConfig.set('graphics.antialias', true);
        this.defaultConfig.set('models.autoOptimize', true);
        this.defaultConfig.set('debug.enabled', false);
    }

    loadUserConfig() {
        try {
            const stored = localStorage.getItem('bridgeApp.config');
            if (stored) {
                const userConfig = JSON.parse(stored);
                Object.entries(userConfig).forEach(([key, value]) => {
                    this.config.set(key, value);
                });
            }
        } catch (error) {
            console.warn('加载用户配置失败:', error);
        }
    }

    get(key, defaultValue = null) {
        if (this.config.has(key)) {
            return this.config.get(key);
        }
        if (this.defaultConfig.has(key)) {
            return this.defaultConfig.get(key);
        }
        return defaultValue;
    }

    set(key, value) {
        this.config.set(key, value);
        this.saveUserConfig();
    }

    saveUserConfig() {
        try {
            const configObject = Object.fromEntries(this.config);
            localStorage.setItem('bridgeApp.config', JSON.stringify(configObject));
        } catch (error) {
            console.warn('保存用户配置失败:', error);
        }
    }
}

/**
 * 资源管理器
 */
class ResourceManager {
    constructor() {
        this.resources = new Map();
        this.resourceTypes = new Map();
    }

    async initialize() {
        // 注册资源类型
        this.registerResourceType('texture', THREE.TextureLoader);
        this.registerResourceType('model', THREE.GLTFLoader);
        
        console.log('📦 资源管理器已初始化');
    }

    registerResourceType(type, loaderClass) {
        this.resourceTypes.set(type, loaderClass);
    }

    async loadResource(type, url, options = {}) {
        const cacheKey = `${type}:${url}`;
        
        if (this.resources.has(cacheKey)) {
            return this.resources.get(cacheKey);
        }

        const LoaderClass = this.resourceTypes.get(type);
        if (!LoaderClass) {
            throw new Error(`不支持的资源类型: ${type}`);
        }

        const loader = new LoaderClass();
        const resource = await this.loadWithLoader(loader, url, options);
        
        this.resources.set(cacheKey, resource);
        return resource;
    }

    loadWithLoader(loader, url, options) {
        return new Promise((resolve, reject) => {
            loader.load(
                url,
                resolve,
                options.onProgress,
                reject
            );
        });
    }

    unloadResource(type, url) {
        const cacheKey = `${type}:${url}`;
        if (this.resources.has(cacheKey)) {
            const resource = this.resources.get(cacheKey);
            
            // 清理资源
            if (resource.dispose) {
                resource.dispose();
            }
            
            this.resources.delete(cacheKey);
        }
    }

    async cleanup() {
        for (const [key, resource] of this.resources) {
            if (resource.dispose) {
                resource.dispose();
            }
        }
        this.resources.clear();
        console.log('🧹 资源管理器已清理');
    }

    getStats() {
        return {
            totalResources: this.resources.size,
            types: Array.from(this.resourceTypes.keys())
        };
    }
}

// 导出核心类
window.ApplicationCore = ApplicationCore;
window.EventBus = EventBus;