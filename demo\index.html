<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路桥建设数字孪生系统</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 主容器 -->
    <div id="container">
        <!-- 3D渲染画布 -->
        <canvas id="three-canvas"></canvas>
        
        <!-- 顶部导航栏 -->
        <header class="top-nav">
            <div class="nav-left">
                <h1><i class="fas fa-bridge"></i> 路桥数字孪生系统</h1>
            </div>
            <div class="nav-center">
                <div class="project-info">
                    <span class="project-name">某某大桥建设项目</span>
                    <span class="project-status">施工中</span>
                </div>
            </div>
            <div class="nav-right">
                <button class="nav-btn" id="fullscreen-btn">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" id="settings-btn">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </header>

        <!-- 左侧控制面板 -->
        <aside class="left-panel">
            <div class="panel-section">
                <h3><i class="fas fa-eye"></i> 视图控制</h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview">
                        <i class="fas fa-globe"></i>
                        <span>总览视图</span>
                    </button>
                    <button class="view-btn" data-view="bridge">
                        <i class="fas fa-bridge"></i>
                        <span>桥梁视图</span>
                    </button>
                    <button class="view-btn" data-view="road">
                        <i class="fas fa-road"></i>
                        <span>道路视图</span>
                    </button>
                    <button class="view-btn" data-view="construction">
                        <i class="fas fa-hard-hat"></i>
                        <span>施工视图</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-layer-group"></i> 图层管理</h3>
                <div class="layer-controls">
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="bridge">
                        <span class="checkmark"></span>
                        <span>桥梁结构</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="road">
                        <span class="checkmark"></span>
                        <span>道路系统</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="terrain">
                        <span class="checkmark"></span>
                        <span>地形地貌</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" data-layer="equipment">
                        <span class="checkmark"></span>
                        <span>施工设备</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" data-layer="safety">
                        <span class="checkmark"></span>
                        <span>安全设施</span>
                    </label>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-palette"></i> 渲染设置</h3>
                <div class="render-controls">
                    <div class="control-group">
                        <label>光照强度</label>
                        <input type="range" id="light-intensity" min="0" max="2" step="0.1" value="1">
                    </div>
                    <div class="control-group">
                        <label>阴影质量</label>
                        <select id="shadow-quality">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>雾效强度</label>
                        <input type="range" id="fog-density" min="0" max="0.01" step="0.001" value="0.002">
                    </div>
                </div>
            </div>
        </aside>

        <!-- 右侧信息面板 -->
        <aside class="right-panel">
            <div class="panel-section">
                <h3><i class="fas fa-info-circle"></i> 项目信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">桥梁长度</span>
                        <span class="value">1,200m</span>
                    </div>
                    <div class="info-item">
                        <span class="label">桥面宽度</span>
                        <span class="value">32m</span>
                    </div>
                    <div class="info-item">
                        <span class="label">主跨长度</span>
                        <span class="value">400m</span>
                    </div>
                    <div class="info-item">
                        <span class="label">桥墩数量</span>
                        <span class="value">8个</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-chart-line"></i> 施工进度</h3>
                <div class="progress-list">
                    <div class="progress-item">
                        <span class="task-name">桥墩施工</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%"></div>
                        </div>
                        <span class="progress-text">85%</span>
                    </div>
                    <div class="progress-item">
                        <span class="task-name">主梁安装</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%"></div>
                        </div>
                        <span class="progress-text">60%</span>
                    </div>
                    <div class="progress-item">
                        <span class="task-name">桥面铺装</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 30%"></div>
                        </div>
                        <span class="progress-text">30%</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-tools"></i> 快速操作</h3>
                <div class="quick-actions">
                    <button class="action-btn">
                        <i class="fas fa-camera"></i>
                        <span>截图</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-video"></i>
                        <span>录制</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-ruler"></i>
                        <span>测量</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-download"></i>
                        <span>导出</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- 底部状态栏 -->
        <footer class="bottom-status">
            <div class="status-left">
                <span class="status-item">
                    <i class="fas fa-mouse-pointer"></i>
                    <span id="cursor-position">X: 0, Y: 0, Z: 0</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-eye"></i>
                    <span id="camera-position">相机: 总览视图</span>
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span id="fps-counter">FPS: 60</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-cube"></i>
                    <span id="object-count">对象: 0</span>
                </span>
            </div>
        </footer>

        <!-- 加载界面 -->
        <div id="loading-screen">
            <div class="loading-content">
                <div class="loading-logo">
                    <i class="fas fa-bridge"></i>
                </div>
                <h2>路桥数字孪生系统</h2>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p class="loading-text">正在加载3D场景...</p>
            </div>
        </div>
    </div>

    <!-- Three.js 和相关库 - 使用BootCDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/0.158.0/three.min.js"></script>
    <script>
        // 检查Three.js是否加载成功
        window.addEventListener('load', function() {
            if (typeof THREE === 'undefined') {
                console.error('❌ Three.js主库加载失败，尝试备用CDN');
                // 尝试备用CDN
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/three@0.158.0/build/three.min.js';
                script.onload = function() {
                    console.log('✅ Three.js从备用CDN加载成功');
                    loadOrbitControls();
                };
                script.onerror = function() {
                    console.error('❌ 所有CDN都无法加载Three.js');
                    showLoadError();
                };
                document.head.appendChild(script);
            } else {
                console.log('✅ Three.js主库加载成功，版本:', THREE.REVISION);
                loadOrbitControls();
            }
        });

        // 加载OrbitControls和其他扩展
        function loadOrbitControls() {
            // 尝试多个CDN源加载OrbitControls
            const controlsSources = [
                'https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/controls/OrbitControls.js',
                'https://threejs.org/examples/js/controls/OrbitControls.js'
            ];

            let currentIndex = 0;

            function tryLoadControls() {
                if (currentIndex >= controlsSources.length) {
                    console.warn('⚠️ 所有OrbitControls源都加载失败，创建基础替代');
                    createBasicOrbitControls();
                    return;
                }

                const script = document.createElement('script');
                script.src = controlsSources[currentIndex];
                script.onload = function() {
                    console.log('✅ OrbitControls加载成功');
                    loadEffectComposer();
                };
                script.onerror = function() {
                    console.warn(`⚠️ OrbitControls源 ${currentIndex + 1} 加载失败`);
                    currentIndex++;
                    tryLoadControls();
                };
                document.head.appendChild(script);
            }

            tryLoadControls();
        }

        // 加载EffectComposer
        function loadEffectComposer() {
            const composerSources = [
                'https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/postprocessing/EffectComposer.js',
                'https://threejs.org/examples/js/postprocessing/EffectComposer.js'
            ];

            let currentIndex = 0;

            function tryLoadComposer() {
                if (currentIndex >= composerSources.length) {
                    console.warn('⚠️ EffectComposer加载失败，将禁用后处理效果');
                    return;
                }

                const script = document.createElement('script');
                script.src = composerSources[currentIndex];
                script.onload = function() {
                    console.log('✅ EffectComposer加载成功');
                    loadRenderPass();
                };
                script.onerror = function() {
                    console.warn(`⚠️ EffectComposer源 ${currentIndex + 1} 加载失败`);
                    currentIndex++;
                    tryLoadComposer();
                };
                document.head.appendChild(script);
            }

            tryLoadComposer();
        }

        // 加载RenderPass
        function loadRenderPass() {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/postprocessing/RenderPass.js';
            script.onload = function() {
                console.log('✅ RenderPass加载成功');
                loadUnrealBloomPass();
            };
            script.onerror = function() {
                console.warn('⚠️ RenderPass加载失败');
            };
            document.head.appendChild(script);
        }

        // 加载UnrealBloomPass
        function loadUnrealBloomPass() {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/postprocessing/UnrealBloomPass.js';
            script.onload = function() {
                console.log('✅ UnrealBloomPass加载成功');
            };
            script.onerror = function() {
                console.warn('⚠️ UnrealBloomPass加载失败');
            };
            document.head.appendChild(script);
        }

        // 创建基础OrbitControls替代
        function createBasicOrbitControls() {
            if (typeof THREE !== 'undefined') {
                THREE.OrbitControls = function(camera, domElement) {
                    this.object = camera;
                    this.domElement = domElement;
                    this.target = new THREE.Vector3();
                    this.update = function() {};
                    this.dispose = function() {};
                    this.enableDamping = true;
                    this.dampingFactor = 0.05;
                    this.enableZoom = true;
                    this.enableRotate = true;
                    this.enablePan = true;
                    this.minDistance = 0;
                    this.maxDistance = Infinity;
                    this.minPolarAngle = 0;
                    this.maxPolarAngle = Math.PI;
                    this.reset = function() {};

                    // 添加基础的鼠标控制
                    let isMouseDown = false;
                    let mouseX = 0, mouseY = 0;

                    domElement.addEventListener('mousedown', (event) => {
                        isMouseDown = true;
                        mouseX = event.clientX;
                        mouseY = event.clientY;
                    });

                    domElement.addEventListener('mousemove', (event) => {
                        if (!isMouseDown) return;

                        const deltaX = event.clientX - mouseX;
                        const deltaY = event.clientY - mouseY;

                        // 简单的相机旋转
                        const spherical = new THREE.Spherical();
                        spherical.setFromVector3(camera.position.clone().sub(this.target));
                        spherical.theta -= deltaX * 0.01;
                        spherical.phi += deltaY * 0.01;
                        spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, spherical.phi));

                        camera.position.setFromSpherical(spherical).add(this.target);
                        camera.lookAt(this.target);

                        mouseX = event.clientX;
                        mouseY = event.clientY;
                    });

                    domElement.addEventListener('mouseup', () => {
                        isMouseDown = false;
                    });

                    // 缩放控制
                    domElement.addEventListener('wheel', (event) => {
                        const scale = event.deltaY > 0 ? 1.1 : 0.9;
                        camera.position.sub(this.target).multiplyScalar(scale).add(this.target);
                    });
                };
                console.log('✅ 基础OrbitControls替代已创建');
            }
        }

        // 显示加载错误
        function showLoadError() {
            document.body.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                            background: #2c3e50; color: white; display: flex;
                            align-items: center; justify-content: center; z-index: 10000;">
                    <div style="text-align: center; max-width: 600px; padding: 40px;">
                        <h2>❌ 资源加载失败</h2>
                        <p>无法从任何CDN加载Three.js库，可能是网络连接问题。</p>
                        <p>请检查网络连接后重试。</p>
                        <button onclick="location.reload()"
                                style="margin-top: 20px; padding: 10px 20px;
                                       background: #3498db; color: white; border: none;
                                       border-radius: 5px; cursor: pointer;">
                            重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    </script>
    
    <!-- 应用脚本 -->
    <script src="js/materials.js"></script>
    <script src="js/geometry.js"></script>
    <script src="js/scene.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
