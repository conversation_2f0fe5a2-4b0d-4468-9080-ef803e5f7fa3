/**
 * 路桥数字孪生场景管理系统
 * 负责Three.js场景的初始化、渲染和管理
 */

class BridgeScene {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.composer = null;
        
        this.materials = null;
        this.geometry = null;
        this.models = {};
        
        this.lights = {};
        this.environment = {};
        
        this.stats = {
            fps: 0,
            objectCount: 0,
            lastTime: 0
        };
        
        this.settings = {
            shadows: true,
            fog: true,
            postProcessing: true,
            lightIntensity: 1.0,
            fogDensity: 0.002
        };

        this.init();
    }

    /**
     * 初始化场景
     */
    init() {
        this.initRenderer();
        this.initScene();
        this.initCamera();
        this.initLights();
        this.initEnvironment();
        this.initPostProcessing();
        this.initMaterials();
        this.initGeometry();
        this.createModels();
        this.setupEventListeners();
        this.animate();
    }

    /**
     * 初始化渲染器
     */
    initRenderer() {
        // 尝试获取现有的canvas，如果不存在则创建
        let canvas = document.getElementById('three-canvas');
        
        if (!canvas) {
            // 如果没有canvas，创建一个并添加到scene-container
            canvas = document.createElement('canvas');
            canvas.id = 'three-canvas';
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.display = 'block';
            
            const container = document.getElementById('scene-container');
            if (container) {
                container.appendChild(canvas);
            } else {
                // 如果没有容器，直接添加到body
                document.body.appendChild(canvas);
            }
        }
        
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: true,
            alpha: false,
            powerPreference: "high-performance"
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // 启用阴影
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 设置色调映射
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
        
        // 启用物理正确的光照
        this.renderer.physicallyCorrectLights = true;
        
        // 设置输出编码
        this.renderer.outputEncoding = THREE.sRGBEncoding;
    }

    /**
     * 初始化场景
     */
    initScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x87ceeb); // 天空蓝
        
        // 添加雾效
        this.scene.fog = new THREE.Fog(0x87ceeb, 1000, 5000);
    }

    /**
     * 初始化相机
     */
    initCamera() {
        this.camera = new THREE.PerspectiveCamera(
            60,
            window.innerWidth / window.innerHeight,
            1,
            10000
        );
        
        // 设置初始相机位置 - 总览视角
        this.camera.position.set(-800, 200, 800);
        this.camera.lookAt(0, 50, 0);
    }

    /**
     * 初始化光照系统
     */
    initLights() {
        // 环境光
        this.lights.ambient = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(this.lights.ambient);

        // 主方向光（太阳光）
        this.lights.directional = new THREE.DirectionalLight(0xffffff, 1.0);
        this.lights.directional.position.set(1000, 1000, 500);
        this.lights.directional.castShadow = true;
        
        // 配置阴影
        this.lights.directional.shadow.mapSize.width = 4096;
        this.lights.directional.shadow.mapSize.height = 4096;
        this.lights.directional.shadow.camera.near = 0.5;
        this.lights.directional.shadow.camera.far = 3000;
        this.lights.directional.shadow.camera.left = -1500;
        this.lights.directional.shadow.camera.right = 1500;
        this.lights.directional.shadow.camera.top = 1500;
        this.lights.directional.shadow.camera.bottom = -1500;
        this.lights.directional.shadow.bias = -0.0001;
        
        this.scene.add(this.lights.directional);

        // 补充光源
        this.lights.fill = new THREE.DirectionalLight(0x87ceeb, 0.3);
        this.lights.fill.position.set(-500, 300, -500);
        this.scene.add(this.lights.fill);

        // 点光源（用于夜景模式）
        this.lights.point = new THREE.PointLight(0xffa500, 0.5, 1000);
        this.lights.point.position.set(0, 100, 0);
        this.lights.point.castShadow = true;
        this.scene.add(this.lights.point);

        // 聚光灯（施工区域照明）
        this.lights.spot = new THREE.SpotLight(0xffffff, 0.8, 500, Math.PI / 6, 0.1);
        this.lights.spot.position.set(200, 150, 200);
        this.lights.spot.target.position.set(200, 0, 200);
        this.lights.spot.castShadow = true;
        this.scene.add(this.lights.spot);
        this.scene.add(this.lights.spot.target);
    }

    /**
     * 初始化环境
     */
    initEnvironment() {
        // 创建天空盒
        this.createSkybox();
        
        // 创建环境贴图
        this.createEnvironmentMap();
    }

    /**
     * 创建天空盒
     */
    createSkybox() {
        const skyGeometry = new THREE.SphereGeometry(8000, 32, 32);
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(0x0077ff) },
                bottomColor: { value: new THREE.Color(0xffffff) },
                offset: { value: 400 },
                exponent: { value: 0.6 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 topColor;
                uniform vec3 bottomColor;
                uniform float offset;
                uniform float exponent;
                varying vec3 vWorldPosition;
                void main() {
                    float h = normalize(vWorldPosition + offset).y;
                    gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
                }
            `,
            side: THREE.BackSide
        });
        
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
        this.environment.sky = sky;
    }

    /**
     * 创建环境贴图
     */
    createEnvironmentMap() {
        const pmremGenerator = new THREE.PMREMGenerator(this.renderer);
        pmremGenerator.compileEquirectangularShader();
        
        // 创建简单的环境贴图
        const envScene = new THREE.Scene();
        envScene.background = new THREE.Color(0x87ceeb);
        
        const envMap = pmremGenerator.fromScene(envScene).texture;
        this.scene.environment = envMap;
        
        pmremGenerator.dispose();
    }

    /**
     * 初始化后处理
     */
    initPostProcessing() {
        if (!this.settings.postProcessing) return;

        try {
            // 检查是否有必要的后处理类
            if (typeof THREE.EffectComposer === 'undefined') {
                console.warn('⚠️ EffectComposer未加载，跳过后处理初始化');
                this.settings.postProcessing = false;
                return;
            }

            // 创建效果合成器
            this.composer = new THREE.EffectComposer(this.renderer);

            // 渲染通道
            if (typeof THREE.RenderPass !== 'undefined') {
                const renderPass = new THREE.RenderPass(this.scene, this.camera);
                this.composer.addPass(renderPass);
            }

            // 辉光效果
            if (typeof THREE.UnrealBloomPass !== 'undefined') {
                const bloomPass = new THREE.UnrealBloomPass(
                    new THREE.Vector2(window.innerWidth, window.innerHeight),
                    0.3,  // 强度
                    0.4,  // 半径
                    0.85  // 阈值
                );
                this.composer.addPass(bloomPass);
            }

            console.log('✅ 后处理系统初始化成功');
        } catch (error) {
            console.warn('⚠️ 后处理初始化失败:', error);
            this.settings.postProcessing = false;
            this.composer = null;
        }
    }

    /**
     * 初始化材质系统
     */
    initMaterials() {
        this.materials = new BridgeMaterials();
    }

    /**
     * 初始化几何体系统
     */
    initGeometry() {
        this.geometry = new BridgeGeometry(this.materials);
    }

    /**
     * 创建所有模型
     */
    createModels() {
        const models = this.geometry.getAllModels();
        
        // 添加桥梁系统
        this.models.bridge = models.bridge;
        this.scene.add(this.models.bridge);
        
        // 添加道路系统
        this.models.road = models.road;
        this.scene.add(this.models.road);
        
        // 添加地形
        this.models.terrain = models.terrain;
        this.scene.add(this.models.terrain);
        
        // 添加施工设备
        this.models.equipment = models.equipment;
        this.scene.add(this.models.equipment);
        
        // 启用阴影
        this.enableShadows();
        
        // 更新对象计数
        this.updateObjectCount();
    }

    /**
     * 启用阴影
     */
    enableShadows() {
        this.scene.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
        
        // 键盘事件
        window.addEventListener('keydown', (event) => {
            this.onKeyDown(event);
        });
    }

    /**
     * 窗口大小调整处理
     */
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        
        if (this.composer) {
            this.composer.setSize(window.innerWidth, window.innerHeight);
        }
    }

    /**
     * 键盘事件处理
     */
    onKeyDown(event) {
        switch (event.code) {
            case 'KeyF':
                this.toggleFullscreen();
                break;
            case 'KeyH':
                this.toggleUI();
                break;
            case 'KeyR':
                this.resetCamera();
                break;
        }
    }

    /**
     * 切换全屏
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * 切换UI显示
     */
    toggleUI() {
        const leftPanel = document.querySelector('.left-panel');
        const rightPanel = document.querySelector('.right-panel');
        const topNav = document.querySelector('.top-nav');
        const bottomStatus = document.querySelector('.bottom-status');
        
        [leftPanel, rightPanel, topNav, bottomStatus].forEach(panel => {
            if (panel) {
                panel.style.display = panel.style.display === 'none' ? '' : 'none';
            }
        });
    }

    /**
     * 重置相机
     */
    resetCamera() {
        this.camera.position.set(-800, 200, 800);
        this.camera.lookAt(0, 50, 0);
        
        if (this.controls) {
            this.controls.reset();
        }
    }

    /**
     * 更新对象计数
     */
    updateObjectCount() {
        let count = 0;
        this.scene.traverse(() => {
            count++;
        });
        this.stats.objectCount = count;
    }

    /**
     * 更新设置
     */
    updateSettings(newSettings) {
        Object.assign(this.settings, newSettings);
        
        // 应用设置
        if (this.settings.shadows !== this.renderer.shadowMap.enabled) {
            this.renderer.shadowMap.enabled = this.settings.shadows;
        }
        
        if (this.settings.fog) {
            this.scene.fog.density = this.settings.fogDensity;
        } else {
            this.scene.fog = null;
        }
        
        // 更新光照强度
        if (this.lights.directional) {
            this.lights.directional.intensity = this.settings.lightIntensity;
        }
    }

    /**
     * 切换图层显示
     */
    toggleLayer(layerName, visible) {
        if (this.models[layerName]) {
            this.models[layerName].visible = visible;
        }
    }

    /**
     * 设置相机视角
     */
    setCameraView(viewName) {
        const views = {
            overview: { position: [-800, 200, 800], target: [0, 50, 0] },
            bridge: { position: [0, 100, 300], target: [0, 70, 0] },
            road: { position: [-600, 50, 100], target: [-400, 30, 0] },
            construction: { position: [200, 80, 200], target: [200, 50, 200] }
        };
        
        const view = views[viewName];
        if (view) {
            this.camera.position.set(...view.position);
            this.camera.lookAt(...view.target);
            
            if (this.controls) {
                this.controls.target.set(...view.target);
                this.controls.update();
            }
        }
    }

    /**
     * 动画循环
     */
    animate() {
        requestAnimationFrame(() => this.animate());
        
        const currentTime = performance.now();
        
        // 计算FPS
        if (currentTime - this.stats.lastTime >= 1000) {
            this.stats.fps = Math.round(1000 / (currentTime - this.stats.lastTime) * 60);
            this.stats.lastTime = currentTime;
        }
        
        // 更新控制器
        if (this.controls) {
            this.controls.update();
        }
        
        // 渲染场景
        if (this.composer && this.settings.postProcessing) {
            this.composer.render();
        } else {
            this.renderer.render(this.scene, this.camera);
        }
        
        // 更新统计信息
        this.updateStats();
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        // 更新FPS显示
        const fpsElement = document.getElementById('fps-counter');
        if (fpsElement) {
            fpsElement.textContent = `FPS: ${this.stats.fps}`;
        }
        
        // 更新对象计数显示
        const objectCountElement = document.getElementById('object-count');
        if (objectCountElement) {
            objectCountElement.textContent = `对象: ${this.stats.objectCount}`;
        }
        
        // 更新相机位置显示
        const cameraPositionElement = document.getElementById('camera-position');
        if (cameraPositionElement) {
            const pos = this.camera.position;
            cameraPositionElement.textContent = 
                `相机: X:${pos.x.toFixed(0)} Y:${pos.y.toFixed(0)} Z:${pos.z.toFixed(0)}`;
        }
    }

    /**
     * 获取场景对象
     */
    getScene() {
        return this.scene;
    }

    /**
     * 获取相机对象
     */
    getCamera() {
        return this.camera;
    }

    /**
     * 获取渲染器对象
     */
    getRenderer() {
        return this.renderer;
    }

    /**
     * 设置控制器
     */
    setControls(controls) {
        this.controls = controls;
    }
}
