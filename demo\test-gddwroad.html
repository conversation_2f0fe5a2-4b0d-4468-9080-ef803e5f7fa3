<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 - gddwroad项目默认加载</title>
    
    <!-- 基础样式 -->
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #three-canvas {
            display: block;
            width: 100%;
            height: 100%;
        }
        
        .info-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 8px;
            max-width: 300px;
            z-index: 1000;
        }
        
        .info-panel h3 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }
        
        .info-panel p {
            margin: 5px 0;
            font-size: 14px;
        }
        
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success { background: #4CAF50; }
        .status.warning { background: #FF9800; }
        .status.error { background: #F44336; }
        
        .controls-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .controls-info h4 {
            margin: 0 0 10px 0;
            color: #2196F3;
        }
        
        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 2000;
        }
        
        .loading-spinner {
            border: 4px solid #333;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="container">
        <!-- 3D渲染画布 -->
        <canvas id="three-canvas"></canvas>
        
        <!-- 信息面板 -->
        <div class="info-panel">
            <h3>🚧 gddwroad项目测试</h3>
            <p>默认项目: <span id="current-project">加载中...</span></p>
            <p>相机位置: <span id="camera-position">-</span></p>
            <p>控制器目标: <span id="camera-target">-</span></p>
            <p>拖拽功能: <span id="drag-status" class="status warning">检测中</span></p>
            <p>项目配置: <span id="config-status" class="status warning">加载中</span></p>
        </div>
        
        <!-- 控制说明 -->
        <div class="controls-info">
            <h4>🎮 控制说明</h4>
            <p>• 左键拖拽: 旋转视角</p>
            <p>• 右键拖拽: 平移视角</p>
            <p>• 滚轮: 缩放</p>
            <p>• 中键拖拽: 缩放</p>
        </div>
        
        <!-- 加载指示器 -->
        <div id="loading" class="loading">
            <div class="loading-spinner"></div>
            <p>正在加载 gddwroad 项目...</p>
        </div>
    </div>

    <!-- Three.js 核心库 -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    
    <!-- Three.js 扩展 -->
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/DRACOLoader.js"></script>
    
    <!-- 标记Three.js扩展已加载 -->
    <script>
        window.threeExtensionsLoaded = true;
        console.log('✅ Three.js扩展已加载');
    </script>
    
    <!-- 原有系统模块 -->
    <script src="js/materials.js"></script>
    <script src="js/geometry.js"></script>
    <script src="js/scene.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/ui.js"></script>
    
    <!-- 增强版系统模块 -->
    <script src="js/managers/project-manager.js"></script>
    <script src="js/managers/model-loader.js"></script>
    <script src="js/managers/progress-manager.js"></script>
    <script src="js/managers/enhanced-scene.js"></script>
    <script src="js/ui/project-ui.js"></script>
    <script src="js/ui/progress-ui.js"></script>
    
    <!-- 主应用程序 -->
    <script src="js/enhanced-app.js"></script>
    
    <script>
        // 测试应用程序
        let testApp = null;
        
        // 更新信息显示
        function updateInfo() {
            if (!testApp || !testApp.isInitialized) return;
            
            const currentProject = testApp.currentProject;
            if (currentProject) {
                document.getElementById('current-project').textContent = currentProject.name;
                document.getElementById('config-status').textContent = '✅ 已加载';
                document.getElementById('config-status').className = 'status success';
            }
            
            const camera = testApp.sceneManager?.getCamera();
            if (camera) {
                const pos = camera.position;
                document.getElementById('camera-position').textContent = 
                    `[${pos.x.toFixed(0)}, ${pos.y.toFixed(0)}, ${pos.z.toFixed(0)}]`;
            }
            
            const controls = testApp.sceneManager?.controls;
            if (controls && controls.target) {
                const target = controls.target;
                document.getElementById('camera-target').textContent = 
                    `[${target.x.toFixed(0)}, ${target.y.toFixed(0)}, ${target.z.toFixed(0)}]`;
                    
                document.getElementById('drag-status').textContent = '✅ 正常';
                document.getElementById('drag-status').className = 'status success';
            }
        }
        
        // 启动测试
        function startTest() {
            console.log('🧪 开始 gddwroad 项目测试...');
            
            try {
                testApp = new EnhancedBridgeApp();
                
                // 监听初始化完成
                const checkInit = () => {
                    if (testApp.isInitialized) {
                        console.log('✅ 应用程序初始化完成');
                        document.getElementById('loading').style.display = 'none';
                        
                        // 开始更新信息
                        setInterval(updateInfo, 1000);
                        updateInfo();
                    } else {
                        setTimeout(checkInit, 500);
                    }
                };
                
                checkInit();
                
            } catch (error) {
                console.error('❌ 测试启动失败:', error);
                document.getElementById('loading').innerHTML = 
                    '<p style="color: #F44336;">❌ 启动失败: ' + error.message + '</p>';
            }
        }
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startTest);
        } else {
            startTest();
        }
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('🚨 全局错误:', event.error);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('🚨 未处理的Promise拒绝:', event.reason);
        });
    </script>
</body>
</html>
