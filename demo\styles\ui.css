/* UI组件专用样式 */

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

.info-item .label {
    font-size: 12px;
    color: #bdc3c7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item .value {
    font-size: 14px;
    font-weight: 500;
    color: #ecf0f1;
}

/* 进度列表 */
.progress-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.progress-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.task-name {
    font-size: 13px;
    color: #ecf0f1;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2ecc71 0%, #27ae60 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: 11px;
    color: #2ecc71;
    font-weight: 500;
    align-self: flex-end;
}

/* 快速操作按钮 */
.quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
    padding: 12px 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    text-align: center;
}

.action-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.action-btn i {
    font-size: 16px;
    color: #3498db;
}

/* 加载界面 */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.loading-logo {
    font-size: 64px;
    color: #3498db;
    margin-bottom: 20px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.loading-content h2 {
    font-size: 24px;
    color: #ecf0f1;
    margin-bottom: 30px;
    font-weight: 300;
}

.loading-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 20px;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #3498db 0%, #2980b9 100%);
    border-radius: 2px;
    width: 0%;
    animation: loading 3s ease-in-out infinite;
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.loading-text {
    font-size: 14px;
    color: #bdc3c7;
    margin-top: 10px;
}

/* 工具提示 */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
    border: 1px solid #3498db;
}

.tooltip.show {
    opacity: 1;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #3498db transparent transparent transparent;
}

/* 模态对话框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: 2px solid #3498db;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80%;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
    font-size: 18px;
    color: #ecf0f1;
    font-weight: 500;
}

.modal-close {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #e74c3c;
}

/* 通知系统 */
.notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: 1px solid #3498db;
    border-radius: 8px;
    padding: 15px 20px;
    max-width: 350px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-color: #2ecc71;
}

.notification.warning {
    border-color: #f39c12;
}

.notification.error {
    border-color: #e74c3c;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.notification-icon {
    font-size: 18px;
    margin-top: 2px;
}

.notification.success .notification-icon {
    color: #2ecc71;
}

.notification.warning .notification-icon {
    color: #f39c12;
}

.notification.error .notification-icon {
    color: #e74c3c;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-size: 14px;
    font-weight: 500;
    color: #ecf0f1;
    margin-bottom: 4px;
}

.notification-message {
    font-size: 12px;
    color: #bdc3c7;
    line-height: 1.4;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease;
}

.fade-out {
    animation: fadeOut 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(-10px); }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}
