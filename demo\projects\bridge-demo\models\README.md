# 示例大桥项目 - 模型文件说明

## 模型文件列表

本文件夹应包含以下glTF模型文件：

### 基础阶段模型
- **foundation.gltf** - 桥梁基础结构模型
  - 包含：桩基、承台、基础设施
  - 建议大小：< 10MB
  - 材质：混凝土材质为主

- **piers-base.gltf** - 桥墩基础模型
  - 包含：桥墩底部结构
  - 建议大小：< 8MB

### 下部结构模型
- **piers.gltf** - 完整桥墩模型
  - 包含：桥墩主体、支座预埋件
  - 建议大小：< 15MB
  - 材质：混凝土、钢材

- **abutments.gltf** - 桥台模型
  - 包含：桥台结构、挡土墙
  - 建议大小：< 12MB

### 上部结构模型
- **main-beams.gltf** - 主梁结构模型
  - 包含：预制梁、现浇段
  - 建议大小：< 20MB
  - 材质：预应力混凝土

- **bridge-deck.gltf** - 桥面系统模型
  - 包含：桥面板、铺装层
  - 建议大小：< 15MB

### 缆索系统模型
- **cables.gltf** - 斜拉索模型
  - 包含：拉索、锚具、减振器
  - 建议大小：< 8MB
  - 材质：高强钢丝

- **towers.gltf** - 索塔模型
  - 包含：索塔主体、索鞍
  - 建议大小：< 25MB

### 装饰装修模型
- **road-surface.gltf** - 路面铺装模型
  - 包含：沥青面层、标线
  - 建议大小：< 10MB

- **guardrails.gltf** - 护栏系统模型
  - 包含：防撞护栏、人行道护栏
  - 建议大小：< 8MB

- **lighting.gltf** - 照明系统模型
  - 包含：路灯、景观灯、标志牌
  - 建议大小：< 6MB

### 施工设备模型
- **tower-crane.gltf** - 塔式起重机
  - 建议大小：< 5MB

- **concrete-pump.gltf** - 混凝土泵车
  - 建议大小：< 4MB

- **bridge-girder-erector.gltf** - 架桥机
  - 建议大小：< 8MB

### 完整模型
- **complete-bridge.gltf** - 完工后的完整桥梁模型
  - 包含：所有完成的结构
  - 建议大小：< 50MB

## 模型制作要求

### 技术规范
1. **格式**：glTF 2.0 (.gltf + .bin + 贴图文件 或 .glb)
2. **坐标系**：Y轴向上，Z轴向前
3. **单位**：米（meter）
4. **原点**：桥梁中心点为原点
5. **压缩**：建议使用Draco几何压缩

### 材质要求
1. **PBR材质**：使用基于物理的渲染材质
2. **贴图分辨率**：主要结构1024x1024，细节部分512x512
3. **法线贴图**：重要结构应包含法线贴图
4. **环境遮蔽**：建议包含AO贴图

### 性能优化
1. **面数控制**：单个模型建议不超过100K三角形
2. **LOD支持**：大型模型建议包含多级细节
3. **实例化**：重复元素使用实例化
4. **合并网格**：相同材质的网格尽量合并

## 文件命名规范

- 使用小写字母和连字符
- 文件名应具有描述性
- 按施工阶段分类命名
- 示例：`foundation-piers.gltf`、`superstructure-beams.gltf`

## 注意事项

1. 所有模型文件应放置在此文件夹中
2. 模型坐标应与项目配置中的位置信息对应
3. 材质名称应具有描述性，便于程序识别
4. 建议为每个模型添加适当的元数据

## 测试模型

如果没有实际的glTF模型文件，系统会自动使用程序化生成的几何体作为替代，以确保演示系统正常运行。
