/**
 * 测量工具
 * 提供距离、面积、体积、角度等测量功能
 */

class MeasurementTool {
    constructor(scene, camera, renderer) {
        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;
        this.canvas = renderer.domElement;
        
        this.isActive = false;
        this.measurementMode = 'distance'; // distance, area, volume, angle
        this.measurements = [];
        this.currentMeasurement = null;
        
        // 交互状态
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.points = [];
        
        // 视觉元素
        this.measurementGroup = new THREE.Group();
        this.measurementGroup.name = 'MeasurementGroup';
        this.scene.add(this.measurementGroup);
        
        // 材质
        this.materials = this.createMaterials();
        
        // 事件监听器
        this.eventListeners = new Map();
        
        this.init();
    }

    /**
     * 初始化测量工具
     */
    init() {
        console.log('📏 初始化测量工具...');
        this.setupEventListeners();
        console.log('✅ 测量工具初始化完成');
    }

    /**
     * 创建材质
     */
    createMaterials() {
        return {
            point: new THREE.MeshBasicMaterial({
                color: 0xff4444,
                transparent: true,
                opacity: 0.8
            }),
            line: new THREE.LineBasicMaterial({
                color: 0xff4444,
                linewidth: 2,
                transparent: true,
                opacity: 0.9
            }),
            area: new THREE.MeshBasicMaterial({
                color: 0x44ff44,
                transparent: true,
                opacity: 0.3,
                side: THREE.DoubleSide
            }),
            text: new THREE.MeshBasicMaterial({
                color: 0xffffff,
                transparent: true,
                opacity: 0.9
            })
        };
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        this.canvas.addEventListener('click', (event) => {
            if (this.isActive) {
                this.onCanvasClick(event);
            }
        });
        
        this.canvas.addEventListener('mousemove', (event) => {
            if (this.isActive) {
                this.onCanvasMouseMove(event);
            }
        });
        
        document.addEventListener('keydown', (event) => {
            if (this.isActive) {
                this.onKeyDown(event);
            }
        });
    }

    /**
     * 激活测量工具
     */
    activate(mode = 'distance') {
        this.isActive = true;
        this.measurementMode = mode;
        this.points = [];
        this.currentMeasurement = null;
        
        // 改变鼠标样式
        this.canvas.style.cursor = 'crosshair';
        
        console.log(`📏 测量工具已激活，模式: ${mode}`);
        this.emit('activated', { mode: mode });
    }

    /**
     * 停用测量工具
     */
    deactivate() {
        this.isActive = false;
        this.points = [];
        this.currentMeasurement = null;
        
        // 恢复鼠标样式
        this.canvas.style.cursor = 'grab';
        
        console.log('📏 测量工具已停用');
        this.emit('deactivated');
    }

    /**
     * 画布点击处理
     */
    onCanvasClick(event) {
        // 更新鼠标坐标
        this.updateMousePosition(event);
        
        // 射线检测
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        
        if (intersects.length > 0) {
            const point = intersects[0].point;
            this.addMeasurementPoint(point);
        }
    }

    /**
     * 鼠标移动处理
     */
    onCanvasMouseMove(event) {
        if (!this.isActive || this.points.length === 0) return;
        
        this.updateMousePosition(event);
        this.raycaster.setFromCamera(this.mouse, this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children, true);
        
        if (intersects.length > 0) {
            const point = intersects[0].point;
            this.updatePreview(point);
        }
    }

    /**
     * 键盘事件处理
     */
    onKeyDown(event) {
        switch (event.key) {
            case 'Escape':
                this.cancelCurrentMeasurement();
                break;
            case 'Enter':
                this.completeMeasurement();
                break;
            case 'Delete':
                this.deleteLastPoint();
                break;
        }
    }

    /**
     * 更新鼠标位置
     */
    updateMousePosition(event) {
        if (!this.canvas) {
            console.warn('⚠️ Canvas not available for measurement tool');
            return;
        }
        
        const rect = this.canvas.getBoundingClientRect();
        if (!rect) {
            console.warn('⚠️ Cannot get bounding client rect for measurement tool');
            return;
        }
        
        this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    }

    /**
     * 添加测量点
     */
    addMeasurementPoint(point) {
        this.points.push(point.clone());
        
        // 创建点标记
        this.createPointMarker(point);
        
        // 根据测量模式处理
        switch (this.measurementMode) {
            case 'distance':
                this.handleDistanceMeasurement();
                break;
            case 'area':
                this.handleAreaMeasurement();
                break;
            case 'volume':
                this.handleVolumeMeasurement();
                break;
            case 'angle':
                this.handleAngleMeasurement();
                break;
        }
    }

    /**
     * 创建点标记
     */
    createPointMarker(point) {
        const geometry = new THREE.SphereGeometry(2, 8, 6);
        const marker = new THREE.Mesh(geometry, this.materials.point);
        marker.position.copy(point);
        
        this.measurementGroup.add(marker);
        
        // 添加点标签
        this.createPointLabel(point, this.points.length);
    }

    /**
     * 创建点标签
     */
    createPointLabel(point, index) {
        // 简化的文本标签（实际实现可能需要使用文本几何体或HTML元素）
        const labelGeometry = new THREE.PlaneGeometry(10, 5);
        const label = new THREE.Mesh(labelGeometry, this.materials.text);
        label.position.copy(point);
        label.position.y += 10;
        label.lookAt(this.camera.position);
        
        this.measurementGroup.add(label);
    }

    /**
     * 处理距离测量
     */
    handleDistanceMeasurement() {
        if (this.points.length === 2) {
            const distance = this.points[0].distanceTo(this.points[1]);
            this.createDistanceLine(this.points[0], this.points[1], distance);
            this.completeMeasurement();
        } else if (this.points.length === 1) {
            // 等待第二个点
            console.log('📏 请点击第二个点完成距离测量');
        }
    }

    /**
     * 处理面积测量
     */
    handleAreaMeasurement() {
        if (this.points.length >= 3) {
            // 可以完成面积测量
            console.log('📐 按Enter键完成面积测量，或继续添加点');
        }
    }

    /**
     * 处理体积测量
     */
    handleVolumeMeasurement() {
        if (this.points.length >= 4) {
            console.log('📦 按Enter键完成体积测量，或继续添加点');
        }
    }

    /**
     * 处理角度测量
     */
    handleAngleMeasurement() {
        if (this.points.length === 3) {
            const angle = this.calculateAngle(this.points[0], this.points[1], this.points[2]);
            this.createAngleMarker(this.points[0], this.points[1], this.points[2], angle);
            this.completeMeasurement();
        }
    }

    /**
     * 创建距离线
     */
    createDistanceLine(point1, point2, distance) {
        // 创建线条
        const geometry = new THREE.BufferGeometry().setFromPoints([point1, point2]);
        const line = new THREE.Line(geometry, this.materials.line);
        this.measurementGroup.add(line);
        
        // 创建距离标签
        const midPoint = point1.clone().add(point2).multiplyScalar(0.5);
        this.createDistanceLabel(midPoint, distance);
        
        // 保存测量结果
        this.saveMeasurement({
            type: 'distance',
            points: [point1.clone(), point2.clone()],
            value: distance,
            unit: 'm',
            timestamp: Date.now()
        });
    }

    /**
     * 创建距离标签
     */
    createDistanceLabel(position, distance) {
        // 简化的距离标签
        const text = `${distance.toFixed(2)}m`;
        console.log(`📏 距离测量结果: ${text}`);
        
        // 这里可以创建3D文本或HTML标签
        // 实际实现中建议使用Three.js的文本几何体或HTML覆盖层
    }

    /**
     * 计算角度
     */
    calculateAngle(point1, vertex, point2) {
        const vec1 = point1.clone().sub(vertex).normalize();
        const vec2 = point2.clone().sub(vertex).normalize();
        const angle = vec1.angleTo(vec2);
        return (angle * 180) / Math.PI; // 转换为度
    }

    /**
     * 创建角度标记
     */
    createAngleMarker(point1, vertex, point2, angle) {
        // 创建角度弧线
        const radius = 20;
        const vec1 = point1.clone().sub(vertex).normalize();
        const vec2 = point2.clone().sub(vertex).normalize();
        
        // 简化的角度显示
        console.log(`📐 角度测量结果: ${angle.toFixed(1)}°`);
        
        this.saveMeasurement({
            type: 'angle',
            points: [point1.clone(), vertex.clone(), point2.clone()],
            value: angle,
            unit: '°',
            timestamp: Date.now()
        });
    }

    /**
     * 保存测量结果
     */
    saveMeasurement(measurement) {
        measurement.id = `measurement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.measurements.push(measurement);
        
        console.log(`💾 测量结果已保存:`, measurement);
        this.emit('measurementSaved', measurement);
    }

    /**
     * 完成当前测量
     */
    completeMeasurement() {
        if (this.measurementMode === 'area' && this.points.length >= 3) {
            this.createAreaMeasurement();
        } else if (this.measurementMode === 'volume' && this.points.length >= 4) {
            this.createVolumeMeasurement();
        }
        
        // 重置状态
        this.points = [];
        this.currentMeasurement = null;
        
        console.log('✅ 测量完成');
    }

    /**
     * 创建面积测量
     */
    createAreaMeasurement() {
        if (this.points.length < 3) return;
        
        // 计算多边形面积
        const area = this.calculatePolygonArea(this.points);
        
        // 创建面积显示
        const geometry = new THREE.BufferGeometry().setFromPoints([...this.points, this.points[0]]);
        const line = new THREE.Line(geometry, this.materials.line);
        this.measurementGroup.add(line);
        
        // 创建填充面（如果点在同一平面）
        if (this.arePointsCoplanar(this.points)) {
            const shape = this.createShapeFromPoints(this.points);
            const fillGeometry = new THREE.ShapeGeometry(shape);
            const fill = new THREE.Mesh(fillGeometry, this.materials.area);
            this.measurementGroup.add(fill);
        }
        
        console.log(`📐 面积测量结果: ${area.toFixed(2)}m²`);
        
        this.saveMeasurement({
            type: 'area',
            points: this.points.map(p => p.clone()),
            value: area,
            unit: 'm²',
            timestamp: Date.now()
        });
    }

    /**
     * 计算多边形面积
     */
    calculatePolygonArea(points) {
        if (points.length < 3) return 0;
        
        let area = 0;
        for (let i = 0; i < points.length; i++) {
            const j = (i + 1) % points.length;
            area += points[i].x * points[j].z;
            area -= points[j].x * points[i].z;
        }
        return Math.abs(area) / 2;
    }

    /**
     * 检查点是否共面
     */
    arePointsCoplanar(points) {
        if (points.length < 4) return true;
        
        // 简化检查：假设所有点的Y坐标相近
        const firstY = points[0].y;
        const tolerance = 1.0; // 1米容差
        
        return points.every(point => Math.abs(point.y - firstY) < tolerance);
    }

    /**
     * 从点创建形状
     */
    createShapeFromPoints(points) {
        const shape = new THREE.Shape();
        
        if (points.length > 0) {
            shape.moveTo(points[0].x, points[0].z);
            for (let i = 1; i < points.length; i++) {
                shape.lineTo(points[i].x, points[i].z);
            }
        }
        
        return shape;
    }

    /**
     * 创建体积测量
     */
    createVolumeMeasurement() {
        // 简化的体积计算（假设为凸多面体）
        const volume = this.calculateConvexHullVolume(this.points);
        
        console.log(`📦 体积测量结果: ${volume.toFixed(2)}m³`);
        
        this.saveMeasurement({
            type: 'volume',
            points: this.points.map(p => p.clone()),
            value: volume,
            unit: 'm³',
            timestamp: Date.now()
        });
    }

    /**
     * 计算凸包体积（简化版）
     */
    calculateConvexHullVolume(points) {
        // 这是一个简化的体积计算
        // 实际实现应该使用更精确的凸包算法
        if (points.length < 4) return 0;
        
        // 使用四面体体积公式的简化版本
        const base = this.calculatePolygonArea(points.slice(0, 3));
        const height = Math.abs(points[3].y - points[0].y);
        
        return (base * height) / 3;
    }

    /**
     * 更新预览
     */
    updatePreview(point) {
        // 清理之前的预览
        this.clearPreview();
        
        if (this.points.length > 0) {
            // 创建预览线
            const lastPoint = this.points[this.points.length - 1];
            const previewGeometry = new THREE.BufferGeometry().setFromPoints([lastPoint, point]);
            const previewLine = new THREE.Line(previewGeometry, this.materials.line);
            previewLine.name = 'preview';
            this.measurementGroup.add(previewLine);
        }
    }

    /**
     * 清理预览
     */
    clearPreview() {
        const preview = this.measurementGroup.getObjectByName('preview');
        if (preview) {
            this.measurementGroup.remove(preview);
        }
    }

    /**
     * 取消当前测量
     */
    cancelCurrentMeasurement() {
        this.points = [];
        this.currentMeasurement = null;
        this.clearPreview();
        
        console.log('❌ 当前测量已取消');
    }

    /**
     * 删除最后一个点
     */
    deleteLastPoint() {
        if (this.points.length > 0) {
            this.points.pop();
            
            // 重新绘制
            this.redrawCurrentMeasurement();
            
            console.log('🗑️ 已删除最后一个测量点');
        }
    }

    /**
     * 重新绘制当前测量
     */
    redrawCurrentMeasurement() {
        // 清理当前测量的视觉元素
        // 重新创建基于当前点的视觉元素
        // 这里需要更复杂的实现来跟踪和重绘
    }

    /**
     * 清理所有测量
     */
    clearAllMeasurements() {
        this.measurementGroup.clear();
        this.measurements = [];
        this.points = [];
        this.currentMeasurement = null;
        
        console.log('🧹 所有测量已清理');
        this.emit('allMeasurementsCleared');
    }

    /**
     * 获取测量结果
     */
    getMeasurements() {
        return this.measurements;
    }

    /**
     * 导出测量结果
     */
    exportMeasurements() {
        const data = {
            timestamp: Date.now(),
            measurements: this.measurements,
            summary: this.getMeasurementSummary()
        };
        
        const dataStr = JSON.stringify(data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `measurements_${Date.now()}.json`;
        link.click();
        
        console.log('📤 测量结果已导出');
        this.emit('measurementsExported', data);
    }

    /**
     * 获取测量摘要
     */
    getMeasurementSummary() {
        const summary = {
            total: this.measurements.length,
            byType: {}
        };
        
        this.measurements.forEach(measurement => {
            if (!summary.byType[measurement.type]) {
                summary.byType[measurement.type] = 0;
            }
            summary.byType[measurement.type]++;
        });
        
        return summary;
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`测量工具事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.clearAllMeasurements();
        this.scene.remove(this.measurementGroup);
        
        // 清理材质
        Object.values(this.materials).forEach(material => {
            material.dispose();
        });
        
        this.eventListeners.clear();
        console.log('🧹 测量工具资源已清理');
    }
}
