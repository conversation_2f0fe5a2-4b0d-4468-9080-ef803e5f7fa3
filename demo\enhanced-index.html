<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版路桥修建演示系统</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui.css">
    <link rel="stylesheet" href="styles/enhanced-ui.css">
    
    <!-- 外部字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 主容器 -->
    <div id="container">
        <!-- 3D渲染画布 -->
        <canvas id="three-canvas"></canvas>
        
        <!-- 顶部导航栏 -->
        <header class="top-nav">
            <div class="nav-left">
                <h1><i class="fas fa-bridge"></i> 增强版路桥演示系统</h1>
            </div>
            <div class="nav-center">
                <div class="project-info">
                    <span class="project-name" id="current-project-name">请选择项目</span>
                    <span class="project-status" id="current-project-status">待加载</span>
                </div>
            </div>
            <div class="nav-right">
                <button class="nav-btn" id="save-project-btn" title="保存项目 (Ctrl+S)">
                    <i class="fas fa-save"></i>
                </button>
                <button class="nav-btn" id="fullscreen-btn" title="全屏">
                    <i class="fas fa-expand"></i>
                </button>
                <button class="nav-btn" id="settings-btn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
        </header>

        <!-- 左侧控制面板 -->
        <aside class="left-panel">
            <!-- 项目选择器将通过JavaScript动态插入 -->
            
            <!-- 模型管理器将通过JavaScript动态插入 -->
            
            <div class="panel-section">
                <h3><i class="fas fa-eye"></i> 视图控制</h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview">
                        <i class="fas fa-globe"></i>
                        <span>总览视图</span>
                    </button>
                    <button class="view-btn" data-view="bridge">
                        <i class="fas fa-bridge"></i>
                        <span>桥梁视图</span>
                    </button>
                    <button class="view-btn" data-view="road">
                        <i class="fas fa-road"></i>
                        <span>道路视图</span>
                    </button>
                    <button class="view-btn" data-view="construction">
                        <i class="fas fa-hard-hat"></i>
                        <span>施工视图</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-layer-group"></i> 图层管理</h3>
                <div class="layer-controls">
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="bridge">
                        <span class="checkmark"></span>
                        <span>桥梁结构</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="road">
                        <span class="checkmark"></span>
                        <span>道路系统</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="terrain">
                        <span class="checkmark"></span>
                        <span>地形地貌</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" data-layer="equipment">
                        <span class="checkmark"></span>
                        <span>施工设备</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" data-layer="foundation">
                        <span class="checkmark"></span>
                        <span>基础结构</span>
                    </label>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-palette"></i> 渲染设置</h3>
                <div class="render-controls">
                    <div class="control-group">
                        <label>光照强度</label>
                        <input type="range" id="light-intensity" min="0" max="2" step="0.1" value="1">
                    </div>
                    <div class="control-group">
                        <label>阴影质量</label>
                        <select id="shadow-quality">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>雾效强度</label>
                        <input type="range" id="fog-density" min="0" max="0.01" step="0.001" value="0.002">
                    </div>
                </div>
            </div>
        </aside>

        <!-- 右侧信息面板 -->
        <aside class="right-panel">
            <!-- 进度控制面板将通过JavaScript动态插入 -->
            
            <!-- 阶段管理器将通过JavaScript动态插入 -->
            
            <div class="panel-section">
                <h3><i class="fas fa-info-circle"></i> 项目信息</h3>
                <div class="info-grid" id="project-info-grid">
                    <div class="info-item">
                        <span class="label">项目状态</span>
                        <span class="value" id="project-status-value">未选择</span>
                    </div>
                    <div class="info-item">
                        <span class="label">模型数量</span>
                        <span class="value" id="model-count-value">0</span>
                    </div>
                    <div class="info-item">
                        <span class="label">总体进度</span>
                        <span class="value" id="overall-progress-value">0%</span>
                    </div>
                    <div class="info-item">
                        <span class="label">当前阶段</span>
                        <span class="value" id="current-phase-value">无</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-chart-line"></i> 施工进度</h3>
                <div class="progress-list">
                    <div class="progress-item">
                        <span class="task-name">基础施工</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 85%"></div>
                        </div>
                        <span class="progress-text">85%</span>
                    </div>
                    <div class="progress-item">
                        <span class="task-name">上部结构</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%"></div>
                        </div>
                        <span class="progress-text">60%</span>
                    </div>
                    <div class="progress-item">
                        <span class="task-name">装饰装修</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 30%"></div>
                        </div>
                        <span class="progress-text">30%</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-tools"></i> 快速操作</h3>
                <div class="quick-actions">
                    <button class="action-btn" id="screenshot-btn">
                        <i class="fas fa-camera"></i>
                        <span>截图</span>
                    </button>
                    <button class="action-btn" id="record-btn">
                        <i class="fas fa-video"></i>
                        <span>录制</span>
                    </button>
                    <button class="action-btn" id="measure-btn">
                        <i class="fas fa-ruler"></i>
                        <span>测量</span>
                    </button>
                    <button class="action-btn" id="export-btn">
                        <i class="fas fa-download"></i>
                        <span>导出</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- 底部状态栏 -->
        <footer class="bottom-status">
            <div class="status-left">
                <span class="status-item">
                    <i class="fas fa-mouse-pointer"></i>
                    <span id="cursor-position">X: 0, Y: 0, Z: 0</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-eye"></i>
                    <span id="camera-position">相机: 总览视图</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-folder"></i>
                    <span id="current-project-display">项目: 未选择</span>
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span id="fps-counter">FPS: 60</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-cube"></i>
                    <span id="object-count">对象: 0</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-memory"></i>
                    <span id="memory-usage">内存: 0MB</span>
                </span>
            </div>
        </footer>

        <!-- 加载界面 -->
        <div id="loading-screen">
            <div class="loading-content">
                <div class="loading-logo">
                    <i class="fas fa-bridge"></i>
                </div>
                <h2>增强版路桥演示系统</h2>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p class="loading-text">正在初始化系统...</p>
            </div>
        </div>
    </div>

    <!-- Three.js 和相关库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/0.158.0/three.min.js"></script>
    <script>
        // Three.js加载检查和备用CDN逻辑（与原版相同）
        window.addEventListener('load', function() {
            if (typeof THREE === 'undefined') {
                console.error('❌ Three.js主库加载失败，尝试备用CDN');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/three@0.158.0/build/three.min.js';
                script.onload = function() {
                    console.log('✅ Three.js从备用CDN加载成功');
                    loadExtensions();
                };
                script.onerror = function() {
                    console.error('❌ 所有CDN都无法加载Three.js');
                    showLoadError();
                };
                document.head.appendChild(script);
            } else {
                console.log('✅ Three.js主库加载成功，版本:', THREE.REVISION);
                loadExtensions();
            }
        });

        // 加载Three.js扩展（使用ES模块）
        async function loadExtensions() {
            try {
                // 动态导入ES模块
                const [
                    OrbitControlsModule,
                    GLTFLoaderModule,
                    DRACOLoaderModule
                ] = await Promise.all([
                    import('https://unpkg.com/three@0.158.0/examples/jsm/controls/OrbitControls.js'),
                    import('https://unpkg.com/three@0.158.0/examples/jsm/loaders/GLTFLoader.js'),
                    import('https://unpkg.com/three@0.158.0/examples/jsm/loaders/DRACOLoader.js')
                ]);

                // 将类添加到全局THREE对象
                window.THREE.OrbitControls = OrbitControlsModule.OrbitControls;
                window.THREE.GLTFLoader = GLTFLoaderModule.GLTFLoader;
                window.THREE.DRACOLoader = DRACOLoaderModule.DRACOLoader;

                console.log('✅ 所有Three.js扩展加载完成');
                window.threeExtensionsLoaded = true;
            } catch (error) {
                console.error('❌ Three.js扩展加载失败:', error);
                // 尝试使用传统方式加载
                loadExtensionsLegacy();
            }
        }

        // 备用：传统方式加载扩展
        function loadExtensionsLegacy() {
            const extensions = [
                'https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/controls/OrbitControls.js',
                'https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/loaders/GLTFLoader.js',
                'https://cdn.jsdelivr.net/npm/three@0.158.0/examples/js/loaders/DRACOLoader.js'
            ];

            let loadedCount = 0;

            extensions.forEach((url, index) => {
                const script = document.createElement('script');
                script.src = url;
                script.onload = function() {
                    loadedCount++;
                    console.log(`✅ 扩展加载成功 (${loadedCount}/${extensions.length}): ${url.split('/').pop()}`);

                    if (loadedCount === extensions.length) {
                        console.log('✅ 所有Three.js扩展加载完成（传统方式）');
                        window.threeExtensionsLoaded = true;
                    }
                };
                script.onerror = function() {
                    console.warn(`⚠️ 扩展加载失败: ${url.split('/').pop()}`);
                    loadedCount++;

                    if (loadedCount === extensions.length) {
                        console.log('⚠️ 扩展加载完成（部分失败）');
                        window.threeExtensionsLoaded = true;
                    }
                };
                document.head.appendChild(script);
            });
        }

        // 显示加载错误
        function showLoadError() {
            document.body.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                            background: #2c3e50; color: white; display: flex;
                            align-items: center; justify-content: center; z-index: 10000;">
                    <div style="text-align: center; max-width: 600px; padding: 40px;">
                        <h2>❌ 资源加载失败</h2>
                        <p>无法从任何CDN加载Three.js库，可能是网络连接问题。</p>
                        <p>请检查网络连接后重试。</p>
                        <button onclick="location.reload()"
                                style="margin-top: 20px; padding: 10px 20px;
                                       background: #3498db; color: white; border: none;
                                       border-radius: 5px; cursor: pointer;">
                            重新加载
                        </button>
                    </div>
                </div>
            `;
        }
    </script>
    
    <!-- 原有系统模块 -->
    <script src="js/materials.js"></script>
    <script src="js/geometry.js"></script>
    <script src="js/scene.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/ui.js"></script>
    
    <!-- 增强版系统模块 -->
    <script src="js/managers/project-manager.js"></script>
    <script src="js/managers/model-loader.js"></script>
    <script src="js/managers/progress-manager.js"></script>
    <script src="js/managers/enhanced-scene.js"></script>
    <script src="js/ui/project-ui.js"></script>
    <script src="js/ui/progress-ui.js"></script>
    
    <!-- 主应用程序 -->
    <script src="js/enhanced-app.js"></script>
    
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM加载完成，等待Three.js...');
            
            // 添加键盘快捷键提示
            console.log(`
🎮 键盘快捷键:
- Ctrl+S: 保存项目
- Ctrl+O: 打开项目选择器
- 空格键: 播放/暂停进度动画
- 数字键1-9: 快速切换施工阶段
- F11: 全屏模式
            `);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('🚨 全局错误:', event.error);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('🚨 未处理的Promise拒绝:', event.reason);
        });
    </script>
</body>
</html>
