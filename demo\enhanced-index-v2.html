<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路桥修建演示系统 - V2 增强版</title>
    
    <!-- 基础样式 -->
    <link rel="stylesheet" href="styles/enhanced-ui.css">
    
    <style>
        /* V2版本专用样式 */
        .v2-header {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.5s ease;
        }
        
        .loading-logo {
            font-size: 48px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }
        
        .loading-title {
            font-size: 24px;
            margin-bottom: 40px;
            text-align: center;
        }
        
        .loading-progress-container {
            width: 300px;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .loading-text {
            font-size: 14px;
            opacity: 0.8;
            text-align: center;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .version-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        .debug-panel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 11px;
            z-index: 1000;
            max-width: 200px;
        }
        
        .debug-panel.hidden {
            display: none;
        }
        
        /* 场景容器 */
        #scene-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            overflow: hidden;
        }
        
        #three-canvas {
            width: 100%;
            height: 100%;
            display: block;
        }

        /* 主容器布局 */
        #main-container {
            position: relative;
            z-index: 1;
            pointer-events: none;
        }

        /* 工具栏样式 */
        .toolbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            pointer-events: all;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }

        .toolbar-section {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0 10px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
        }

        .toolbar-section:last-child {
            border-right: none;
        }

        .toolbar-section label {
            color: #ecf0f1;
            font-size: 12px;
            font-weight: 500;
        }

        /* 按钮样式 */
        .icon-btn {
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(52, 152, 219, 0.4);
            color: #3498db;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            min-width: 40px;
            text-align: center;
        }

        .icon-btn:hover {
            background: rgba(52, 152, 219, 0.3);
            border-color: #3498db;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .toggle-btn {
            background: rgba(127, 140, 141, 0.2);
            border: 1px solid rgba(127, 140, 141, 0.4);
            color: #7f8c8d;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            min-width: 40px;
            text-align: center;
        }

        .toggle-btn.active {
            background: rgba(46, 204, 113, 0.2);
            border-color: #2ecc71;
            color: #2ecc71;
        }

        /* 测量工具特定样式 */
        .measurement-tool-btn.active {
            background: rgba(231, 76, 60, 0.3);
            border-color: #e74c3c;
            color: #e74c3c;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
            100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
        }

        .toggle-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* 下拉框样式 */
        .project-dropdown {
            background: rgba(44, 62, 80, 0.9);
            border: 1px solid rgba(52, 152, 219, 0.4);
            color: #ecf0f1;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            min-width: 200px;
            transition: all 0.3s ease;
        }

        .project-dropdown:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .project-dropdown option {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 8px;
        }

        /* 侧边面板 */
        .side-panel {
            position: fixed;
            top: 80px;
            width: 300px;
            background: rgba(0, 0, 0, 0.85);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            z-index: 1000;
            pointer-events: all;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
            max-height: calc(100vh - 200px);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        #left-panel {
            left: 20px;
        }

        #right-panel {
            right: 20px;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(52, 152, 219, 0.1);
        }

        .panel-header h3 {
            margin: 0;
            color: #ecf0f1;
            font-size: 16px;
            font-weight: 600;
        }

        .panel-toggle {
            background: none;
            border: none;
            color: #3498db;
            cursor: pointer;
            font-size: 16px;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .panel-toggle:hover {
            background: rgba(52, 152, 219, 0.2);
        }

        .panel-content {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        /* 进度控制样式 */
        .progress-section {
            margin-bottom: 20px;
        }

        .progress-section h4 {
            color: #ecf0f1;
            margin: 0 0 10px 0;
            font-size: 14px;
        }

        .progress-bar {
            position: relative;
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            right: 8px;
            transform: translateY(-50%);
            color: #ecf0f1;
            font-size: 11px;
            font-weight: 600;
        }

        /* 播放控制 */
        .playback-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(52, 152, 219, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(52, 152, 219, 0.2);
        }

        .control-btn {
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(52, 152, 219, 0.4);
            color: #3498db;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            min-width: 40px;
        }

        .control-btn:hover {
            background: rgba(52, 152, 219, 0.3);
            transform: scale(1.05);
        }

        /* 阶段列表 */
        .phase-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .phase-item:hover {
            background: rgba(52, 152, 219, 0.1);
            border-color: rgba(52, 152, 219, 0.3);
            transform: translateY(-1px);
        }

        .phase-item.active {
            background: rgba(52, 152, 219, 0.2);
            border-color: #3498db;
        }

        .phase-name {
            color: #ecf0f1;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .phase-progress {
            position: relative;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
        }

        .phase-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c, #f39c12, #2ecc71);
            border-radius: 3px;
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 状态栏 */
        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
            pointer-events: all;
        }

        .status-left, .status-center, .status-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-bar span {
            color: #bdc3c7;
            font-size: 12px;
            font-weight: 500;
        }

        /* 项目信息样式 */
        .project-info-item {
            margin-bottom: 15px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            border-left: 3px solid #3498db;
        }

        .project-info-label {
            color: #7f8c8d;
            font-size: 12px;
            margin-bottom: 4px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .project-info-value {
            color: #ecf0f1;
            font-size: 14px;
            font-weight: 500;
        }

        /* 滑块样式 */
        input[type="range"] {
            appearance: none;
            background: transparent;
            cursor: pointer;
            flex: 1;
        }

        input[type="range"]::-webkit-slider-track {
            background: rgba(255, 255, 255, 0.2);
            height: 4px;
            border-radius: 2px;
        }

        input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            background: #3498db;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            border: 2px solid #2980b9;
            cursor: pointer;
        }

        /* 可拖拽面板 */
        .panel-draggable {
            cursor: move;
        }

        .panel-dragging {
            opacity: 0.8;
            transform: scale(1.02);
            z-index: 9999 !important;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .side-panel {
                width: 280px;
            }
        }

        @media (max-width: 768px) {
            .toolbar {
                flex-direction: column;
                gap: 10px;
                padding: 15px;
            }
            
            .side-panel {
                width: calc(100vw - 40px);
                left: 20px;
                right: 20px;
            }
            
            #right-panel {
                top: 400px;
            }
        }
    </style>
</head>
<body>
    <!-- V2版本标识 -->
    <div class="v2-header">
        路桥演示系统 V2.0
    </div>
    
    <!-- 加载屏幕 -->
    <div class="loading-screen">
        <div class="loading-logo">🌉</div>
        <div class="loading-title">
            增强版路桥修建演示系统<br>
            <small>Enhanced Bridge Construction Demo System V2</small>
        </div>
        <div class="loading-progress-container">
            <div class="loading-progress"></div>
        </div>
        <div class="loading-text">正在初始化系统...</div>
    </div>
    
    <!-- 主容器 -->
    <div id="main-container">
        <!-- 3D场景容器 -->
        <div id="scene-container"></div>
        
        <!-- 顶部工具栏 -->
        <div id="top-toolbar" class="toolbar">
            <!-- 项目选择 -->
            <div class="toolbar-section">
                <label for="project-selector">项目:</label>
                <select id="project-selector" class="project-dropdown">
                    <option value="">加载中...</option>
                </select>
                <button id="refresh-projects" class="icon-btn" title="刷新项目列表">🔄</button>
            </div>
            
            <!-- 视图控制 -->
            <div class="toolbar-section">
                <button id="reset-camera" class="icon-btn" title="重置相机">📷</button>
                <button id="fullscreen" class="icon-btn" title="全屏">🔍</button>
                <button id="screenshot" class="icon-btn" title="截图">📸</button>
            </div>
            
            <!-- 工具 -->
            <div class="toolbar-section">
                <label>工具:</label>
                <button id="measurement-tool" class="toggle-btn measurement-tool-btn" title="测量工具" onclick="toggleMeasurement()">📐</button>
                <button id="show-measurements" class="icon-btn" title="查看测量结果" onclick="showMeasurements()">📊</button>
                <button id="clear-measurements" class="icon-btn" title="清理测量" onclick="clearMeasurements()">🧹</button>
            </div>
            
            <!-- 显示控制 -->
            <div class="toolbar-section">
                <label>显示:</label>
                <button id="toggle-grid" class="toggle-btn active" title="网格">📏</button>
                <button id="toggle-axes" class="toggle-btn active" title="坐标轴">⚡</button>
                <button id="toggle-shadows" class="toggle-btn active" title="阴影">🌓</button>
            </div>
        </div>
        
        <!-- 左侧面板 -->
        <div id="left-panel" class="side-panel">
            <div class="panel-header">
                <h3>项目信息</h3>
                <button class="panel-toggle">📌</button>
            </div>
            <div class="panel-content">
                <div id="project-info">
                    <p>请选择一个项目</p>
                </div>
            </div>
        </div>
        
        <!-- 右侧面板 -->
        <div id="right-panel" class="side-panel">
            <div class="panel-header">
                <h3>进度控制</h3>
                <button class="panel-toggle">📌</button>
            </div>
            <div class="panel-content">
                <!-- 总体进度 -->
                <div class="progress-section">
                    <h4>总体进度</h4>
                    <div class="progress-bar">
                        <div id="overall-progress" class="progress-fill"></div>
                        <span id="overall-percentage" class="progress-text">0%</span>
                    </div>
                </div>
                
                <!-- 播放控制 -->
                <div class="playback-controls">
                    <button id="play-btn" class="control-btn">▶️</button>
                    <button id="pause-btn" class="control-btn">⏸️</button>
                    <button id="reset-btn" class="control-btn">🔄</button>
                    <input id="speed-slider" type="range" min="0.5" max="3" step="0.5" value="1">
                    <span id="speed-display">1x</span>
                </div>
                
                <!-- 阶段列表 -->
                <div id="phases-list">
                    <!-- 动态生成 -->
                </div>
            </div>
        </div>
        
        <!-- 底部状态栏 -->
        <div id="bottom-status" class="status-bar">
            <div class="status-left">
                <span id="fps-counter">FPS: --</span>
                <span id="model-count">模型: --</span>
                <span id="triangle-count">三角形: --</span>
            </div>
            <div class="status-center">
                <span id="current-phase">当前阶段: --</span>
            </div>
            <div class="status-right">
                <span id="memory-usage">内存: --</span>
                <span id="load-status">就绪</span>
            </div>
        </div>
    </div>
    
    <!-- 调试面板 -->
    <div id="debug-panel" class="debug-panel hidden">
        <div><strong>调试信息</strong></div>
        <div>FPS: <span id="debug-fps">--</span></div>
        <div>内存: <span id="debug-memory">--</span></div>
        <div>状态: <span id="debug-state">--</span></div>
        <div>服务: <span id="debug-services">--</span></div>
    </div>
    
    <!-- 版本信息 -->
    <div class="version-info">
        <div>增强版 V2.0</div>
        <div>基于服务架构</div>
        <div>配置驱动模型</div>
    </div>
    
    <!-- Three.js 核心库 -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    
    <!-- Three.js 扩展 -->
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/DRACOLoader.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    
    <!-- 标记Three.js扩展已加载 -->
    <script>
        window.threeExtensionsLoaded = true;
        console.log('✅ Three.js扩展已加载');
    </script>
    
    <!-- 新架构核心 -->
    <script src="js/core/application-core.js"></script>
    
    <!-- 服务层 -->
    <script src="js/services/project-service.js"></script>
    <script src="js/services/model-service.js"></script>
    <script src="js/services/scene-service.js"></script>
    <script src="js/services/progress-service.js"></script>
    <script src="js/services/tool-service.js"></script>
    
    <!-- 原有组件（兼容性） -->
    <script src="js/materials.js"></script>
    <script src="js/geometry.js"></script>
    <script src="js/scene.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/ui.js"></script>
    
    <!-- 原有管理器（兼容性） -->
    <script src="js/managers/enhanced-scene.js"></script>
    <script src="js/managers/model-loader.js"></script>
    <script src="js/managers/progress-manager.js"></script>
    <script src="js/managers/project-manager.js"></script>
    <script src="js/managers/tool-manager.js"></script>
    
    <!-- 工具系统 -->
    <script src="js/tools/measurement-tool.js"></script>
    
    <!-- UI组件 -->
    <script src="js/ui/project-ui.js"></script>
    <script src="js/ui/progress-ui.js"></script>
    
    <!-- 新版主应用程序 -->
    <script src="js/enhanced-app-v2.js"></script>
    
    <!-- 调试和监控 -->
    <script>
        // 开发环境调试功能
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            // 显示调试面板
            document.addEventListener('keydown', (e) => {
                if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'D')) {
                    e.preventDefault();
                    const debugPanel = document.getElementById('debug-panel');
                    debugPanel.classList.toggle('hidden');
                }
            });
            
            // 更新调试信息
            setInterval(() => {
                if (window.debugBridgeV2) {
                    const stats = window.debugBridgeV2.getSystemInfo();
                    document.getElementById('debug-fps').textContent = stats.performance?.fps || '--';
                    document.getElementById('debug-memory').textContent = 
                        stats.memoryUsage ? `${stats.memoryUsage.used}MB` : '--';
                    document.getElementById('debug-state').textContent = stats.coreState?.state || '--';
                    document.getElementById('debug-services').textContent = 
                        stats.coreState?.services?.length || '--';
                }
            }, 1000);
            
            console.log('🔧 开发模式已启用');
            console.log('   - 按 Ctrl+Shift+D 切换调试面板');
            console.log('   - 使用 window.debugBridgeV2 访问调试功能');
        }
        
        // 性能监控
        let lastFrameTime = performance.now();
        let frameCount = 0;
        
        function updatePerformanceStats() {
            frameCount++;
            const now = performance.now();
            
            if (now - lastFrameTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (now - lastFrameTime));
                
                // 更新状态栏
                document.getElementById('fps-counter').textContent = `FPS: ${fps}`;
                
                frameCount = 0;
                lastFrameTime = now;
            }
            
            requestAnimationFrame(updatePerformanceStats);
        }
        
        // 启动性能监控
        requestAnimationFrame(updatePerformanceStats);
        
        console.log('📊 性能监控已启动');
        
        // 初始化UI交互
        initializeUIInteractions();

        /**
         * 初始化UI交互功能
         */
        function initializeUIInteractions() {
            console.log('🎛️ 初始化UI交互功能');
            
            // 初始化工具栏功能
            initializeToolbar();
            
            // 初始化面板拖拽
            initializePanelDragging();
            
            // 初始化播放控制
            initializePlaybackControls();
            
            // 初始化面板切换
            initializePanelToggles();
            
            // 初始化状态更新
            initializeStatusUpdates();
            
            console.log('✅ UI交互功能初始化完成');
        }

        /**
         * 初始化工具栏功能
         */
        function initializeToolbar() {
            // 项目下拉框
            const projectSelector = document.getElementById('project-selector');
            if (projectSelector) {
                projectSelector.addEventListener('change', handleProjectChange);
                
                // 初始化项目列表
                updateProjectList();
            }
            
            // 刷新项目按钮
            const refreshBtn = document.getElementById('refresh-projects');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    refreshBtn.style.transform = 'rotate(360deg)';
                    setTimeout(() => {
                        refreshBtn.style.transform = '';
                        updateProjectList();
                    }, 500);
                });
            }
            
            // 相机重置
            const resetCameraBtn = document.getElementById('reset-camera');
            if (resetCameraBtn) {
                resetCameraBtn.addEventListener('click', resetCamera);
            }
            
            // 全屏切换
            const fullscreenBtn = document.getElementById('fullscreen');
            if (fullscreenBtn) {
                fullscreenBtn.addEventListener('click', toggleFullscreen);
            }
            
            // 截图功能
            const screenshotBtn = document.getElementById('screenshot');
            if (screenshotBtn) {
                screenshotBtn.addEventListener('click', takeScreenshot);
            }
            
            // 显示切换按钮
            setupDisplayToggles();
        }

        /**
         * 处理项目选择变化
         */
        async function handleProjectChange(event) {
            const projectId = event.target.value;
            if (!projectId) return;
            
            console.log(`📂 切换项目: ${projectId}`);
            
            try {
                if (window.debugBridgeV2) {
                    const projectService = await window.debugBridgeV2.getService('project');
                    await projectService.switchProject(projectId);
                    
                    // 更新项目信息显示
                    updateProjectInfo(projectService.getCurrentProject());
                    
                    showNotification('success', `项目已切换: ${projectId}`);
                }
            } catch (error) {
                console.error('项目切换失败:', error);
                showNotification('error', '项目切换失败');
            }
        }

        /**
         * 更新项目列表
         */
        async function updateProjectList() {
            const selector = document.getElementById('project-selector');
            if (!selector) return;
            
            try {
                if (window.debugBridgeV2) {
                    const projectService = await window.debugBridgeV2.getService('project');
                    const projects = projectService.getAvailableProjects();
                    
                    selector.innerHTML = '<option value="">选择项目...</option>';
                    
                    projects.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.id;
                        option.textContent = project.name;
                        selector.appendChild(option);
                    });
                    
                    // 选中当前项目
                    const currentProject = projectService.getCurrentProject();
                    if (currentProject) {
                        selector.value = currentProject.id;
                    }
                }
            } catch (error) {
                console.warn('更新项目列表失败:', error);
            }
        }

        /**
         * 更新项目信息显示
         */
        function updateProjectInfo(project) {
            const container = document.getElementById('project-info');
            if (!container || !project) return;
            
            container.innerHTML = `
                <div class="project-info-item">
                    <div class="project-info-label">项目名称</div>
                    <div class="project-info-value">${project.name}</div>
                </div>
                <div class="project-info-item">
                    <div class="project-info-label">项目类型</div>
                    <div class="project-info-value">${project.config.type || '未指定'}</div>
                </div>
                <div class="project-info-item">
                    <div class="project-info-label">描述</div>
                    <div class="project-info-value">${project.description || '暂无描述'}</div>
                </div>
                <div class="project-info-item">
                    <div class="project-info-label">阶段数量</div>
                    <div class="project-info-value">${project.config.phases?.length || 0} 个</div>
                </div>
                <div class="project-info-item">
                    <div class="project-info-label">规格</div>
                    <div class="project-info-value">
                        ${Object.entries(project.config.specifications || {})
                            .map(([key, value]) => `${key}: ${value}`)
                            .join('<br>') || '暂无规格信息'}
                    </div>
                </div>
            `;
        }

        /**
         * 重置相机
         */
        async function resetCamera() {
            try {
                if (window.debugBridgeV2) {
                    const app = window.debugBridgeV2;
                    if (app.sceneManager && app.sceneManager.controls) {
                        // 重置相机位置
                        const camera = app.sceneManager.getCamera();
                        const controls = app.sceneManager.controls;
                        
                        if (camera) {
                            camera.position.set(50, 50, 50);
                        }
                        
                        if (controls && controls.target) {
                            controls.target.set(0, 0, 0);
                        }
                        
                        if (controls && controls.update) {
                            controls.update();
                        }
                        
                        showNotification('success', '相机位置已重置');
                    }
                }
            } catch (error) {
                console.error('重置相机失败:', error);
                showNotification('error', '相机重置失败');
            }
        }

        /**
         * 切换全屏
         */
        function toggleFullscreen() {
            if (document.fullscreenElement) {
                document.exitFullscreen();
                showNotification('info', '已退出全屏');
            } else {
                document.documentElement.requestFullscreen();
                showNotification('info', '已进入全屏');
            }
        }

        /**
         * 截图功能 - 使用新的工具服务
         */
        async function takeScreenshot() {
            try {
                if (window.debugBridgeV2 && window.debugBridgeV2.takeScreenshot) {
                    await window.debugBridgeV2.takeScreenshot();
                    showNotification('success', '截图已保存');
                } else {
                    // 降级处理
                    const app = window.getEnhancedBridgeAppV2();
                    if (app && app.sceneManager) {
                        const canvas = app.sceneManager.getRenderer()?.domElement;
                        if (canvas) {
                            const link = document.createElement('a');
                            link.download = `bridge-screenshot-${Date.now()}.png`;
                            link.href = canvas.toDataURL();
                            link.click();
                            showNotification('success', '截图已保存');
                        }
                    }
                }
            } catch (error) {
                console.error('截图失败:', error);
                showNotification('error', '截图失败');
            }
        }

        /**
         * 测量工具功能
         */
        let measurementActive = false;
        
        async function toggleMeasurement() {
            try {
                if (window.debugBridgeV2) {
                    if (measurementActive) {
                        await window.debugBridgeV2.stopMeasurement();
                        measurementActive = false;
                        showNotification('info', '测量工具已停用');
                        
                        // 更新UI状态
                        const measureBtn = document.querySelector('.measurement-tool-btn');
                        if (measureBtn) {
                            measureBtn.classList.remove('active');
                        }
                    } else {
                        await window.debugBridgeV2.startMeasurement('distance');
                        measurementActive = true;
                        showNotification('info', '测量工具已激活 - 点击两个点测量距离');
                        
                        // 更新UI状态
                        const measureBtn = document.querySelector('.measurement-tool-btn');
                        if (measureBtn) {
                            measureBtn.classList.add('active');
                        }
                    }
                }
            } catch (error) {
                console.error('测量工具切换失败:', error);
                showNotification('error', '测量工具操作失败');
            }
        }

        /**
         * 清理所有测量
         */
        async function clearMeasurements() {
            try {
                if (window.debugBridgeV2) {
                    await window.debugBridgeV2.clearMeasurements();
                    showNotification('success', '所有测量已清理');
                }
            } catch (error) {
                console.error('清理测量失败:', error);
                showNotification('error', '清理测量失败');
            }
        }

        /**
         * 获取测量结果
         */
        async function showMeasurements() {
            try {
                if (window.debugBridgeV2) {
                    const measurements = await window.debugBridgeV2.getMeasurements();
                    if (measurements && measurements.length > 0) {
                        let message = `共有 ${measurements.length} 项测量结果:\n`;
                        measurements.forEach((measurement, index) => {
                            message += `${index + 1}. ${measurement.type}: ${measurement.value.toFixed(2)}${measurement.unit}\n`;
                        });
                        alert(message);
                    } else {
                        showNotification('info', '暂无测量结果');
                    }
                }
            } catch (error) {
                console.error('获取测量结果失败:', error);
                showNotification('error', '获取测量结果失败');
            }
        }

        /**
         * 设置显示切换按钮
         */
        function setupDisplayToggles() {
            const toggles = [
                { id: 'toggle-grid', name: '网格' },
                { id: 'toggle-axes', name: '坐标轴' },
                { id: 'toggle-shadows', name: '阴影' }
            ];
            
            toggles.forEach(toggle => {
                const btn = document.getElementById(toggle.id);
                if (btn) {
                    btn.addEventListener('click', () => {
                        btn.classList.toggle('active');
                        const isActive = btn.classList.contains('active');
                        
                        // 这里可以添加实际的显示切换逻辑
                        console.log(`${toggle.name} ${isActive ? '开启' : '关闭'}`);
                        showNotification('info', `${toggle.name} ${isActive ? '已开启' : '已关闭'}`);
                    });
                }
            });
        }

        /**
         * 初始化面板拖拽功能
         */
        function initializePanelDragging() {
            const panels = document.querySelectorAll('.side-panel');
            
            panels.forEach(panel => {
                const header = panel.querySelector('.panel-header');
                if (header) {
                    makeDraggable(panel, header);
                }
            });
        }

        /**
         * 使元素可拖拽
         */
        function makeDraggable(element, handle) {
            let isDragging = false;
            let startX, startY, startLeft, startTop;
            
            handle.addEventListener('mousedown', startDrag);
            handle.style.cursor = 'move';
            
            function startDrag(e) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                
                const rect = element.getBoundingClientRect();
                startLeft = rect.left;
                startTop = rect.top;
                
                element.classList.add('panel-dragging');
                
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', stopDrag);
                
                e.preventDefault();
            }
            
            function drag(e) {
                if (!isDragging) return;
                
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                const newLeft = Math.max(0, Math.min(window.innerWidth - element.offsetWidth, startLeft + deltaX));
                const newTop = Math.max(0, Math.min(window.innerHeight - element.offsetHeight, startTop + deltaY));
                
                element.style.left = newLeft + 'px';
                element.style.top = newTop + 'px';
                element.style.right = 'auto';
            }
            
            function stopDrag() {
                isDragging = false;
                element.classList.remove('panel-dragging');
                
                document.removeEventListener('mousemove', drag);
                document.removeEventListener('mouseup', stopDrag);
            }
        }

        /**
         * 初始化播放控制
         */
        function initializePlaybackControls() {
            const playBtn = document.getElementById('play-btn');
            const pauseBtn = document.getElementById('pause-btn');
            const resetBtn = document.getElementById('reset-btn');
            const speedSlider = document.getElementById('speed-slider');
            const speedDisplay = document.getElementById('speed-display');
            
            if (playBtn) {
                playBtn.addEventListener('click', () => {
                    console.log('▶️ 播放进度动画');
                    showNotification('info', '开始播放');
                });
            }
            
            if (pauseBtn) {
                pauseBtn.addEventListener('click', () => {
                    console.log('⏸️ 暂停进度动画');
                    showNotification('info', '已暂停');
                });
            }
            
            if (resetBtn) {
                resetBtn.addEventListener('click', () => {
                    console.log('🔄 重置进度');
                    const progressBars = document.querySelectorAll('.progress-fill, .phase-progress-fill');
                    progressBars.forEach(bar => {
                        bar.style.width = '0%';
                    });
                    showNotification('info', '进度已重置');
                });
            }
            
            if (speedSlider && speedDisplay) {
                speedSlider.addEventListener('input', (e) => {
                    const speed = e.target.value;
                    speedDisplay.textContent = speed + 'x';
                    console.log(`⚡ 速度设置: ${speed}x`);
                });
            }
        }

        /**
         * 初始化面板切换
         */
        function initializePanelToggles() {
            const toggles = document.querySelectorAll('.panel-toggle');
            
            toggles.forEach(toggle => {
                toggle.addEventListener('click', () => {
                    const panel = toggle.closest('.side-panel');
                    const content = panel.querySelector('.panel-content');
                    
                    if (content) {
                        const isVisible = content.style.display !== 'none';
                        content.style.display = isVisible ? 'none' : 'block';
                        toggle.textContent = isVisible ? '📂' : '📌';
                        
                        // 调整面板高度
                        if (isVisible) {
                            panel.style.height = 'auto';
                        }
                    }
                });
            });
        }

        /**
         * 初始化状态更新
         */
        function initializeStatusUpdates() {
            setInterval(() => {
                updateStatusBar();
            }, 1000);
        }

        /**
         * 更新状态栏
         */
        function updateStatusBar() {
            try {
                // 更新模型数量
                const modelCountElement = document.getElementById('model-count');
                if (modelCountElement && window.debugBridgeV2) {
                    // 这里可以从场景服务获取实际的模型数量
                    modelCountElement.textContent = '模型: --';
                }
                
                // 更新三角形数量
                const triangleCountElement = document.getElementById('triangle-count');
                if (triangleCountElement) {
                    triangleCountElement.textContent = '三角形: --';
                }
                
                // 更新内存使用
                const memoryElement = document.getElementById('memory-usage');
                if (memoryElement && window.debugBridgeV2) {
                    const info = window.debugBridgeV2.getSystemInfo();
                    if (info.memoryUsage) {
                        memoryElement.textContent = `内存: ${info.memoryUsage.used}MB`;
                    }
                }
                
                // 更新当前阶段
                const currentPhaseElement = document.getElementById('current-phase');
                if (currentPhaseElement) {
                    currentPhaseElement.textContent = '当前阶段: --';
                }
                
                // 更新加载状态
                const loadStatusElement = document.getElementById('load-status');
                if (loadStatusElement && window.debugBridgeV2) {
                    const info = window.debugBridgeV2.getSystemInfo();
                    loadStatusElement.textContent = info.coreState?.state || '未知';
                }
                
            } catch (error) {
                console.warn('更新状态栏失败:', error);
            }
        }

        /**
         * 显示通知
         */
        function showNotification(type, message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                right: 20px;
                padding: 12px 20px;
                background: ${type === 'error' ? '#e74c3c' : type === 'success' ? '#2ecc71' : '#3498db'};
                color: white;
                border-radius: 8px;
                z-index: 10000;
                font-size: 14px;
                font-weight: 500;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                transform: translateX(100%);
                transition: transform 0.3s ease;
                pointer-events: none;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 显示动画
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);
            
            // 自动隐藏
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        /**
         * 模拟阶段数据
         */
        function createMockPhases() {
            const phasesList = document.getElementById('phases-list');
            if (!phasesList) return;
            
            const phases = [
                { id: 'phase1', name: '基础施工', progress: 80, color: '#3498db' },
                { id: 'phase2', name: '主体建设', progress: 45, color: '#2ecc71' },
                { id: 'phase3', name: '装饰完善', progress: 10, color: '#e74c3c' }
            ];
            
            phasesList.innerHTML = phases.map(phase => `
                <div class="phase-item" data-phase="${phase.id}">
                    <div class="phase-name">${phase.name}</div>
                    <div class="phase-progress">
                        <div class="phase-progress-fill" style="width: ${phase.progress}%"></div>
                    </div>
                    <div style="text-align: right; margin-top: 8px; color: #bdc3c7; font-size: 12px;">
                        ${phase.progress}%
                    </div>
                </div>
            `).join('');
            
            // 添加点击事件
            phasesList.querySelectorAll('.phase-item').forEach(item => {
                item.addEventListener('click', () => {
                    // 移除其他活动状态
                    phasesList.querySelectorAll('.phase-item').forEach(i => i.classList.remove('active'));
                    // 添加当前活动状态
                    item.classList.add('active');
                    
                    const phaseId = item.dataset.phase;
                    const phaseName = item.querySelector('.phase-name').textContent;
                    
                    console.log(`切换到阶段: ${phaseName}`);
                    showNotification('info', `已选择: ${phaseName}`);
                });
            });
        }

        // 页面加载完成后初始化模拟数据
        setTimeout(() => {
            createMockPhases();
            updateProjectList();
        }, 2000);
    </script>
</body>
</html>