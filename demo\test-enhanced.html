<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #2c3e50;
            color: white;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .status.success { background: #27ae60; }
        .status.error { background: #e74c3c; }
        .status.warning { background: #f39c12; }
        .status.info { background: #3498db; }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        #log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 增强版路桥演示系统测试</h1>
        
        <div class="test-section">
            <h3>📋 系统状态检查</h3>
            <div id="status-checks"></div>
        </div>
        
        <div class="test-section">
            <h3>🔧 功能测试</h3>
            <button onclick="testProjectManager()">测试项目管理器</button>
            <button onclick="testModelLoader()">测试模型加载器</button>
            <button onclick="testProgressManager()">测试进度管理器</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div id="log"></div>
        </div>
    </div>

    <!-- Three.js 和相关库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/0.158.0/three.min.js"></script>
    
    <script>
        let testLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            testLog.push(logEntry);
            
            const logElement = document.getElementById('log');
            logElement.innerHTML = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }
        
        function clearLog() {
            testLog = [];
            document.getElementById('log').innerHTML = '';
        }
        
        function updateStatus(id, status, message) {
            const statusElement = document.getElementById(id);
            if (statusElement) {
                statusElement.className = `status ${status}`;
                statusElement.textContent = message;
            }
        }
        
        function createStatusCheck(id, label) {
            const container = document.getElementById('status-checks');
            const div = document.createElement('div');
            div.innerHTML = `<strong>${label}:</strong> <span id="${id}" class="status info">检查中...</span>`;
            container.appendChild(div);
        }
        
        // 初始化状态检查
        function initStatusChecks() {
            createStatusCheck('three-status', 'Three.js');
            createStatusCheck('gltf-status', 'GLTFLoader');
            createStatusCheck('draco-status', 'DRACOLoader');
            createStatusCheck('controls-status', 'OrbitControls');
        }
        
        // 检查Three.js加载状态
        function checkThreeJS() {
            if (typeof THREE !== 'undefined') {
                updateStatus('three-status', 'success', `已加载 (版本 ${THREE.REVISION})`);
                log('✅ Three.js加载成功');
                return true;
            } else {
                updateStatus('three-status', 'error', '未加载');
                log('❌ Three.js未加载');
                return false;
            }
        }
        
        // 检查扩展加载状态
        function checkExtensions() {
            // GLTFLoader
            if (typeof THREE.GLTFLoader !== 'undefined') {
                updateStatus('gltf-status', 'success', '已加载');
                log('✅ GLTFLoader加载成功');
            } else {
                updateStatus('gltf-status', 'error', '未加载');
                log('❌ GLTFLoader未加载');
            }
            
            // DRACOLoader
            if (typeof THREE.DRACOLoader !== 'undefined') {
                updateStatus('draco-status', 'success', '已加载');
                log('✅ DRACOLoader加载成功');
            } else {
                updateStatus('draco-status', 'warning', '未加载');
                log('⚠️ DRACOLoader未加载');
            }
            
            // OrbitControls
            if (typeof THREE.OrbitControls !== 'undefined') {
                updateStatus('controls-status', 'success', '已加载');
                log('✅ OrbitControls加载成功');
            } else {
                updateStatus('controls-status', 'error', '未加载');
                log('❌ OrbitControls未加载');
            }
        }
        
        // 加载Three.js扩展
        function loadExtensions() {
            const extensions = [
                'https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js',
                'https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js',
                'https://unpkg.com/three@0.158.0/examples/js/loaders/DRACOLoader.js'
            ];

            let loadedCount = 0;
            
            extensions.forEach((url) => {
                const script = document.createElement('script');
                script.src = url;
                script.onload = function() {
                    loadedCount++;
                    log(`✅ 扩展加载成功 (${loadedCount}/${extensions.length}): ${url.split('/').pop()}`);
                    
                    if (loadedCount === extensions.length) {
                        log('✅ 所有Three.js扩展加载完成');
                        window.threeExtensionsLoaded = true;
                        checkExtensions();
                    }
                };
                script.onerror = function() {
                    log(`❌ 扩展加载失败: ${url.split('/').pop()}`);
                    loadedCount++;
                    
                    if (loadedCount === extensions.length) {
                        log('⚠️ 扩展加载完成（部分失败）');
                        window.threeExtensionsLoaded = true;
                        checkExtensions();
                    }
                };
                document.head.appendChild(script);
            });
        }
        
        // 页面加载完成后开始检查
        window.addEventListener('load', function() {
            initStatusChecks();
            
            if (checkThreeJS()) {
                loadExtensions();
            } else {
                log('❌ Three.js加载失败，尝试备用CDN');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/three@0.158.0/build/three.min.js';
                script.onload = function() {
                    log('✅ Three.js从备用CDN加载成功');
                    checkThreeJS();
                    loadExtensions();
                };
                script.onerror = function() {
                    log('❌ 所有CDN都无法加载Three.js');
                    updateStatus('three-status', 'error', '加载失败');
                };
                document.head.appendChild(script);
            }
        });
        
        // 测试函数
        async function testProjectManager() {
            log('🧪 开始测试项目管理器...');
            
            try {
                // 动态加载项目管理器
                const script = document.createElement('script');
                script.src = 'js/managers/project-manager.js';
                script.onload = function() {
                    log('✅ 项目管理器脚本加载成功');
                    
                    try {
                        const projectManager = new ProjectManager();
                        log('✅ 项目管理器实例创建成功');
                        
                        // 测试基本功能
                        setTimeout(() => {
                            const projects = projectManager.getAvailableProjects();
                            log(`📁 发现项目数量: ${projects.length}`);
                            
                            if (projects.length > 0) {
                                log(`📋 第一个项目: ${projects[0].name}`);
                            }
                        }, 1000);
                        
                    } catch (error) {
                        log(`❌ 项目管理器测试失败: ${error.message}`);
                    }
                };
                script.onerror = function() {
                    log('❌ 项目管理器脚本加载失败');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ 项目管理器测试异常: ${error.message}`);
            }
        }
        
        async function testModelLoader() {
            log('🧪 开始测试模型加载器...');
            
            if (typeof THREE.GLTFLoader === 'undefined') {
                log('❌ GLTFLoader未加载，无法测试');
                return;
            }
            
            try {
                // 动态加载模型加载器
                const script = document.createElement('script');
                script.src = 'js/managers/model-loader.js';
                script.onload = function() {
                    log('✅ 模型加载器脚本加载成功');
                    
                    try {
                        const modelLoader = new GLTFModelLoader();
                        log('✅ 模型加载器实例创建成功');
                        
                        // 等待初始化完成
                        setTimeout(() => {
                            if (modelLoader.isInitialized) {
                                log('✅ 模型加载器初始化完成');
                            } else {
                                log('⏳ 模型加载器仍在初始化中...');
                            }
                        }, 2000);
                        
                    } catch (error) {
                        log(`❌ 模型加载器测试失败: ${error.message}`);
                    }
                };
                script.onerror = function() {
                    log('❌ 模型加载器脚本加载失败');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ 模型加载器测试异常: ${error.message}`);
            }
        }
        
        async function testProgressManager() {
            log('🧪 开始测试进度管理器...');
            
            try {
                // 创建一个简单的场景用于测试
                const scene = new THREE.Scene();
                
                // 动态加载进度管理器
                const script = document.createElement('script');
                script.src = 'js/managers/progress-manager.js';
                script.onload = function() {
                    log('✅ 进度管理器脚本加载成功');
                    
                    try {
                        const progressManager = new ProgressManager(scene);
                        log('✅ 进度管理器实例创建成功');
                        
                        // 测试基本功能
                        const testData = {
                            phases: [
                                { id: 'test1', name: '测试阶段1', progress: 50, color: '#e74c3c' },
                                { id: 'test2', name: '测试阶段2', progress: 30, color: '#3498db' }
                            ]
                        };
                        
                        progressManager.init(testData);
                        log('✅ 进度管理器初始化成功');
                        
                    } catch (error) {
                        log(`❌ 进度管理器测试失败: ${error.message}`);
                    }
                };
                script.onerror = function() {
                    log('❌ 进度管理器脚本加载失败');
                };
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ 进度管理器测试异常: ${error.message}`);
            }
        }
    </script>
</body>
</html>
