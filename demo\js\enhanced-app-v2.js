/**
 * 增强版路桥演示系统 V2
 * 基于新架构的主应用程序
 */

class EnhancedBridgeAppV2 {
    constructor() {
        // 应用核心
        this.core = new ApplicationCore();
        
        // 应用状态
        this.isInitialized = false;
        this.isRunning = false;
        
        // 性能监控
        this.performanceMonitor = {
            frameCount: 0,
            lastTime: 0,
            fps: 0
        };
        
        console.log('🌉 增强版路桥演示系统 V2 初始化');
        
        // 开始初始化
        this.initialize().catch(error => {
            console.error('❌ 应用程序初始化失败:', error);
            this.showInitializationError(error);
        });
    }

    /**
     * 初始化应用程序
     */
    async initialize() {
        try {
            console.log('🚀 开始初始化增强版应用程序 V2...');
            
            // 显示加载进度
            this.updateLoadingProgress(10, '检查浏览器兼容性...');
            
            // 检查浏览器兼容性
            if (!this.checkCompatibility()) {
                throw new Error('浏览器不兼容');
            }
            
            this.updateLoadingProgress(20, '等待Three.js加载...');
            
            // 等待Three.js加载
            await this.waitForThreeJS();
            
            this.updateLoadingProgress(30, '注册核心服务...');
            
            // 注册核心服务
            this.registerServices();
            
            this.updateLoadingProgress(50, '初始化应用核心...');
            
            // 初始化应用核心
            await this.core.initialize();
            
            this.updateLoadingProgress(70, '设置场景管理器...');
            
            // 设置场景管理器（重用现有的）
            await this.setupSceneManager();
            
            this.updateLoadingProgress(80, '初始化用户界面...');
            
            // 初始化UI
            await this.initializeUI();
            
            this.updateLoadingProgress(90, '启动系统服务...');
            
            // 启动系统
            await this.startSystem();
            
            this.updateLoadingProgress(100, '系统就绪');
            
            // 标记为已初始化
            this.isInitialized = true;
            this.isRunning = true;
            
            console.log('✅ 增强版路桥演示系统 V2 启动完成');
            
            // 隐藏加载界面
            setTimeout(() => {
                this.hideLoadingScreen();
                this.showWelcomeMessage();
            }, 1000);
            
        } catch (error) {
            console.error('❌ 系统初始化失败:', error);
            this.showInitializationError(error);
            throw error;
        }
    }

    /**
     * 检查浏览器兼容性
     */
    checkCompatibility() {
        // 检查WebGL
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) {
            this.showCompatibilityError('WebGL不支持');
            return false;
        }
        
        // 检查必要API
        const requiredAPIs = ['fetch', 'Promise', 'Map', 'Set'];
        for (const api of requiredAPIs) {
            if (typeof window[api] === 'undefined') {
                this.showCompatibilityError(`${api} API不支持`);
                return false;
            }
        }
        
        console.log('✅ 浏览器兼容性检查通过');
        return true;
    }

    /**
     * 等待Three.js加载
     */
    async waitForThreeJS() {
        return new Promise((resolve, reject) => {
            const checkThree = () => {
                if (typeof THREE !== 'undefined') {
                    console.log('✅ Three.js已加载，版本:', THREE.REVISION);
                    resolve();
                } else {
                    setTimeout(checkThree, 100);
                }
            };
            
            checkThree();
            
            // 10秒超时
            setTimeout(() => {
                reject(new Error('Three.js加载超时'));
            }, 10000);
        });
    }

    /**
     * 注册核心服务
     */
    registerServices() {
        console.log('📦 注册核心服务...');
        
        // 项目服务
        this.core.registerService('project', ProjectService);
        
        // 模型服务
        this.core.registerService('model', ModelService);
        
        // 场景服务 - 不需要依赖，稍后手动设置
        this.core.registerService('scene', SceneService, []);
        
        // 进度服务
        this.core.registerService('progress', ProgressService);
        
        // 工具服务
        this.core.registerService('tool', ToolService);
        
        console.log('✅ 核心服务注册完成');
    }

    /**
     * 设置场景管理器
     */
    async setupSceneManager() {
        // 检查是否已有场景管理器
        if (typeof EnhancedBridgeScene !== 'undefined') {
            // 使用现有的场景管理器
            this.sceneManager = new EnhancedBridgeScene();
            
            // 等待场景管理器就绪
            await this.waitForSceneManager();
            
            // EnhancedBridgeScene 也需要控制器
            await this.initializeControls();
            
            console.log('✅ 增强场景管理器已设置');
        } else if (typeof BridgeScene !== 'undefined') {
            // 使用基础场景管理器
            this.sceneManager = new BridgeScene();
            
            // 初始化基础场景
            this.sceneManager.init();
            
            // 创建控制器
            await this.initializeControls();
            
            console.log('✅ 基础场景管理器已设置');
        } else {
            throw new Error('找不到可用的场景管理器');
        }
        
        // 将场景管理器和模型服务传递给场景服务
        const sceneService = await this.core.getService('scene');
        const modelService = await this.core.getService('model');
        
        // 手动设置依赖关系
        sceneService.sceneManager = this.sceneManager;
        sceneService.modelService = modelService;
        
        // 重新初始化事件监听
        sceneService.setupEventListeners();
        sceneService.setupPerformanceMonitoring();
    }

    /**
     * 初始化控制器
     */
    async initializeControls() {
        try {
            console.log('🎮 初始化场景控制器...');
            
            // 检查是否有相机和渲染器
            const camera = this.sceneManager.getCamera();
            const renderer = this.sceneManager.getRenderer();
            
            if (!camera || !renderer) {
                throw new Error('相机或渲染器未初始化');
            }
            
            // 创建控制器
            if (typeof BridgeControls !== 'undefined') {
                const controls = new BridgeControls(
                    this.sceneManager.scene,
                    camera,
                    renderer
                );
                
                // 初始化控制器
                controls.init();
                
                // 设置到场景管理器
                this.sceneManager.setControls(controls);
                
                console.log('✅ BridgeControls已创建并设置');
            } else {
                // 创建基础OrbitControls
                await this.createBasicOrbitControls(camera, renderer);
            }
            
        } catch (error) {
            console.error('❌ 控制器初始化失败:', error);
            // 即使控制器失败，也不阻止应用启动
        }
    }

    /**
     * 创建基础OrbitControls
     */
    async createBasicOrbitControls(camera, renderer) {
        try {
            if (typeof THREE.OrbitControls !== 'undefined') {
                const controls = new THREE.OrbitControls(camera, renderer.domElement);
                
                // 配置控制器
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.screenSpacePanning = false;
                controls.minDistance = 10;
                controls.maxDistance = 2000;
                controls.maxPolarAngle = Math.PI * 0.8;
                controls.minPolarAngle = Math.PI * 0.1;
                
                // 创建一个包装对象以兼容BridgeControls接口
                const controlsWrapper = {
                    orbitControls: controls,
                    update: () => controls.update(),
                    getOrbitControls: () => controls,
                    reset: () => controls.reset(),
                    target: controls.target
                };
                
                this.sceneManager.setControls(controlsWrapper);
                
                console.log('✅ 基础OrbitControls已创建');
            } else {
                console.warn('⚠️ OrbitControls不可用，视角控制将受限');
            }
        } catch (error) {
            console.error('❌ 基础控制器创建失败:', error);
        }
    }

    /**
     * 等待场景管理器就绪
     */
    async waitForSceneManager() {
        return new Promise((resolve) => {
            const checkReady = () => {
                if (this.sceneManager.isEnhancementsReady) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            
            checkReady();
            
            // 15秒超时
            setTimeout(() => {
                console.warn('⚠️ 场景管理器就绪检查超时，继续启动');
                resolve();
            }, 15000);
        });
    }

    /**
     * 初始化UI
     */
    async initializeUI() {
        try {
            // 获取服务
            const projectService = await this.core.getService('project');
            const progressService = await this.core.getService('progress');
            
            // 创建UI管理器
            if (typeof ProjectUI !== 'undefined') {
                this.projectUI = new ProjectUI(projectService, this.sceneManager);
            }
            
            if (typeof ProgressUI !== 'undefined') {
                this.progressUI = new ProgressUI(progressService);
            }
            
            // 保持原有UI兼容性
            if (typeof BridgeUI !== 'undefined' && this.sceneManager) {
                this.originalUI = new BridgeUI(this.sceneManager, this.sceneManager.controls);
            }
            
            console.log('✅ 用户界面初始化完成');
            
        } catch (error) {
            console.warn('⚠️ 部分UI组件初始化失败:', error);
        }
    }

    /**
     * 启动系统
     */
    async startSystem() {
        // 设置事件监听
        this.setupEventListeners();
        
        // 启动性能监控
        this.startPerformanceMonitoring();
        
        // 设置全局调试接口
        this.setupDebugInterface();
        
        console.log('✅ 系统服务启动完成');
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.onWindowResize();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
        
        // 页面卸载清理
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        // 应用事件
        this.core.eventBus.on('app:initialized', () => {
            console.log('📨 应用初始化完成事件');
        });
        
        // 项目事件
        this.core.eventBus.on('project:loaded', (data) => {
            console.log(`📨 项目加载完成: ${data.project.name}`);
        });
        
        // 场景事件
        this.core.eventBus.on('scene:projectLoaded', (data) => {
            console.log(`📨 场景加载完成: 成功 ${data.successful}/${data.totalModels}`);
        });
    }

    /**
     * 窗口大小变化处理
     */
    onWindowResize() {
        if (this.sceneManager) {
            const camera = this.sceneManager.getCamera();
            const renderer = this.sceneManager.getRenderer();
            
            if (camera && renderer) {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
        }
    }

    /**
     * 键盘快捷键处理
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + S: 保存项目
        if ((event.ctrlKey || event.metaKey) && event.key === 's') {
            event.preventDefault();
            this.saveCurrentProject();
        }
        
        // 空格键: 播放/暂停进度
        if (event.key === ' ' && !event.target.matches('input, textarea, select')) {
            event.preventDefault();
            this.toggleProgressPlayback();
        }
        
        // R键: 重置进度
        if (event.key === 'r' || event.key === 'R') {
            this.resetProgress();
        }
        
        // 数字键1-9: 快速切换阶段
        if (event.key >= '1' && event.key <= '9') {
            const phaseIndex = parseInt(event.key) - 1;
            this.switchToPhase(phaseIndex);
        }
    }

    /**
     * 保存当前项目
     */
    async saveCurrentProject() {
        try {
            const projectService = await this.core.getService('project');
            const currentProject = projectService.getCurrentProject();
            
            if (!currentProject) {
                this.showNotification('warning', '没有当前项目可保存');
                return;
            }
            
            // 获取当前状态
            const currentState = this.getCurrentState();
            
            // 更新项目配置
            Object.assign(currentProject.config, currentState);
            
            // 保存配置
            await projectService.saveProjectConfig(currentProject.config);
            
            this.showNotification('success', '项目状态已保存');
            
        } catch (error) {
            console.error('❌ 保存项目失败:', error);
            this.showNotification('error', '保存项目失败');
        }
    }

    /**
     * 获取当前状态
     */
    getCurrentState() {
        const state = {};
        
        // 相机状态
        if (this.sceneManager) {
            const camera = this.sceneManager.getCamera();
            const controls = this.sceneManager.controls;
            
            if (camera) {
                state.camera = {
                    position: camera.position.toArray(),
                    target: controls?.getOrbitControls ? 
                        controls.getOrbitControls().target.toArray() : [0, 0, 0]
                };
            }
        }
        
        // 时间戳
        state.timestamp = Date.now();
        
        return state;
    }

    /**
     * 切换进度播放
     */
    async toggleProgressPlayback() {
        try {
            const progressService = await this.core.getService('progress');
            progressService.togglePlayback();
        } catch (error) {
            console.error('❌ 切换播放失败:', error);
        }
    }

    /**
     * 重置进度
     */
    async resetProgress() {
        try {
            const progressService = await this.core.getService('progress');
            progressService.resetProgress();
            this.showNotification('info', '进度已重置');
        } catch (error) {
            console.error('❌ 重置进度失败:', error);
        }
    }

    /**
     * 切换到指定阶段
     */
    async switchToPhase(phaseIndex) {
        try {
            const progressService = await this.core.getService('progress');
            const progressData = progressService.getProgressData();
            
            if (progressData.phases && progressData.phases[phaseIndex]) {
                const phase = progressData.phases[phaseIndex];
                progressService.switchToPhase(phase.id);
                this.showNotification('info', `切换到: ${phase.name}`);
            }
        } catch (error) {
            console.error('❌ 切换阶段失败:', error);
        }
    }

    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        const monitor = () => {
            this.performanceMonitor.frameCount++;
            const now = performance.now();
            
            if (now >= this.performanceMonitor.lastTime + 1000) {
                this.performanceMonitor.fps = Math.round(
                    (this.performanceMonitor.frameCount * 1000) / (now - this.performanceMonitor.lastTime)
                );
                
                // 更新FPS显示
                this.updateFPSDisplay();
                
                this.performanceMonitor.frameCount = 0;
                this.performanceMonitor.lastTime = now;
            }
            
            if (this.isRunning) {
                requestAnimationFrame(monitor);
            }
        };
        
        monitor();
    }

    /**
     * 更新FPS显示
     */
    updateFPSDisplay() {
        const fpsElement = document.getElementById('fps-counter');
        if (fpsElement) {
            fpsElement.textContent = `FPS: ${this.performanceMonitor.fps}`;
        }
    }

    /**
     * 设置调试接口
     */
    setupDebugInterface() {
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            window.bridgeAppV2 = this;
            window.debugBridgeV2 = {
                getSystemInfo: () => this.getSystemInfo(),
                getServices: () => Array.from(this.core.services.keys()),
                getService: (name) => this.core.getService(name),
                restart: () => this.restart(),
                pause: () => this.pause(),
                resume: () => this.resume(),
                getStats: () => this.getAllStats(),
                
                // 工具相关的调试方法
                activateTool: async (toolName, ...args) => {
                    const toolService = await this.core.getService('tool');
                    return toolService.activateTool(toolName, ...args);
                },
                deactivateTool: async (toolName) => {
                    const toolService = await this.core.getService('tool');
                    return toolService.deactivateTool(toolName);
                },
                takeScreenshot: async () => {
                    const toolService = await this.core.getService('tool');
                    return toolService.takeScreenshot();
                },
                startMeasurement: async (mode) => {
                    const toolService = await this.core.getService('tool');
                    return toolService.startMeasurement(mode);
                },
                stopMeasurement: async () => {
                    const toolService = await this.core.getService('tool');
                    return toolService.stopMeasurement();
                },
                getMeasurements: async () => {
                    const toolService = await this.core.getService('tool');
                    return toolService.getMeasurements();
                },
                clearMeasurements: async () => {
                    const toolService = await this.core.getService('tool');
                    return toolService.clearAllMeasurements();
                },
                getToolStates: async () => {
                    const toolService = await this.core.getService('tool');
                    return toolService.getToolStates();
                }
            };
            
            console.log('🔧 调试接口已启用，使用 window.debugBridgeV2 访问');
        }
    }

    /**
     * 获取系统信息
     */
    getSystemInfo() {
        return {
            version: 'V2',
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            coreState: this.core.getState(),
            performance: this.performanceMonitor,
            memoryUsage: this.getMemoryUsage()
        };
    }

    /**
     * 获取所有统计信息
     */
    async getAllStats() {
        const stats = {
            system: this.getSystemInfo(),
            core: this.core.getState()
        };
        
        try {
            const modelService = await this.core.getService('model');
            stats.model = modelService.getStats();
            
            const sceneService = await this.core.getService('scene');
            stats.scene = sceneService.getSceneStats();
            
            const progressService = await this.core.getService('progress');
            stats.progress = progressService.getProgressData();
            
        } catch (error) {
            console.warn('获取部分统计信息失败:', error);
        }
        
        return stats;
    }

    /**
     * 获取内存使用情况
     */
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }

    /**
     * 显示通知
     */
    showNotification(type, message) {
        if (this.originalUI && this.originalUI.showNotification) {
            this.originalUI.showNotification(type, '系统', message);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(progress, message) {
        const progressBar = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('.loading-text');
        
        if (progressBar) {
            progressBar.style.width = `${progress}%`;
        }
        
        if (loadingText) {
            loadingText.textContent = message;
        }
        
        console.log(`📊 加载进度: ${progress}% - ${message}`);
    }

    /**
     * 隐藏加载屏幕
     */
    hideLoadingScreen() {
        const loadingScreen = document.querySelector('.loading-screen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }

    /**
     * 显示欢迎消息
     */
    showWelcomeMessage() {
        setTimeout(() => {
            this.showNotification('success', '增强版路桥演示系统 V2 已成功启动');
        }, 1500);
    }

    /**
     * 显示兼容性错误
     */
    showCompatibilityError(reason) {
        document.body.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                        background: #2c3e50; color: white; display: flex;
                        align-items: center; justify-content: center; z-index: 10000;">
                <div style="text-align: center; max-width: 600px; padding: 40px;">
                    <h2>❌ 浏览器兼容性问题</h2>
                    <p>您的浏览器不支持运行此应用程序：${reason}</p>
                    <p>请使用现代浏览器（Chrome 90+、Firefox 88+、Safari 14+、Edge 90+）。</p>
                </div>
            </div>
        `;
    }

    /**
     * 显示初始化错误
     */
    showInitializationError(error) {
        document.body.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                        background: #2c3e50; color: white; display: flex;
                        align-items: center; justify-content: center; z-index: 10000;">
                <div style="text-align: center; max-width: 600px; padding: 40px;">
                    <h2>❌ 系统初始化失败</h2>
                    <p>系统启动过程中发生错误：</p>
                    <p style="color: #e74c3c; margin: 20px 0;">${error.message}</p>
                    <button onclick="location.reload()"
                            style="margin-top: 20px; padding: 10px 20px;
                                   background: #3498db; color: white; border: none;
                                   border-radius: 5px; cursor: pointer;">
                        重新加载
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 暂停应用程序
     */
    pause() {
        this.isRunning = false;
        console.log('⏸️ 应用程序已暂停');
    }

    /**
     * 恢复应用程序
     */
    resume() {
        this.isRunning = true;
        this.startPerformanceMonitoring();
        console.log('▶️ 应用程序已恢复');
    }

    /**
     * 重启应用程序
     */
    restart() {
        this.cleanup();
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('🧹 清理应用程序资源...');
        
        this.isRunning = false;
        
        // 清理核心
        await this.core.shutdown();
        
        // 清理UI
        if (this.projectUI && this.projectUI.cleanup) {
            this.projectUI.cleanup();
        }
        if (this.progressUI && this.progressUI.cleanup) {
            this.progressUI.cleanup();
        }
        if (this.originalUI && this.originalUI.cleanup) {
            this.originalUI.cleanup();
        }
        
        console.log('✅ 应用程序资源清理完成');
    }
}

// 全局变量
let enhancedBridgeAppV2 = null;

// 启动应用程序
function startEnhancedAppV2() {
    // 检查前置条件
    const coreLoaded = typeof ApplicationCore !== 'undefined';
    const servicesLoaded = typeof ProjectService !== 'undefined';
    const domReady = document.readyState === 'complete' || document.readyState === 'interactive';

    console.log(`🔍 启动条件检查 V2: Core=${coreLoaded}, Services=${servicesLoaded}, DOM=${domReady}`);

    if (coreLoaded && servicesLoaded && domReady) {
        console.log('🚀 所有条件满足，启动增强版应用程序 V2...');
        enhancedBridgeAppV2 = new EnhancedBridgeAppV2();
    } else {
        // 等待条件满足
        setTimeout(startEnhancedAppV2, 200);
    }
}

// 启动应用程序
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', startEnhancedAppV2);
} else {
    startEnhancedAppV2();
}

// 导出到全局作用域
window.EnhancedBridgeAppV2 = EnhancedBridgeAppV2;
window.getEnhancedBridgeAppV2 = () => enhancedBridgeAppV2;