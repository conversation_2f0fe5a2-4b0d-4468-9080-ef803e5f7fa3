/**
 * 路桥数字孪生UI交互系统
 * 处理用户界面交互和控制面板功能
 */

class BridgeUI {
    constructor(bridgeScene, bridgeControls) {
        this.scene = bridgeScene;
        this.controls = bridgeControls;
        
        this.notifications = [];
        this.modals = {};
        
        this.init();
    }

    /**
     * 初始化UI系统
     */
    init() {
        this.setupViewControls();
        this.setupLayerControls();
        this.setupRenderControls();
        this.setupQuickActions();
        this.setupNavigationControls();
        this.setupNotificationSystem();
        this.setupModalSystem();
        this.setupProgressTracking();
        this.hideLoadingScreen();
    }

    /**
     * 设置视图控制
     */
    setupViewControls() {
        const viewButtons = document.querySelectorAll('.view-btn');
        
        viewButtons.forEach(button => {
            button.addEventListener('click', () => {
                // 移除所有活动状态
                viewButtons.forEach(btn => btn.classList.remove('active'));
                
                // 添加当前按钮的活动状态
                button.classList.add('active');
                
                // 获取视图类型
                const viewType = button.getAttribute('data-view');
                
                // 切换相机视角
                this.controls.setCameraView(viewType);
                
                // 更新相机位置显示
                this.updateCameraPositionDisplay(viewType);
                
                // 显示通知
                this.showNotification('success', '视图切换', `已切换到${this.getViewName(viewType)}`);
            });
        });
    }

    /**
     * 设置图层控制
     */
    setupLayerControls() {
        const layerCheckboxes = document.querySelectorAll('[data-layer]');
        
        layerCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const layerName = checkbox.getAttribute('data-layer');
                const isVisible = checkbox.checked;
                
                // 切换图层显示
                this.scene.toggleLayer(layerName, isVisible);
                
                // 显示通知
                const action = isVisible ? '显示' : '隐藏';
                this.showNotification('success', '图层控制', `${action}${this.getLayerName(layerName)}图层`);
            });
        });
    }

    /**
     * 设置渲染控制
     */
    setupRenderControls() {
        // 光照强度控制
        const lightIntensitySlider = document.getElementById('light-intensity');
        if (lightIntensitySlider) {
            lightIntensitySlider.addEventListener('input', (event) => {
                const intensity = parseFloat(event.target.value);
                this.scene.updateSettings({ lightIntensity: intensity });
            });
        }

        // 阴影质量控制
        const shadowQualitySelect = document.getElementById('shadow-quality');
        if (shadowQualitySelect) {
            shadowQualitySelect.addEventListener('change', (event) => {
                const quality = event.target.value;
                this.updateShadowQuality(quality);
                this.showNotification('success', '渲染设置', `阴影质量已设置为${quality}`);
            });
        }

        // 雾效强度控制
        const fogDensitySlider = document.getElementById('fog-density');
        if (fogDensitySlider) {
            fogDensitySlider.addEventListener('input', (event) => {
                const density = parseFloat(event.target.value);
                this.scene.updateSettings({ fogDensity: density });
            });
        }
    }

    /**
     * 设置快速操作
     */
    setupQuickActions() {
        const actionButtons = document.querySelectorAll('.action-btn');
        
        actionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const action = this.getActionFromButton(button);
                this.executeQuickAction(action);
            });
        });
    }

    /**
     * 设置导航控制
     */
    setupNavigationControls() {
        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleFullscreen();
            });
        }

        // 设置按钮
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettingsModal();
            });
        }
    }

    /**
     * 设置通知系统
     */
    setupNotificationSystem() {
        // 创建通知容器
        this.notificationContainer = document.createElement('div');
        this.notificationContainer.className = 'notification-container';
        this.notificationContainer.style.position = 'fixed';
        this.notificationContainer.style.top = '80px';
        this.notificationContainer.style.right = '20px';
        this.notificationContainer.style.zIndex = '10000';
        document.body.appendChild(this.notificationContainer);
    }

    /**
     * 设置模态框系统
     */
    setupModalSystem() {
        // 创建设置模态框
        this.createSettingsModal();
        
        // 创建测量工具模态框
        this.createMeasurementModal();
        
        // 创建导出模态框
        this.createExportModal();
    }

    /**
     * 设置进度跟踪
     */
    setupProgressTracking() {
        // 模拟进度更新
        this.updateProgress();
        
        // 设置定时更新
        setInterval(() => {
            this.updateProgress();
        }, 30000); // 每30秒更新一次
    }

    /**
     * 隐藏加载界面
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 2000);
        }
    }

    /**
     * 获取视图名称
     */
    getViewName(viewType) {
        const names = {
            overview: '总览视图',
            bridge: '桥梁视图',
            road: '道路视图',
            construction: '施工视图'
        };
        return names[viewType] || '未知视图';
    }

    /**
     * 获取图层名称
     */
    getLayerName(layerName) {
        const names = {
            bridge: '桥梁结构',
            road: '道路系统',
            terrain: '地形地貌',
            equipment: '施工设备',
            safety: '安全设施'
        };
        return names[layerName] || '未知图层';
    }

    /**
     * 更新相机位置显示
     */
    updateCameraPositionDisplay(viewType) {
        const cameraPositionElement = document.getElementById('camera-position');
        if (cameraPositionElement) {
            cameraPositionElement.textContent = `相机: ${this.getViewName(viewType)}`;
        }
    }

    /**
     * 更新阴影质量
     */
    updateShadowQuality(quality) {
        const renderer = this.scene.getRenderer();
        
        switch (quality) {
            case 'low':
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.BasicShadowMap;
                break;
            case 'medium':
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFShadowMap;
                break;
            case 'high':
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                break;
        }
    }

    /**
     * 从按钮获取操作类型
     */
    getActionFromButton(button) {
        const icon = button.querySelector('i');
        if (icon.classList.contains('fa-camera')) return 'screenshot';
        if (icon.classList.contains('fa-video')) return 'record';
        if (icon.classList.contains('fa-ruler')) return 'measure';
        if (icon.classList.contains('fa-download')) return 'export';
        return 'unknown';
    }

    /**
     * 执行快速操作
     */
    executeQuickAction(action) {
        switch (action) {
            case 'screenshot':
                this.takeScreenshot();
                break;
            case 'record':
                this.toggleRecording();
                break;
            case 'measure':
                this.showMeasurementModal();
                break;
            case 'export':
                this.showExportModal();
                break;
            default:
                this.showNotification('warning', '操作提示', '该功能正在开发中');
        }
    }

    /**
     * 截图功能
     */
    takeScreenshot() {
        const renderer = this.scene.getRenderer();
        const canvas = renderer.domElement;
        
        // 创建下载链接
        const link = document.createElement('a');
        link.download = `bridge_screenshot_${Date.now()}.png`;
        link.href = canvas.toDataURL();
        link.click();
        
        this.showNotification('success', '截图完成', '截图已保存到下载文件夹');
    }

    /**
     * 切换录制
     */
    toggleRecording() {
        // 这里可以集成录制功能
        this.showNotification('info', '录制功能', '录制功能需要额外的库支持');
    }

    /**
     * 切换全屏
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().then(() => {
                this.showNotification('success', '全屏模式', '已进入全屏模式');
            });
        } else {
            document.exitFullscreen().then(() => {
                this.showNotification('success', '窗口模式', '已退出全屏模式');
            });
        }
    }

    /**
     * 显示通知
     */
    showNotification(type, title, message, duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const iconMap = {
            success: 'fa-check-circle',
            warning: 'fa-exclamation-triangle',
            error: 'fa-times-circle',
            info: 'fa-info-circle'
        };
        
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="fas ${iconMap[type]}"></i>
                </div>
                <div class="notification-text">
                    <div class="notification-title">${title}</div>
                    <div class="notification-message">${message}</div>
                </div>
            </div>
        `;
        
        this.notificationContainer.appendChild(notification);
        
        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
        
        this.notifications.push(notification);
    }

    /**
     * 创建设置模态框
     */
    createSettingsModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'settings-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">系统设置</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="setting-group">
                        <h4>渲染设置</h4>
                        <label>
                            <input type="checkbox" id="enable-shadows" checked>
                            启用阴影
                        </label>
                        <label>
                            <input type="checkbox" id="enable-fog" checked>
                            启用雾效
                        </label>
                        <label>
                            <input type="checkbox" id="enable-postprocessing" checked>
                            启用后处理
                        </label>
                    </div>
                    <div class="setting-group">
                        <h4>性能设置</h4>
                        <label>
                            渲染质量:
                            <select id="render-quality">
                                <option value="low">低</option>
                                <option value="medium" selected>中</option>
                                <option value="high">高</option>
                            </select>
                        </label>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.modals.settings = modal;
        
        // 设置事件监听器
        this.setupModalEvents(modal);
    }

    /**
     * 创建测量工具模态框
     */
    createMeasurementModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'measurement-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">测量工具</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="measurement-tools">
                        <button class="tool-btn" data-tool="distance">
                            <i class="fas fa-ruler"></i>
                            距离测量
                        </button>
                        <button class="tool-btn" data-tool="area">
                            <i class="fas fa-vector-square"></i>
                            面积测量
                        </button>
                        <button class="tool-btn" data-tool="angle">
                            <i class="fas fa-drafting-compass"></i>
                            角度测量
                        </button>
                    </div>
                    <div class="measurement-results">
                        <h4>测量结果</h4>
                        <div id="measurement-output">请选择测量工具开始测量</div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.modals.measurement = modal;
        
        this.setupModalEvents(modal);
    }

    /**
     * 创建导出模态框
     */
    createExportModal() {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'export-modal';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">导出数据</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="export-options">
                        <h4>导出格式</h4>
                        <label>
                            <input type="radio" name="export-format" value="image" checked>
                            图片 (PNG/JPG)
                        </label>
                        <label>
                            <input type="radio" name="export-format" value="model">
                            3D模型 (OBJ/GLTF)
                        </label>
                        <label>
                            <input type="radio" name="export-format" value="data">
                            数据报告 (PDF/Excel)
                        </label>
                    </div>
                    <div class="export-settings">
                        <h4>导出设置</h4>
                        <label>
                            分辨率:
                            <select id="export-resolution">
                                <option value="1920x1080">1920x1080</option>
                                <option value="2560x1440">2560x1440</option>
                                <option value="3840x2160">3840x2160</option>
                            </select>
                        </label>
                    </div>
                    <button class="export-btn">开始导出</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        this.modals.export = modal;
        
        this.setupModalEvents(modal);
    }

    /**
     * 设置模态框事件
     */
    setupModalEvents(modal) {
        // 关闭按钮
        const closeBtn = modal.querySelector('.modal-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.hideModal(modal);
            });
        }
        
        // 点击背景关闭
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                this.hideModal(modal);
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && modal.classList.contains('show')) {
                this.hideModal(modal);
            }
        });
    }

    /**
     * 显示模态框
     */
    showModal(modal) {
        modal.classList.add('show');
    }

    /**
     * 隐藏模态框
     */
    hideModal(modal) {
        modal.classList.remove('show');
    }

    /**
     * 显示设置模态框
     */
    showSettingsModal() {
        this.showModal(this.modals.settings);
    }

    /**
     * 显示测量模态框
     */
    showMeasurementModal() {
        this.showModal(this.modals.measurement);
    }

    /**
     * 显示导出模态框
     */
    showExportModal() {
        this.showModal(this.modals.export);
    }

    /**
     * 更新进度
     */
    updateProgress() {
        const progressItems = document.querySelectorAll('.progress-item');
        
        progressItems.forEach((item, index) => {
            const progressFill = item.querySelector('.progress-fill');
            const progressText = item.querySelector('.progress-text');
            
            if (progressFill && progressText) {
                // 模拟进度增长
                const currentWidth = parseInt(progressFill.style.width) || 0;
                const increment = Math.random() * 2;
                const newWidth = Math.min(currentWidth + increment, 100);
                
                progressFill.style.width = `${newWidth}%`;
                progressText.textContent = `${Math.round(newWidth)}%`;
            }
        });
    }

    /**
     * 更新项目状态
     */
    updateProjectStatus(status) {
        const statusElement = document.querySelector('.project-status');
        if (statusElement) {
            statusElement.textContent = status;
            
            // 根据状态设置颜色
            statusElement.className = 'project-status';
            if (status === '已完成') {
                statusElement.style.color = '#2ecc71';
                statusElement.style.backgroundColor = 'rgba(46, 204, 113, 0.2)';
            } else if (status === '施工中') {
                statusElement.style.color = '#f39c12';
                statusElement.style.backgroundColor = 'rgba(243, 156, 18, 0.2)';
            } else if (status === '暂停') {
                statusElement.style.color = '#e74c3c';
                statusElement.style.backgroundColor = 'rgba(231, 76, 60, 0.2)';
            }
        }
    }

    /**
     * 添加自定义事件监听器
     */
    addEventListener(eventName, callback) {
        window.addEventListener(eventName, callback);
    }

    /**
     * 移除事件监听器
     */
    removeEventListener(eventName, callback) {
        window.removeEventListener(eventName, callback);
    }

    /**
     * 销毁UI系统
     */
    dispose() {
        // 清理通知
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
        
        // 清理模态框
        Object.values(this.modals).forEach(modal => {
            if (modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        });
        
        // 清理通知容器
        if (this.notificationContainer && this.notificationContainer.parentNode) {
            this.notificationContainer.parentNode.removeChild(this.notificationContainer);
        }
    }
}
