<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版增强路桥演示系统</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui.css">
    <link rel="stylesheet" href="styles/enhanced-ui.css">
    
    <!-- 外部字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 主容器 -->
    <div id="container">
        <!-- 3D渲染画布 -->
        <canvas id="three-canvas"></canvas>
        
        <!-- 顶部导航栏 -->
        <header class="top-nav">
            <div class="nav-left">
                <h1><i class="fas fa-bridge"></i> 简化版增强路桥演示系统</h1>
            </div>
            <div class="nav-center">
                <div class="project-info">
                    <span class="project-name" id="current-project-name">系统启动中...</span>
                    <span class="project-status" id="current-project-status">初始化</span>
                </div>
            </div>
            <div class="nav-right">
                <button class="nav-btn" id="test-btn" title="测试功能">
                    <i class="fas fa-flask"></i>
                </button>
                <button class="nav-btn" id="fullscreen-btn" title="全屏">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </header>

        <!-- 左侧控制面板 -->
        <aside class="left-panel">
            <div class="panel-section">
                <h3><i class="fas fa-info-circle"></i> 系统状态</h3>
                <div id="system-status">
                    <div class="status-item">
                        <span class="label">Three.js:</span>
                        <span class="value" id="three-status">检查中...</span>
                    </div>
                    <div class="status-item">
                        <span class="label">GLTFLoader:</span>
                        <span class="value" id="gltf-status">检查中...</span>
                    </div>
                    <div class="status-item">
                        <span class="label">场景管理器:</span>
                        <span class="value" id="scene-status">检查中...</span>
                    </div>
                    <div class="status-item">
                        <span class="label">项目管理器:</span>
                        <span class="value" id="project-status">检查中...</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-play"></i> 快速测试</h3>
                <div class="test-controls">
                    <button id="test-three" class="test-btn">测试Three.js</button>
                    <button id="test-scene" class="test-btn">测试场景</button>
                    <button id="test-project" class="test-btn">测试项目管理</button>
                    <button id="start-full" class="test-btn">启动完整系统</button>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-eye"></i> 视图控制</h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview">
                        <i class="fas fa-globe"></i>
                        <span>总览视图</span>
                    </button>
                    <button class="view-btn" data-view="bridge">
                        <i class="fas fa-bridge"></i>
                        <span>桥梁视图</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- 右侧信息面板 -->
        <aside class="right-panel">
            <div class="panel-section">
                <h3><i class="fas fa-terminal"></i> 系统日志</h3>
                <div id="system-log" style="height: 200px; overflow-y: auto; background: #1a1a1a; color: #00ff00; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 12px;">
                    系统启动中...<br>
                </div>
                <button id="clear-log" style="margin-top: 10px;">清空日志</button>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-chart-line"></i> 基础演示</h3>
                <div class="demo-controls">
                    <button id="demo-bridge" class="demo-btn">演示桥梁</button>
                    <button id="demo-progress" class="demo-btn">演示进度</button>
                    <button id="demo-measurement" class="demo-btn">演示测量</button>
                </div>
            </div>
        </aside>

        <!-- 底部状态栏 -->
        <footer class="bottom-status">
            <div class="status-left">
                <span class="status-item">
                    <i class="fas fa-cog"></i>
                    <span id="system-mode">简化模式</span>
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">
                    <i class="fas fa-clock"></i>
                    <span id="system-time"></span>
                </span>
            </div>
        </footer>
    </div>

    <!-- Three.js 主库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/0.158.0/three.min.js"></script>
    
    <script>
        let systemLog = [];
        let bridgeScene = null;
        let projectManager = null;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            systemLog.push(logEntry);
            
            const logElement = document.getElementById('system-log');
            logElement.innerHTML = systemLog.join('<br>');
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }
        
        function updateStatus(elementId, status, className = '') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = status;
                element.className = `value ${className}`;
            }
        }
        
        function updateTime() {
            const timeElement = document.getElementById('system-time');
            if (timeElement) {
                timeElement.textContent = new Date().toLocaleTimeString();
            }
        }
        
        // 检查Three.js
        function checkThreeJS() {
            if (typeof THREE !== 'undefined') {
                updateStatus('three-status', '✅ 已加载', 'success');
                log('✅ Three.js加载成功，版本: ' + THREE.REVISION);
                return true;
            } else {
                updateStatus('three-status', '❌ 未加载', 'error');
                log('❌ Three.js未加载');
                return false;
            }
        }
        
        // 加载Three.js扩展
        async function loadExtensions() {
            log('📦 开始加载Three.js扩展...');
            
            const extensions = [
                { url: 'https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js', name: 'OrbitControls' },
                { url: 'https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js', name: 'GLTFLoader' },
                { url: 'https://unpkg.com/three@0.158.0/examples/js/loaders/DRACOLoader.js', name: 'DRACOLoader' }
            ];
            
            for (const ext of extensions) {
                try {
                    await loadScript(ext.url);
                    log(`✅ ${ext.name} 加载成功`);
                } catch (error) {
                    log(`❌ ${ext.name} 加载失败: ${error.message}`);
                }
            }
            
            // 检查GLTFLoader
            if (typeof THREE.GLTFLoader !== 'undefined') {
                updateStatus('gltf-status', '✅ 已加载', 'success');
                log('✅ GLTFLoader可用');
            } else {
                updateStatus('gltf-status', '❌ 未加载', 'error');
                log('❌ GLTFLoader不可用');
            }
        }
        
        // 加载脚本的Promise包装
        function loadScript(url) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = url;
                script.onload = () => resolve();
                script.onerror = () => reject(new Error(`无法加载: ${url}`));
                document.head.appendChild(script);
            });
        }
        
        // 测试基础场景
        async function testBasicScene() {
            log('🧪 测试基础场景创建...');
            
            try {
                // 加载原有场景脚本
                await loadScript('js/materials.js');
                await loadScript('js/geometry.js');
                await loadScript('js/scene.js');
                await loadScript('js/controls.js');
                await loadScript('js/ui.js');
                
                log('✅ 原有脚本加载完成');
                
                // 创建基础场景
                if (typeof BridgeScene !== 'undefined') {
                    bridgeScene = new BridgeScene();
                    updateStatus('scene-status', '✅ 已创建', 'success');
                    log('✅ 基础场景创建成功');
                } else {
                    throw new Error('BridgeScene类未定义');
                }
                
            } catch (error) {
                updateStatus('scene-status', '❌ 失败', 'error');
                log(`❌ 基础场景测试失败: ${error.message}`);
            }
        }
        
        // 测试项目管理器
        async function testProjectManager() {
            log('🧪 测试项目管理器...');
            
            try {
                await loadScript('js/managers/project-manager.js');
                
                if (typeof ProjectManager !== 'undefined') {
                    projectManager = new ProjectManager();
                    updateStatus('project-status', '✅ 已创建', 'success');
                    log('✅ 项目管理器创建成功');
                    
                    // 等待初始化
                    setTimeout(() => {
                        const projects = projectManager.getAvailableProjects();
                        log(`📁 发现 ${projects.length} 个项目`);
                    }, 1000);
                } else {
                    throw new Error('ProjectManager类未定义');
                }
                
            } catch (error) {
                updateStatus('project-status', '❌ 失败', 'error');
                log(`❌ 项目管理器测试失败: ${error.message}`);
            }
        }
        
        // 启动完整系统
        async function startFullSystem() {
            log('🚀 启动完整增强系统...');
            
            try {
                // 加载所有增强模块
                await loadScript('js/managers/model-loader.js');
                await loadScript('js/managers/progress-manager.js');
                await loadScript('js/managers/enhanced-scene.js');
                await loadScript('js/managers/tool-manager.js');
                await loadScript('js/ui/project-ui.js');
                await loadScript('js/ui/progress-ui.js');
                await loadScript('js/tools/measurement-tool.js');
                
                log('✅ 所有增强模块加载完成');
                
                // 重定向到完整系统
                setTimeout(() => {
                    window.location.href = 'enhanced-index.html';
                }, 2000);
                
            } catch (error) {
                log(`❌ 完整系统启动失败: ${error.message}`);
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', async function() {
            log('📄 页面加载完成，开始系统检查...');
            
            // 更新时间
            updateTime();
            setInterval(updateTime, 1000);
            
            // 检查Three.js
            if (checkThreeJS()) {
                await loadExtensions();
                await testBasicScene();
                await testProjectManager();
                
                log('✅ 系统检查完成，可以启动完整系统');
                document.getElementById('current-project-name').textContent = '系统就绪';
                document.getElementById('current-project-status').textContent = '可以使用';
            }
        });
        
        // 事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            // 清空日志按钮
            document.getElementById('clear-log').addEventListener('click', () => {
                systemLog = [];
                document.getElementById('system-log').innerHTML = '';
            });
            
            // 测试按钮
            document.getElementById('test-three').addEventListener('click', checkThreeJS);
            document.getElementById('test-scene').addEventListener('click', testBasicScene);
            document.getElementById('test-project').addEventListener('click', testProjectManager);
            document.getElementById('start-full').addEventListener('click', startFullSystem);
            
            // 演示按钮
            document.getElementById('demo-bridge').addEventListener('click', () => {
                log('🌉 演示桥梁功能...');
                if (bridgeScene) {
                    log('✅ 基础桥梁场景可用');
                } else {
                    log('❌ 请先测试场景创建');
                }
            });
            
            document.getElementById('demo-progress').addEventListener('click', () => {
                log('📊 演示进度功能...');
                // 这里可以添加简单的进度演示
            });
            
            document.getElementById('demo-measurement').addEventListener('click', () => {
                log('📏 演示测量功能...');
                // 这里可以添加简单的测量演示
            });
        });
    </script>
    
    <style>
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }
        
        .value.success { color: #27ae60; }
        .value.error { color: #e74c3c; }
        .value.warning { color: #f39c12; }
        
        .test-btn, .demo-btn {
            width: 100%;
            margin: 5px 0;
            padding: 8px 12px;
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid #3498db;
            color: #ecf0f1;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover, .demo-btn:hover {
            background: rgba(52, 152, 219, 0.4);
        }
        
        .test-controls, .demo-controls {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
    </style>
</body>
</html>
