/* 增强版UI样式 */

/* 项目选择器样式 */
.project-selector-section {
    border-bottom: 2px solid #3498db !important;
}

.project-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.project-select {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
}

.project-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.project-select option {
    background: #2c3e50;
    color: #ecf0f1;
}

.refresh-btn {
    background: rgba(52, 152, 219, 0.2);
    border: 1px solid #3498db;
    color: #3498db;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: rgba(52, 152, 219, 0.3);
    transform: scale(1.05);
}

.refresh-btn i.fa-spin {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 项目信息显示 */
.project-info-display {
    margin-top: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid #3498db;
}

.project-detail {
    margin-bottom: 12px;
}

.project-name-display {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #ecf0f1;
    margin-bottom: 4px;
}

.project-description {
    display: block;
    font-size: 12px;
    color: #bdc3c7;
    line-height: 1.4;
}

.project-stats {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-label {
    font-size: 11px;
    color: #95a5a6;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 14px;
    font-weight: 500;
    color: #3498db;
    margin-top: 2px;
}

/* 模型管理器样式 */
.model-manager-section {
    max-height: 400px;
    overflow-y: auto;
}

.model-list {
    max-height: 250px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.no-models-message {
    text-align: center;
    padding: 20px;
    color: #95a5a6;
    font-size: 13px;
}

.no-models-message i {
    display: block;
    font-size: 24px;
    margin-bottom: 8px;
    color: #7f8c8d;
}

.model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border-left: 3px solid #3498db;
    transition: all 0.3s ease;
}

.model-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateX(2px);
}

.model-info {
    flex: 1;
}

.model-name {
    font-size: 13px;
    font-weight: 500;
    color: #ecf0f1;
    margin-bottom: 4px;
}

.model-details {
    display: flex;
    gap: 10px;
    margin-bottom: 2px;
}

.model-type {
    font-size: 11px;
    color: #3498db;
    background: rgba(52, 152, 219, 0.2);
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
}

.model-size {
    font-size: 11px;
    color: #95a5a6;
}

.model-description {
    font-size: 11px;
    color: #bdc3c7;
    line-height: 1.3;
}

.model-actions {
    display: flex;
    gap: 4px;
}

.model-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.model-action-btn:hover:not(:disabled) {
    background: rgba(52, 152, 219, 0.3);
    border-color: #3498db;
}

.model-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.model-controls {
    display: flex;
    gap: 8px;
}

.model-btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
    padding: 10px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
}

.model-btn:hover:not(:disabled) {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
}

.model-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 加载指示器 */
.loading-indicator {
    margin-top: 15px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.loading-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.loading-progress {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 2px;
    transition: width 0.3s ease;
    width: 0%;
}

.loading-text {
    font-size: 12px;
    color: #bdc3c7;
    text-align: center;
    display: block;
}

/* 进度控制面板样式 */
.progress-control-section {
    border-bottom: 2px solid #e74c3c !important;
}

.overall-progress {
    margin-bottom: 20px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-label {
    font-size: 13px;
    color: #ecf0f1;
    font-weight: 500;
}

.progress-value {
    font-size: 16px;
    font-weight: 700;
    color: #3498db;
}

.progress-bar-large {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill-large {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #f1c40f, #2ecc71);
    border-radius: 4px;
    transition: width 0.5s ease;
    width: 0%;
}

/* 时间轴控制 */
.timeline-container {
    margin-bottom: 20px;
}

.timeline-label {
    display: block;
    font-size: 13px;
    color: #ecf0f1;
    margin-bottom: 10px;
}

.timeline-wrapper {
    position: relative;
    margin-bottom: 8px;
}

.timeline-slider {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
}

.timeline-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.timeline-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.timeline-markers {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    pointer-events: none;
}

.timeline-marker {
    position: absolute;
    width: 3px;
    height: 6px;
    border-radius: 1px;
    transform: translateX(-50%);
}

.timeline-labels {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: #95a5a6;
}

/* 播放控制 */
.playback-controls {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.playback-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.playback-btn:hover {
    background: rgba(52, 152, 219, 0.3);
    border-color: #3498db;
}

/* 阶段管理器样式 */
.phase-manager-section {
    max-height: 500px;
    overflow-y: auto;
}

.phase-list {
    max-height: 350px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.phase-item {
    margin-bottom: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.phase-item:hover {
    background: rgba(255, 255, 255, 0.08);
}

.phase-item.active {
    border: 2px solid #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.phase-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    cursor: pointer;
}

.phase-info {
    flex: 1;
}

.phase-name {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #ecf0f1;
    margin-bottom: 4px;
}

.phase-dates {
    font-size: 11px;
    color: #95a5a6;
}

.phase-progress-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.phase-progress-text {
    font-size: 14px;
    font-weight: 600;
    color: #ecf0f1;
    min-width: 35px;
    text-align: right;
}

.phase-color-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.phase-progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    margin: 0 12px;
}

.phase-progress-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 2px;
}

.phase-controls {
    padding: 8px 12px 12px;
}

.phase-slider {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
}

.phase-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px;
    height: 12px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
}

.phase-slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: #3498db;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.phase-nav-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ecf0f1;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.phase-nav-btn:hover {
    background: rgba(52, 152, 219, 0.3);
    border-color: #3498db;
}

/* 响应式设计增强 */
@media (max-width: 1400px) {
    .left-panel,
    .right-panel {
        width: 260px;
    }
    
    .model-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .model-actions {
        align-self: flex-end;
    }
}

@media (max-width: 1200px) {
    .project-stats {
        flex-direction: column;
        gap: 8px;
    }
    
    .stat-item {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .phase-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .phase-progress-display {
        align-self: flex-end;
    }
}

@media (max-width: 768px) {
    .project-controls {
        flex-direction: column;
    }
    
    .model-controls {
        flex-direction: column;
    }
    
    .playback-controls {
        flex-wrap: wrap;
        gap: 6px;
    }
    
    .playback-btn {
        flex: 1;
        min-width: 60px;
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

/* 滚动条美化 */
.model-list::-webkit-scrollbar,
.phase-list::-webkit-scrollbar {
    width: 4px;
}

.model-list::-webkit-scrollbar-track,
.phase-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 2px;
}

.model-list::-webkit-scrollbar-thumb,
.phase-list::-webkit-scrollbar-thumb {
    background: #3498db;
    border-radius: 2px;
}

.model-list::-webkit-scrollbar-thumb:hover,
.phase-list::-webkit-scrollbar-thumb:hover {
    background: #2980b9;
}

/* 工具提示样式 */
.tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 10001;
    max-width: 200px;
    word-wrap: break-word;
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}
