/**
 * 场景服务
 * 重构后的3D场景管理，提供统一的场景操作接口
 */

class SceneService {
    constructor(core) {
        this.core = core;
        this.sceneManager = null;
        this.modelService = null;
        
        // 场景中的模型组织
        this.sceneModels = new Map(); // 场景中的模型实例
        this.modelGroups = new Map(); // 按类型分组的模型
        this.phaseModels = new Map(); // 按阶段分组的模型
        
        // 场景状态
        this.currentProject = null;
        this.loadingState = {
            isLoading: false,
            progress: 0,
            currentModel: null,
            totalModels: 0
        };
        
        // 性能监控
        this.performanceStats = {
            triangleCount: 0,
            drawCalls: 0,
            memoryUsage: 0,
            fps: 0
        };
    }

    /**
     * 初始化服务
     */
    async initialize() {
        console.log('🌟 初始化场景服务');
        
        // 检查依赖是否已设置
        if (!this.sceneManager || !this.modelService) {
            console.warn('⚠️ 场景服务依赖未完全设置，将在设置后初始化事件监听');
            return;
        }
        
        this.setupEventListeners();
        this.setupPerformanceMonitoring();
        
        console.log('✅ 场景服务初始化完成');
    }

    /**
     * 设置事件监听（在依赖设置后调用）
     */
    setupEventListeners() {
        if (!this.core.eventBus) return;
        
        // 监听项目变化事件
        this.core.eventBus.on('project:loaded', (data) => {
            this.onProjectLoaded(data.project);
        });
        
        this.core.eventBus.on('project:unloading', (data) => {
            this.onProjectUnloading(data.project);
        });
        
        // 监听模型加载事件
        this.core.eventBus.on('model:loaded', (data) => {
            this.onModelLoaded(data);
        });
        
        console.log('📡 场景服务事件监听已设置');
    }

    /**
     * 加载项目到场景
     */
    async loadProjectToScene(project) {
        try {
            console.log(`🏗️ 加载项目到场景: ${project.name}`);
            
            this.loadingState.isLoading = true;
            this.loadingState.progress = 0;
            
            // 清理现有场景
            await this.clearScene();
            
            // 设置当前项目
            this.currentProject = project;
            
            // 获取项目模型列表
            const projectService = await this.core.getService('project');
            const models = projectService.getProjectModels(project.id);
            
            this.loadingState.totalModels = models.length;
            
            if (models.length === 0) {
                console.warn('⚠️ 项目中没有模型定义');
                this.loadingState.isLoading = false;
                return;
            }
            
            // 并发加载所有模型
            const loadPromises = models.map((modelInfo, index) => 
                this.loadModelToScene(modelInfo, index)
            );
            
            const results = await Promise.allSettled(loadPromises);
            
            // 统计结果
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.length - successful;
            
            console.log(`✅ 场景加载完成: 成功 ${successful}/${models.length}, 失败 ${failed}`);
            
            // 更新场景设置
            this.applyProjectSettings(project);
            
            // 更新统计信息
            this.updatePerformanceStats();
            
            this.loadingState.isLoading = false;
            this.loadingState.progress = 100;
            
            // 触发事件
            this.core.eventBus.emit('scene:projectLoaded', {
                project,
                successful,
                failed,
                totalModels: models.length
            });
            
        } catch (error) {
            this.loadingState.isLoading = false;
            console.error('❌ 项目加载到场景失败:', error);
            this.core.errorHandler.handleError('场景加载失败', error);
            throw error;
        }
    }

    /**
     * 加载单个模型到场景
     */
    async loadModelToScene(modelInfo, index = 0) {
        try {
            console.log(`📦 加载模型到场景: ${modelInfo.name}`);
            
            this.loadingState.currentModel = modelInfo.name;
            
            // 检查模型服务是否可用
            if (!this.modelService) {
                throw new Error('模型服务不可用');
            }
            
            // 使用模型服务加载模型
            const gltf = await this.modelService.loadModel(modelInfo, {
                onProgress: (percent) => {
                    const baseProgress = (index / this.loadingState.totalModels) * 100;
                    const currentProgress = (percent / this.loadingState.totalModels);
                    this.loadingState.progress = baseProgress + currentProgress;
                    
                    this.core.eventBus.emit('scene:loadProgress', {
                        progress: this.loadingState.progress,
                        currentModel: modelInfo.name,
                        modelProgress: percent
                    });
                },
                optimize: true,
                useCache: true
            });
            
            // 添加模型到场景
            this.addModelToScene(modelInfo, gltf);
            
            return gltf;
            
        } catch (error) {
            console.error(`❌ 模型加载失败: ${modelInfo.name}`, error);
            
            // 创建错误占位符
            this.createErrorPlaceholder(modelInfo);
            
            throw error;
        }
    }

    /**
     * 添加模型到场景
     */
    addModelToScene(modelInfo, gltf) {
        const model = gltf.scene;
        model.name = modelInfo.name;
        
        // 设置模型用户数据
        model.userData = {
            ...model.userData,
            modelInfo: modelInfo,
            addedToScene: Date.now(),
            visible: modelInfo.visible !== false
        };
        
        // 添加到Three.js场景
        if (this.sceneManager && this.sceneManager.scene) {
            this.sceneManager.scene.add(model);
        } else {
            throw new Error('场景管理器不可用');
        }
        
        // 注册到管理系统
        this.registerSceneModel(modelInfo, model, gltf);
        
        // 设置初始可见性
        model.visible = modelInfo.visible !== false;
        
        console.log(`➕ 模型已添加到场景: ${modelInfo.name}`);
        
        // 触发事件
        this.core.eventBus.emit('scene:modelAdded', {
            modelInfo,
            model,
            gltf
        });
    }

    /**
     * 注册场景模型
     */
    registerSceneModel(modelInfo, model, gltf) {
        const modelId = this.generateModelId(modelInfo);
        
        // 注册到总管理器
        this.sceneModels.set(modelId, {
            modelInfo,
            model,
            gltf,
            addedAt: Date.now()
        });
        
        // 按类型分组
        if (modelInfo.type) {
            if (!this.modelGroups.has(modelInfo.type)) {
                this.modelGroups.set(modelInfo.type, []);
            }
            this.modelGroups.get(modelInfo.type).push({
                modelId,
                model,
                modelInfo
            });
        }
        
        // 按阶段分组
        if (modelInfo.phase) {
            if (!this.phaseModels.has(modelInfo.phase)) {
                this.phaseModels.set(modelInfo.phase, []);
            }
            this.phaseModels.get(modelInfo.phase).push({
                modelId,
                model,
                modelInfo
            });
        }
    }

    /**
     * 生成模型ID
     */
    generateModelId(modelInfo) {
        return `${modelInfo.name}_${modelInfo.phase || 'default'}_${Date.now()}`;
    }

    /**
     * 创建错误占位符
     */
    createErrorPlaceholder(modelInfo) {
        const geometry = new THREE.BoxGeometry(10, 10, 10);
        const material = new THREE.MeshStandardMaterial({ 
            color: 0xff0000,
            wireframe: true 
        });
        const placeholder = new THREE.Mesh(geometry, material);
        
        placeholder.name = `${modelInfo.name}_ERROR`;
        placeholder.userData = {
            modelInfo: modelInfo,
            isErrorPlaceholder: true,
            addedToScene: Date.now()
        };
        
        // 应用变换
        if (modelInfo.position) {
            placeholder.position.fromArray(modelInfo.position);
        }
        if (modelInfo.rotation) {
            placeholder.rotation.fromArray(modelInfo.rotation);
        }
        if (modelInfo.scale) {
            placeholder.scale.fromArray(modelInfo.scale);
        }
        
        this.sceneManager.scene.add(placeholder);
        
        console.log(`🔴 错误占位符已创建: ${modelInfo.name}`);
    }

    /**
     * 应用项目设置
     */
    applyProjectSettings(project) {
        const config = project.config;
        
        // 应用相机设置
        if (config.camera) {
            this.applyCameraSettings(config.camera);
        }
        
        // 应用光照设置
        if (config.lighting) {
            this.applyLightingSettings(config.lighting);
        }
        
        console.log(`⚙️ 项目设置已应用: ${project.name}`);
    }

    /**
     * 应用相机设置
     */
    applyCameraSettings(cameraConfig) {
        const camera = this.sceneManager.getCamera();
        const controls = this.sceneManager.controls;
        
        if (cameraConfig.position) {
            camera.position.fromArray(cameraConfig.position);
        }
        
        if (cameraConfig.target && controls.getOrbitControls) {
            const orbitControls = controls.getOrbitControls();
            orbitControls.target.fromArray(cameraConfig.target);
        }
        
        if (cameraConfig.fov && camera.isPerspectiveCamera) {
            camera.fov = cameraConfig.fov;
            camera.updateProjectionMatrix();
        }
        
        if (cameraConfig.near) {
            camera.near = cameraConfig.near;
        }
        
        if (cameraConfig.far) {
            camera.far = cameraConfig.far;
        }
        
        if (cameraConfig.near || cameraConfig.far) {
            camera.updateProjectionMatrix();
        }
        
        console.log('📷 相机设置已应用');
    }

    /**
     * 应用光照设置
     */
    applyLightingSettings(lightingConfig) {
        // 查找现有光源
        const ambientLight = this.sceneManager.scene.children.find(
            child => child.isAmbientLight
        );
        const directionalLight = this.sceneManager.scene.children.find(
            child => child.isDirectionalLight
        );
        
        // 更新环境光
        if (ambientLight && lightingConfig.ambientLight) {
            ambientLight.color.setHex(lightingConfig.ambientLight);
        }
        
        // 更新定向光
        if (directionalLight && lightingConfig.directionalLight) {
            const dirConfig = lightingConfig.directionalLight;
            
            if (dirConfig.color) {
                directionalLight.color.setHex(dirConfig.color);
            }
            
            if (dirConfig.position) {
                directionalLight.position.fromArray(dirConfig.position);
            }
            
            if (dirConfig.castShadow !== undefined) {
                directionalLight.castShadow = dirConfig.castShadow;
            }
        }
        
        // 更新整体光照强度
        if (lightingConfig.intensity) {
            this.sceneManager.scene.children.forEach(child => {
                if (child.isLight && child.intensity !== undefined) {
                    child.intensity = lightingConfig.intensity;
                }
            });
        }
        
        console.log('💡 光照设置已应用');
    }

    /**
     * 按类型切换模型组可见性
     */
    toggleModelGroupVisibility(type, visible) {
        const group = this.modelGroups.get(type);
        if (!group) {
            console.warn(`⚠️ 模型组不存在: ${type}`);
            return;
        }
        
        group.forEach(({ model }) => {
            model.visible = visible;
        });
        
        console.log(`👁️ ${visible ? '显示' : '隐藏'}模型组: ${type} (${group.length}个模型)`);
        
        this.core.eventBus.emit('scene:groupVisibilityChanged', {
            type,
            visible,
            modelCount: group.length
        });
    }

    /**
     * 按阶段切换模型可见性
     */
    togglePhaseVisibility(phaseId, visible) {
        const models = this.phaseModels.get(phaseId);
        if (!models) {
            console.warn(`⚠️ 阶段模型不存在: ${phaseId}`);
            return;
        }
        
        models.forEach(({ model }) => {
            model.visible = visible;
        });
        
        console.log(`👁️ ${visible ? '显示' : '隐藏'}阶段模型: ${phaseId} (${models.length}个模型)`);
        
        this.core.eventBus.emit('scene:phaseVisibilityChanged', {
            phaseId,
            visible,
            modelCount: models.length
        });
    }

    /**
     * 获取场景统计信息
     */
    getSceneStats() {
        return {
            totalModels: this.sceneModels.size,
            modelGroups: Array.from(this.modelGroups.keys()).map(type => ({
                type,
                count: this.modelGroups.get(type).length
            })),
            phases: Array.from(this.phaseModels.keys()).map(phase => ({
                phase,
                count: this.phaseModels.get(phase).length
            })),
            performance: this.performanceStats,
            currentProject: this.currentProject?.name || null
        };
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 每秒更新性能统计
        setInterval(() => {
            this.updatePerformanceStats();
        }, 1000);
    }

    /**
     * 更新性能统计
     */
    updatePerformanceStats() {
        const renderer = this.sceneManager.getRenderer();
        const info = renderer.info;
        
        this.performanceStats = {
            triangleCount: info.render.triangles,
            drawCalls: info.render.calls,
            memoryUsage: this.calculateSceneMemoryUsage(),
            fps: this.sceneManager.performanceMonitor?.fps || 0,
            geometries: info.memory.geometries,
            textures: info.memory.textures
        };
    }

    /**
     * 计算场景内存使用
     */
    calculateSceneMemoryUsage() {
        let memoryUsage = 0;
        
        this.sceneModels.forEach(({ model }) => {
            model.traverse((child) => {
                if (child.isMesh && child.geometry) {
                    const attributes = child.geometry.attributes;
                    for (const name in attributes) {
                        memoryUsage += attributes[name].array.byteLength;
                    }
                    
                    if (child.geometry.index) {
                        memoryUsage += child.geometry.index.array.byteLength;
                    }
                }
            });
        });
        
        return Math.round(memoryUsage / 1024 / 1024); // MB
    }

    /**
     * 清理场景
     */
    async clearScene() {
        console.log('🧹 清理场景');
        
        // 移除所有场景模型
        this.sceneModels.forEach(({ model }) => {
            this.sceneManager.scene.remove(model);
        });
        
        // 清理数据结构
        this.sceneModels.clear();
        this.modelGroups.clear();
        this.phaseModels.clear();
        
        // 重置状态
        this.currentProject = null;
        this.loadingState = {
            isLoading: false,
            progress: 0,
            currentModel: null,
            totalModels: 0
        };
        
        console.log('✅ 场景清理完成');
        
        this.core.eventBus.emit('scene:cleared');
    }

    /**
     * 事件处理器
     */
    async onProjectLoaded(project) {
        console.log(`📨 收到项目加载事件: ${project.name}`);
        await this.loadProjectToScene(project);
    }

    async onProjectUnloading(project) {
        console.log(`📨 收到项目卸载事件: ${project.name}`);
        await this.clearScene();
    }

    onModelLoaded(data) {
        console.log(`📨 收到模型加载事件: ${data.modelInfo.name}`);
    }

    /**
     * 获取模型组列表
     */
    getModelGroups() {
        const groups = [];
        this.modelGroups.forEach((models, type) => {
            groups.push({
                type,
                count: models.length,
                visible: models.every(({ model }) => model.visible)
            });
        });
        return groups;
    }

    /**
     * 获取阶段模型列表
     */
    getPhaseModels() {
        const phases = [];
        this.phaseModels.forEach((models, phaseId) => {
            phases.push({
                phaseId,
                count: models.length,
                visible: models.every(({ model }) => model.visible)
            });
        });
        return phases;
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('🧹 清理场景服务资源');
        
        await this.clearScene();
        
        console.log('✅ 场景服务资源清理完成');
    }
}