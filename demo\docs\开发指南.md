# 增强版路桥修建演示系统 - 开发指南

## 1. 开发环境设置

### 1.1 必需工具
- **代码编辑器**：VS Code（推荐）
- **浏览器**：Chrome（用于调试）
- **本地服务器**：Live Server扩展或Python HTTP服务器
- **版本控制**：Git

### 1.2 推荐扩展（VS Code）
- Live Server
- ES6 String HTML
- JavaScript (ES6) code snippets
- Three.js Snippets

### 1.3 本地服务器启动
```bash
# 使用Python（如果已安装）
python -m http.server 8000

# 或使用Node.js
npx http-server -p 8000

# 或使用VS Code Live Server扩展
```

## 2. 项目结构说明

### 2.1 核心文件结构
```
增强版路桥演示系统/
├── enhanced-index.html          # 主页面
├── js/
│   ├── managers/               # 管理器模块
│   │   ├── project-manager.js  # 项目管理
│   │   ├── model-loader.js     # 模型加载
│   │   ├── progress-manager.js # 进度管理
│   │   ├── enhanced-scene.js   # 增强场景
│   │   └── tool-manager.js     # 工具管理
│   ├── ui/                     # UI模块
│   │   ├── project-ui.js       # 项目界面
│   │   └── progress-ui.js      # 进度界面
│   ├── tools/                  # 工具模块
│   │   └── measurement-tool.js # 测量工具
│   └── enhanced-app.js         # 主应用
├── styles/
│   └── enhanced-ui.css         # 增强UI样式
├── projects/                   # 项目文件夹
│   ├── bridge-demo/           # 示例项目
│   └── highway-project/       # 高速公路项目
└── docs/                      # 文档
```

### 2.2 模块依赖关系
```
EnhancedBridgeApp
├── ProjectManager
├── EnhancedBridgeScene
│   ├── GLTFModelLoader
│   └── ProgressManager
├── ToolManager
│   └── MeasurementTool
└── UI Managers
    ├── ProjectUI
    └── ProgressUI
```

## 3. 核心API参考

### 3.1 ProjectManager API

```javascript
// 获取可用项目
const projects = projectManager.getAvailableProjects();

// 切换项目
await projectManager.switchProject('projects/bridge-demo');

// 获取当前项目
const currentProject = projectManager.getCurrentProject();

// 保存项目配置
await projectManager.saveProjectConfig(config);
```

### 3.2 GLTFModelLoader API

```javascript
// 加载模型
const gltf = await modelLoader.loadModel('path/to/model.gltf', {
    onProgress: (percent) => console.log(`加载进度: ${percent}%`),
    optimize: true,
    useCache: true
});

// 卸载模型
modelLoader.unloadModel('path/to/model.gltf');

// 获取加载统计
const stats = modelLoader.getLoadingStats();
```

### 3.3 ProgressManager API

```javascript
// 更新阶段进度
progressManager.updatePhaseProgress('foundation', 85);

// 设置时间轴进度
progressManager.setTimelineProgress(60);

// 切换到指定阶段
progressManager.switchToPhase('superstructure');

// 获取进度数据
const progressData = progressManager.getProgressData();
```

### 3.4 MeasurementTool API

```javascript
// 激活测量工具
measurementTool.activate('distance');

// 停用测量工具
measurementTool.deactivate();

// 获取测量结果
const measurements = measurementTool.getMeasurements();

// 导出测量数据
measurementTool.exportMeasurements();
```

## 4. 扩展开发

### 4.1 添加新工具

1. 在 `js/tools/` 文件夹创建新工具文件
2. 实现工具类，包含以下方法：
   ```javascript
   class NewTool {
       constructor(scene, camera, renderer) { }
       activate() { }
       deactivate() { }
       cleanup() { }
   }
   ```
3. 在 `ToolManager` 中注册新工具
4. 在UI中添加工具按钮

### 4.2 添加新的测量模式

```javascript
// 在MeasurementTool中添加新模式
handleNewMeasurementMode() {
    // 实现新的测量逻辑
}

// 在activate方法中添加新模式支持
activate(mode = 'distance') {
    if (mode === 'newMode') {
        // 设置新模式的特定配置
    }
}
```

### 4.3 自定义材质和效果

```javascript
// 在materials.js中添加新材质
createCustomMaterial() {
    return new THREE.MeshStandardMaterial({
        color: 0x自定义颜色,
        roughness: 0.5,
        metalness: 0.1,
        // 其他属性
    });
}
```

### 4.4 添加新的UI组件

1. 在相应的UI文件中添加HTML结构
2. 在CSS文件中添加样式
3. 在JavaScript中添加交互逻辑
4. 确保响应式设计兼容性

## 5. 性能优化

### 5.1 模型优化
- 使用Draco压缩减少文件大小
- 合理设置LOD（细节层次）
- 优化纹理分辨率
- 减少不必要的几何体细节

### 5.2 渲染优化
- 使用对象池重用几何体
- 合理设置渲染距离
- 启用视锥体剔除
- 优化材质和着色器

### 5.3 内存管理
- 及时清理不需要的模型
- 使用模型缓存避免重复加载
- 监控内存使用情况
- 实现垃圾回收策略

## 6. 调试技巧

### 6.1 控制台调试
```javascript
// 获取系统状态
console.log(window.debugEnhancedBridge.getSystemInfo());

// 查看当前项目
console.log(window.debugEnhancedBridge.getCurrentProject());

// 监控性能
console.log('FPS:', bridgeApp.performanceMonitor.fps);
```

### 6.2 Three.js调试
```javascript
// 显示辅助对象
scene.add(new THREE.AxesHelper(100));
scene.add(new THREE.GridHelper(1000, 100));

// 线框模式
material.wireframe = true;

// 显示边界框
const box = new THREE.BoxHelper(object, 0xffff00);
scene.add(box);
```

### 6.3 性能分析
- 使用浏览器的Performance面板
- 监控Three.js的渲染统计
- 检查内存使用情况
- 分析加载时间

## 7. 部署指南

### 7.1 生产环境部署
1. 优化所有资源文件
2. 配置CDN加速
3. 启用Gzip压缩
4. 设置适当的缓存策略

### 7.2 服务器配置
```nginx
# Nginx配置示例
location ~* \.(gltf|glb|bin)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    gzip on;
    gzip_types application/octet-stream;
}
```

### 7.3 CDN配置
- 将大型模型文件部署到CDN
- 配置适当的CORS头
- 启用HTTP/2支持

## 8. 测试策略

### 8.1 功能测试
- 测试所有交互功能
- 验证模型加载和显示
- 检查进度控制功能
- 测试测量工具精度

### 8.2 性能测试
- 测试大型模型加载性能
- 验证内存使用情况
- 检查渲染帧率
- 测试多项目切换性能

### 8.3 兼容性测试
- 测试不同浏览器兼容性
- 验证移动设备支持
- 检查不同屏幕分辨率

## 9. 贡献指南

### 9.1 代码规范
- 使用ES6+语法
- 遵循驼峰命名规范
- 添加详细的注释
- 保持代码整洁和可读性

### 9.2 提交规范
- 使用清晰的提交信息
- 每个功能使用独立的分支
- 提交前进行充分测试
- 更新相关文档

### 9.3 文档维护
- 及时更新API文档
- 添加新功能的使用说明
- 维护故障排除指南
- 更新版本变更日志

## 10. 未来规划

### 10.1 计划功能
- VR/AR支持
- 多用户协作
- 云端项目同步
- 高级动画系统
- 物理仿真集成

### 10.2 技术升级
- 升级到最新版Three.js
- 集成WebGPU支持
- 优化移动端性能
- 增强安全性

---

*本开发指南将随系统发展持续更新。*
