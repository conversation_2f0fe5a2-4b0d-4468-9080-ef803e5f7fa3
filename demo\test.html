<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #2c3e50;
            color: white;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            background: #34495e;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }
        .status {
            font-weight: bold;
            margin-left: 10px;
        }
        .success { color: #2ecc71; }
        .error { color: #e74c3c; }
        .warning { color: #f39c12; }
        #canvas-container {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 2px solid #3498db;
            border-radius: 8px;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 路桥数字孪生系统 - 兼容性测试</h1>
        
        <div class="test-item">
            <h3>浏览器信息</h3>
            <p>用户代理: <span id="user-agent"></span></p>
            <p>浏览器: <span id="browser-name"></span></p>
        </div>

        <div class="test-item">
            <h3>WebGL 支持测试</h3>
            <p>WebGL 1.0: <span id="webgl1-status" class="status">检测中...</span></p>
            <p>WebGL 2.0: <span id="webgl2-status" class="status">检测中...</span></p>
            <p>WebGL 扩展: <span id="webgl-extensions" class="status">检测中...</span></p>
        </div>

        <div class="test-item">
            <h3>Three.js 库加载测试</h3>
            <p>Three.js 核心库: <span id="threejs-status" class="status">检测中...</span></p>
            <p>OrbitControls: <span id="controls-status" class="status">检测中...</span></p>
            <p>EffectComposer: <span id="composer-status" class="status">检测中...</span></p>
            <p>Three.js 版本: <span id="threejs-version" class="status">-</span></p>
        </div>

        <div class="test-item">
            <h3>基础API支持测试</h3>
            <p>requestAnimationFrame: <span id="raf-status" class="status">检测中...</span></p>
            <p>addEventListener: <span id="event-status" class="status">检测中...</span></p>
            <p>querySelector: <span id="query-status" class="status">检测中...</span></p>
            <p>Canvas API: <span id="canvas-status" class="status">检测中...</span></p>
        </div>

        <div class="test-item">
            <h3>Three.js 渲染测试</h3>
            <div id="canvas-container">
                <canvas id="test-canvas" width="400" height="300"></canvas>
            </div>
            <button onclick="startRenderTest()">开始渲染测试</button>
            <button onclick="location.href='index.html'">启动主应用</button>
        </div>

        <div class="test-item">
            <h3>测试结果</h3>
            <p>总体状态: <span id="overall-status" class="status">检测中...</span></p>
            <div id="recommendations"></div>
        </div>
    </div>

    <!-- Three.js 库 - 使用BootCDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/0.158.0/three.min.js"></script>
    <script>
        // 备用CDN加载
        window.addEventListener('load', function() {
            if (typeof THREE === 'undefined') {
                console.warn('BootCDN加载失败，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/three@0.158.0/build/three.min.js';
                script.onload = function() {
                    console.log('备用CDN加载成功');
                    window.location.reload();
                };
                document.head.appendChild(script);
            }
        });
    </script>

    <script>
        // 设置状态显示函数
        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        // 浏览器信息检测
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            document.getElementById('user-agent').textContent = userAgent;
            
            let browserName = 'Unknown';
            if (userAgent.includes('Chrome')) browserName = 'Chrome';
            else if (userAgent.includes('Firefox')) browserName = 'Firefox';
            else if (userAgent.includes('Safari')) browserName = 'Safari';
            else if (userAgent.includes('Edge')) browserName = 'Edge';
            else if (userAgent.includes('Opera')) browserName = 'Opera';
            
            document.getElementById('browser-name').textContent = browserName;
        }

        // WebGL 支持检测
        function testWebGL() {
            try {
                const canvas = document.createElement('canvas');
                const gl1 = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                const gl2 = canvas.getContext('webgl2');
                
                if (gl1) {
                    setStatus('webgl1-status', 'success', '✅ 支持');
                    const extensions = gl1.getSupportedExtensions();
                    setStatus('webgl-extensions', 'success', `✅ ${extensions.length} 个扩展`);
                } else {
                    setStatus('webgl1-status', 'error', '❌ 不支持');
                    setStatus('webgl-extensions', 'error', '❌ 无法检测');
                }
                
                if (gl2) {
                    setStatus('webgl2-status', 'success', '✅ 支持');
                } else {
                    setStatus('webgl2-status', 'warning', '⚠️ 不支持');
                }
            } catch (e) {
                setStatus('webgl1-status', 'error', '❌ 检测失败');
                setStatus('webgl2-status', 'error', '❌ 检测失败');
                setStatus('webgl-extensions', 'error', '❌ 检测失败');
            }
        }

        // Three.js 库检测
        function testThreeJS() {
            setTimeout(() => {
                if (typeof THREE !== 'undefined') {
                    setStatus('threejs-status', 'success', '✅ 已加载');
                    setStatus('threejs-version', 'success', THREE.REVISION || 'Unknown');
                } else {
                    setStatus('threejs-status', 'error', '❌ 未加载');
                    setStatus('threejs-version', 'error', '❌ 无法检测');
                }

                if (typeof THREE !== 'undefined' && THREE.OrbitControls) {
                    setStatus('controls-status', 'success', '✅ 已加载');
                } else {
                    setStatus('controls-status', 'error', '❌ 未加载');
                }

                if (typeof THREE !== 'undefined' && THREE.EffectComposer) {
                    setStatus('composer-status', 'success', '✅ 已加载');
                } else {
                    setStatus('composer-status', 'error', '❌ 未加载');
                }
            }, 1000);
        }

        // 基础API检测
        function testBasicAPIs() {
            setStatus('raf-status', 
                typeof requestAnimationFrame !== 'undefined' ? 'success' : 'error',
                typeof requestAnimationFrame !== 'undefined' ? '✅ 支持' : '❌ 不支持'
            );

            setStatus('event-status',
                typeof addEventListener !== 'undefined' ? 'success' : 'error',
                typeof addEventListener !== 'undefined' ? '✅ 支持' : '❌ 不支持'
            );

            setStatus('query-status',
                typeof document.querySelector !== 'undefined' ? 'success' : 'error',
                typeof document.querySelector !== 'undefined' ? '✅ 支持' : '❌ 不支持'
            );

            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                setStatus('canvas-status', ctx ? 'success' : 'error', ctx ? '✅ 支持' : '❌ 不支持');
            } catch (e) {
                setStatus('canvas-status', 'error', '❌ 检测失败');
            }
        }

        // Three.js 渲染测试
        function startRenderTest() {
            if (typeof THREE === 'undefined') {
                alert('Three.js 未加载，无法进行渲染测试');
                return;
            }

            try {
                const canvas = document.getElementById('test-canvas');
                const renderer = new THREE.WebGLRenderer({ canvas: canvas });
                renderer.setSize(400, 300);

                const scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87ceeb);

                const camera = new THREE.PerspectiveCamera(75, 400/300, 0.1, 1000);
                camera.position.z = 5;

                // 创建一个简单的立方体
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshBasicMaterial({ color: 0x3498db });
                const cube = new THREE.Mesh(geometry, material);
                scene.add(cube);

                // 渲染
                function animate() {
                    requestAnimationFrame(animate);
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    renderer.render(scene, camera);
                }

                animate();
                alert('✅ 渲染测试成功！您应该能看到一个旋转的蓝色立方体。');
            } catch (e) {
                alert('❌ 渲染测试失败: ' + e.message);
            }
        }

        // 总体评估
        function evaluateOverall() {
            setTimeout(() => {
                const tests = [
                    'webgl1-status', 'threejs-status', 'raf-status', 
                    'event-status', 'query-status', 'canvas-status'
                ];
                
                let passed = 0;
                let total = tests.length;
                
                tests.forEach(testId => {
                    const element = document.getElementById(testId);
                    if (element.classList.contains('success')) {
                        passed++;
                    }
                });
                
                const percentage = (passed / total) * 100;
                const recommendations = document.getElementById('recommendations');
                
                if (percentage >= 100) {
                    setStatus('overall-status', 'success', '✅ 完全兼容');
                    recommendations.innerHTML = '<p style="color: #2ecc71;">🎉 您的浏览器完全支持路桥数字孪生系统！可以正常使用所有功能。</p>';
                } else if (percentage >= 80) {
                    setStatus('overall-status', 'warning', '⚠️ 基本兼容');
                    recommendations.innerHTML = '<p style="color: #f39c12;">⚠️ 您的浏览器基本支持系统功能，但可能存在一些限制。</p>';
                } else {
                    setStatus('overall-status', 'error', '❌ 兼容性问题');
                    recommendations.innerHTML = `
                        <p style="color: #e74c3c;">❌ 您的浏览器存在兼容性问题，建议：</p>
                        <ul>
                            <li>更新浏览器到最新版本</li>
                            <li>启用硬件加速</li>
                            <li>检查网络连接</li>
                            <li>尝试使用 Chrome、Firefox 或新版 Edge</li>
                        </ul>
                    `;
                }
            }, 2000);
        }

        // 页面加载完成后开始测试
        window.addEventListener('load', () => {
            detectBrowser();
            testWebGL();
            testThreeJS();
            testBasicAPIs();
            evaluateOverall();
        });
    </script>
</body>
</html>
