/**
 * 项目UI管理器
 * 负责项目选择、模型管理等界面功能
 */

class ProjectUI {
    constructor(projectManager, sceneManager) {
        this.projectManager = projectManager;
        this.sceneManager = sceneManager;
        
        this.elements = {};
        this.currentProject = null;
        
        this.init();
    }

    /**
     * 初始化项目UI
     */
    init() {
        console.log('🎨 初始化项目UI...');
        
        this.createProjectSelector();
        this.createModelManager();
        this.setupEventListeners();
        
        console.log('✅ 项目UI初始化完成');
    }

    /**
     * 创建项目选择器
     */
    createProjectSelector() {
        // 在左侧面板顶部添加项目选择区域
        const leftPanel = document.querySelector('.left-panel');
        
        const projectSection = document.createElement('div');
        projectSection.className = 'panel-section project-selector-section';
        projectSection.innerHTML = `
            <h3><i class="fas fa-folder-open"></i> 项目选择</h3>
            <div class="project-controls">
                <select id="project-dropdown" class="project-select">
                    <option value="">选择项目...</option>
                </select>
                <button id="refresh-projects" class="refresh-btn" title="刷新项目列表">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="project-info-display" id="project-info-display" style="display: none;">
                <div class="project-detail">
                    <span class="project-name-display" id="project-name-display"></span>
                    <span class="project-description" id="project-description"></span>
                </div>
                <div class="project-stats">
                    <div class="stat-item">
                        <span class="stat-label">模型数量</span>
                        <span class="stat-value" id="model-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总大小</span>
                        <span class="stat-value" id="total-size">0MB</span>
                    </div>
                </div>
            </div>
        `;
        
        // 插入到第一个位置
        leftPanel.insertBefore(projectSection, leftPanel.firstChild);
        
        // 缓存元素
        this.elements.projectDropdown = document.getElementById('project-dropdown');
        this.elements.refreshButton = document.getElementById('refresh-projects');
        this.elements.projectInfoDisplay = document.getElementById('project-info-display');
        this.elements.projectNameDisplay = document.getElementById('project-name-display');
        this.elements.projectDescription = document.getElementById('project-description');
        this.elements.modelCount = document.getElementById('model-count');
        this.elements.totalSize = document.getElementById('total-size');
    }

    /**
     * 创建模型管理器
     */
    createModelManager() {
        const leftPanel = document.querySelector('.left-panel');
        
        const modelSection = document.createElement('div');
        modelSection.className = 'panel-section model-manager-section';
        modelSection.innerHTML = `
            <h3><i class="fas fa-cubes"></i> 模型管理</h3>
            <div class="model-list" id="model-list">
                <div class="no-models-message">
                    <i class="fas fa-info-circle"></i>
                    <span>请先选择一个项目</span>
                </div>
            </div>
            <div class="model-controls">
                <button id="load-all-models" class="model-btn" disabled>
                    <i class="fas fa-download"></i>
                    <span>加载全部</span>
                </button>
                <button id="unload-all-models" class="model-btn" disabled>
                    <i class="fas fa-trash"></i>
                    <span>卸载全部</span>
                </button>
            </div>
            <div class="loading-indicator" id="model-loading-indicator" style="display: none;">
                <div class="loading-bar">
                    <div class="loading-progress" id="model-loading-progress"></div>
                </div>
                <span class="loading-text" id="model-loading-text">加载中...</span>
            </div>
        `;
        
        // 插入到项目选择器后面
        const projectSection = leftPanel.querySelector('.project-selector-section');
        leftPanel.insertBefore(modelSection, projectSection.nextSibling);
        
        // 缓存元素
        this.elements.modelList = document.getElementById('model-list');
        this.elements.loadAllButton = document.getElementById('load-all-models');
        this.elements.unloadAllButton = document.getElementById('unload-all-models');
        this.elements.loadingIndicator = document.getElementById('model-loading-indicator');
        this.elements.loadingProgress = document.getElementById('model-loading-progress');
        this.elements.loadingText = document.getElementById('model-loading-text');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 项目选择
        this.elements.projectDropdown.addEventListener('change', (e) => {
            this.onProjectSelected(e.target.value);
        });
        
        // 刷新项目
        this.elements.refreshButton.addEventListener('click', () => {
            this.refreshProjects();
        });
        
        // 加载全部模型
        this.elements.loadAllButton.addEventListener('click', () => {
            this.loadAllModels();
        });
        
        // 卸载全部模型
        this.elements.unloadAllButton.addEventListener('click', () => {
            this.unloadAllModels();
        });
        
        // 监听项目管理器事件
        this.projectManager.on('initialized', (data) => {
            this.updateProjectList(data.projects);
        });
        
        this.projectManager.on('projectLoaded', (data) => {
            this.onProjectLoaded(data.project);
        });
        
        // 监听场景管理器事件（安全检查）
        if (this.sceneManager && typeof this.sceneManager.on === 'function') {
            this.sceneManager.on('projectModelsLoaded', (data) => {
                this.onModelsLoaded(data);
            });

            this.sceneManager.on('loadingProgress', (data) => {
                this.updateLoadingProgress(data);
            });
        } else {
            console.warn('⚠️ 场景管理器事件系统未就绪，将延迟设置事件监听器');

            // 延迟设置事件监听器
            setTimeout(() => {
                this.setupSceneEventListeners();
            }, 1000);
        }
    }

    /**
     * 延迟设置场景事件监听器
     */
    setupSceneEventListeners() {
        if (this.sceneManager && typeof this.sceneManager.on === 'function') {
            this.sceneManager.on('projectModelsLoaded', (data) => {
                this.onModelsLoaded(data);
            });

            this.sceneManager.on('loadingProgress', (data) => {
                this.updateLoadingProgress(data);
            });

            console.log('✅ 场景管理器事件监听器设置完成');
        } else {
            console.warn('⚠️ 场景管理器仍未就绪，稍后重试');
            setTimeout(() => {
                this.setupSceneEventListeners();
            }, 2000);
        }
    }

    /**
     * 更新项目列表
     */
    updateProjectList(projects) {
        const dropdown = this.elements.projectDropdown;
        
        // 清空现有选项（保留默认选项）
        dropdown.innerHTML = '<option value="">选择项目...</option>';
        
        // 添加项目选项
        projects.forEach(project => {
            const option = document.createElement('option');
            option.value = project.path;
            option.textContent = project.name;
            option.title = project.description;
            dropdown.appendChild(option);
        });
        
        console.log(`📋 项目列表已更新: ${projects.length} 个项目`);
    }

    /**
     * 项目选择处理
     */
    async onProjectSelected(projectPath) {
        if (!projectPath) {
            this.clearProjectDisplay();
            return;
        }
        
        try {
            console.log(`🎯 选择项目: ${projectPath}`);
            
            // 切换项目
            const project = await this.projectManager.switchProject(projectPath);
            
            // 更新UI显示
            this.updateProjectDisplay(project);
            
        } catch (error) {
            console.error('❌ 项目选择失败:', error);
            this.showError('项目加载失败', error.message);
        }
    }

    /**
     * 项目加载完成处理
     */
    onProjectLoaded(project) {
        this.currentProject = project;
        this.updateProjectDisplay(project);
        this.updateModelList(project.models);
        
        // 启用控制按钮
        this.elements.loadAllButton.disabled = false;
        this.elements.unloadAllButton.disabled = false;
    }

    /**
     * 更新项目显示
     */
    updateProjectDisplay(project) {
        this.elements.projectNameDisplay.textContent = project.name;
        this.elements.projectDescription.textContent = project.description;
        this.elements.modelCount.textContent = project.models.length;
        this.elements.totalSize.textContent = this.calculateTotalSize(project.models);
        
        this.elements.projectInfoDisplay.style.display = 'block';
    }

    /**
     * 更新模型列表
     */
    updateModelList(models) {
        const modelList = this.elements.modelList;
        
        if (models.length === 0) {
            modelList.innerHTML = `
                <div class="no-models-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>该项目没有可用模型</span>
                </div>
            `;
            return;
        }
        
        modelList.innerHTML = '';
        
        models.forEach(model => {
            const modelItem = document.createElement('div');
            modelItem.className = 'model-item';
            modelItem.innerHTML = `
                <div class="model-info">
                    <div class="model-name">${model.name}</div>
                    <div class="model-details">
                        <span class="model-type">${this.getTypeDisplayName(model.type)}</span>
                        <span class="model-size">${model.size}</span>
                    </div>
                    <div class="model-description">${model.description}</div>
                </div>
                <div class="model-actions">
                    <button class="model-action-btn load-btn" data-model="${model.name}" title="加载模型">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="model-action-btn unload-btn" data-model="${model.name}" title="卸载模型" disabled>
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="model-action-btn toggle-btn" data-model="${model.name}" title="显示/隐藏" disabled>
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            `;
            
            modelList.appendChild(modelItem);
        });
        
        // 添加模型操作事件监听
        this.setupModelItemListeners();
    }

    /**
     * 设置模型项事件监听
     */
    setupModelItemListeners() {
        const modelList = this.elements.modelList;
        
        // 加载按钮
        modelList.querySelectorAll('.load-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modelName = e.currentTarget.getAttribute('data-model');
                this.loadSingleModel(modelName);
            });
        });
        
        // 卸载按钮
        modelList.querySelectorAll('.unload-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modelName = e.currentTarget.getAttribute('data-model');
                this.unloadSingleModel(modelName);
            });
        });
        
        // 显示/隐藏按钮
        modelList.querySelectorAll('.toggle-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modelName = e.currentTarget.getAttribute('data-model');
                this.toggleSingleModel(modelName);
            });
        });
    }

    /**
     * 加载单个模型
     */
    async loadSingleModel(modelName) {
        if (!this.currentProject) return;
        
        const modelInfo = this.currentProject.models.find(m => m.name === modelName);
        if (!modelInfo) return;
        
        try {
            console.log(`📦 加载单个模型: ${modelName}`);
            
            const gltf = await this.sceneManager.modelLoader.loadModel(modelInfo.path, {
                name: modelInfo.name,
                onProgress: (percent) => {
                    this.updateSingleModelProgress(modelName, percent);
                }
            });
            
            this.sceneManager.addGLTFModel(modelInfo, gltf);
            this.updateModelItemState(modelName, 'loaded');
            
        } catch (error) {
            console.error(`❌ 单个模型加载失败: ${modelName}`, error);
            this.showError('模型加载失败', `无法加载模型: ${modelName}`);
        }
    }

    /**
     * 更新模型项状态
     */
    updateModelItemState(modelName, state) {
        const modelItem = this.elements.modelList.querySelector(`[data-model="${modelName}"]`);
        if (!modelItem) return;
        
        const loadBtn = modelItem.closest('.model-item').querySelector('.load-btn');
        const unloadBtn = modelItem.closest('.model-item').querySelector('.unload-btn');
        const toggleBtn = modelItem.closest('.model-item').querySelector('.toggle-btn');
        
        switch (state) {
            case 'loaded':
                loadBtn.disabled = true;
                unloadBtn.disabled = false;
                toggleBtn.disabled = false;
                break;
            case 'unloaded':
                loadBtn.disabled = false;
                unloadBtn.disabled = true;
                toggleBtn.disabled = true;
                break;
            case 'loading':
                loadBtn.disabled = true;
                unloadBtn.disabled = true;
                toggleBtn.disabled = true;
                break;
        }
    }

    /**
     * 加载所有模型
     */
    async loadAllModels() {
        if (!this.currentProject) return;
        
        try {
            this.showLoadingIndicator(true);
            await this.sceneManager.loadProjectModels(this.currentProject);
            this.showLoadingIndicator(false);
            
            // 更新所有模型项状态
            this.currentProject.models.forEach(model => {
                this.updateModelItemState(model.name, 'loaded');
            });
            
        } catch (error) {
            this.showLoadingIndicator(false);
            this.showError('批量加载失败', error.message);
        }
    }

    /**
     * 卸载所有模型
     */
    unloadAllModels() {
        if (!this.currentProject) return;
        
        this.sceneManager.clearGLTFModels();
        
        // 更新所有模型项状态
        this.currentProject.models.forEach(model => {
            this.updateModelItemState(model.name, 'unloaded');
        });
        
        console.log('🗑️ 所有模型已卸载');
    }

    /**
     * 显示/隐藏加载指示器
     */
    showLoadingIndicator(show, text = '加载中...') {
        const indicator = this.elements.loadingIndicator;
        const textElement = this.elements.loadingText;
        
        if (show) {
            indicator.style.display = 'block';
            textElement.textContent = text;
        } else {
            indicator.style.display = 'none';
        }
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(data) {
        const progressBar = this.elements.loadingProgress;
        const textElement = this.elements.loadingText;
        
        progressBar.style.width = `${data.progress}%`;
        textElement.textContent = `加载中... ${Math.round(data.progress)}% (${data.currentModel}/${data.totalModels})`;
    }

    /**
     * 刷新项目列表
     */
    async refreshProjects() {
        try {
            console.log('🔄 刷新项目列表...');
            
            // 显示刷新动画
            const refreshBtn = this.elements.refreshButton;
            const icon = refreshBtn.querySelector('i');
            icon.classList.add('fa-spin');
            
            // 重新扫描项目
            await this.projectManager.scanAvailableProjects();
            
            // 更新下拉列表
            this.updateProjectList(this.projectManager.getAvailableProjects());
            
            // 停止动画
            setTimeout(() => {
                icon.classList.remove('fa-spin');
            }, 1000);
            
        } catch (error) {
            console.error('❌ 刷新项目失败:', error);
            this.showError('刷新失败', error.message);
        }
    }

    /**
     * 清空项目显示
     */
    clearProjectDisplay() {
        this.elements.projectInfoDisplay.style.display = 'none';
        this.elements.modelList.innerHTML = `
            <div class="no-models-message">
                <i class="fas fa-info-circle"></i>
                <span>请先选择一个项目</span>
            </div>
        `;
        
        this.elements.loadAllButton.disabled = true;
        this.elements.unloadAllButton.disabled = true;
        
        this.currentProject = null;
    }

    /**
     * 获取类型显示名称
     */
    getTypeDisplayName(type) {
        const typeNames = {
            'bridge': '桥梁',
            'road': '道路',
            'foundation': '基础',
            'equipment': '设备',
            'terrain': '地形',
            'building': '建筑'
        };
        return typeNames[type] || type;
    }

    /**
     * 计算总大小
     */
    calculateTotalSize(models) {
        let totalBytes = 0;
        models.forEach(model => {
            const sizeStr = model.size || '0MB';
            const size = parseFloat(sizeStr.replace('MB', ''));
            totalBytes += size;
        });
        return `${totalBytes.toFixed(1)}MB`;
    }

    /**
     * 显示错误消息
     */
    showError(title, message) {
        // 这里可以集成通知系统
        console.error(`${title}: ${message}`);
        
        // 简单的错误显示
        alert(`${title}\n${message}`);
    }

    /**
     * 模型加载完成处理
     */
    onModelsLoaded(data) {
        console.log(`✅ 项目模型加载完成: ${data.project.name}`);
        this.showLoadingIndicator(false);
    }

    /**
     * 获取当前项目
     */
    getCurrentProject() {
        return this.currentProject;
    }

    /**
     * 更新单个模型进度
     */
    updateSingleModelProgress(modelName, percent) {
        // 可以在这里更新单个模型的加载进度显示
        console.log(`📊 ${modelName} 加载进度: ${percent.toFixed(1)}%`);
    }

    /**
     * 卸载单个模型
     */
    unloadSingleModel(modelName) {
        // 实现单个模型卸载逻辑
        console.log(`🗑️ 卸载模型: ${modelName}`);
        this.updateModelItemState(modelName, 'unloaded');
    }

    /**
     * 切换单个模型显示
     */
    toggleSingleModel(modelName) {
        // 实现单个模型显示切换逻辑
        console.log(`👁️ 切换模型显示: ${modelName}`);
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 清理事件监听器和DOM元素
        console.log('🧹 项目UI资源已清理');
    }
}
