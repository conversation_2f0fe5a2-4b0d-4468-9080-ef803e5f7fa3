/**
 * 性能优化器
 * 提供自动性能监控、内存管理和渲染优化
 */

class PerformanceOptimizer {
    constructor(core) {
        this.core = core;
        
        // 性能监控
        this.metrics = {
            fps: 0,
            frameTime: 0,
            memoryUsage: 0,
            drawCalls: 0,
            triangles: 0,
            lastUpdateTime: 0
        };
        
        // 优化配置
        this.config = {
            targetFPS: 60,
            maxMemoryUsage: 512, // MB
            adaptiveQuality: true,
            enableLOD: true,
            enableFrustumCulling: true,
            enableOcclusion: false,
            maxDrawCalls: 1000,
            autoOptimize: true
        };
        
        // 优化状态
        this.optimizations = {
            shadowQuality: 'high',
            modelLOD: 'high',
            textureQuality: 'high',
            antialiasing: true,
            postProcessing: true
        };
        
        // 性能历史
        this.performanceHistory = [];
        this.maxHistorySize = 300; // 5分钟 @ 1fps
        
        // 优化策略
        this.strategies = new Map();
        this.initializeStrategies();
        
        // 监控间隔
        this.monitoringInterval = null;
        this.isMonitoring = false;
    }

    /**
     * 初始化优化策略
     */
    initializeStrategies() {
        // 低FPS优化策略
        this.strategies.set('lowFPS', [
            () => this.reduceShadowQuality(),
            () => this.enableModelLOD(),
            () => this.reduceTextureQuality(),
            () => this.disablePostProcessing(),
            () => this.disableAntialiasing()
        ]);
        
        // 高内存使用优化策略
        this.strategies.set('highMemory', [
            () => this.clearModelCache(),
            () => this.optimizeTextures(),
            () => this.enableInstancedRendering(),
            () => this.reduceModelDetail()
        ]);
        
        // 高绘制调用优化策略
        this.strategies.set('highDrawCalls', [
            () => this.batchGeometries(),
            () => this.enableInstancing(),
            () => this.mergeStaticObjects(),
            () => this.enableFrustumCulling()
        ]);
    }

    /**
     * 初始化性能监控
     */
    async initialize() {
        console.log('⚡ 初始化性能优化器');
        
        // 从配置加载设置
        await this.loadOptimizationSettings();
        
        // 启动监控
        this.startMonitoring();
        
        // 应用初始优化
        await this.applyInitialOptimizations();
        
        console.log('✅ 性能优化器初始化完成');
    }

    /**
     * 加载优化设置
     */
    async loadOptimizationSettings() {
        const savedConfig = this.core.config.get('performance');
        if (savedConfig) {
            Object.assign(this.config, savedConfig);
            console.log('📖 已加载性能配置');
        }
        
        // 根据设备性能调整配置
        this.adjustConfigForDevice();
    }

    /**
     * 根据设备性能调整配置
     */
    adjustConfigForDevice() {
        const deviceInfo = this.getDeviceInfo();
        
        // 移动设备优化
        if (deviceInfo.isMobile) {
            this.config.targetFPS = 30;
            this.config.maxMemoryUsage = 256;
            this.optimizations.shadowQuality = 'medium';
            this.optimizations.antialiasing = false;
            console.log('📱 移动设备优化配置已应用');
        }
        
        // 低端设备优化
        if (deviceInfo.isLowEnd) {
            this.config.targetFPS = 24;
            this.config.maxMemoryUsage = 128;
            this.optimizations.shadowQuality = 'low';
            this.optimizations.modelLOD = 'low';
            this.optimizations.postProcessing = false;
            console.log('🔧 低端设备优化配置已应用');
        }
    }

    /**
     * 获取设备信息
     */
    getDeviceInfo() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        const deviceInfo = {
            isMobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
            isLowEnd: false,
            maxTextureSize: gl ? gl.getParameter(gl.MAX_TEXTURE_SIZE) : 2048,
            maxRenderbufferSize: gl ? gl.getParameter(gl.MAX_RENDERBUFFER_SIZE) : 2048,
            cores: navigator.hardwareConcurrency || 2,
            memory: navigator.deviceMemory || 4
        };
        
        // 判断低端设备
        deviceInfo.isLowEnd = deviceInfo.memory < 4 || 
                              deviceInfo.cores < 4 || 
                              deviceInfo.maxTextureSize < 4096;
        
        return deviceInfo;
    }

    /**
     * 应用初始优化
     */
    async applyInitialOptimizations() {
        try {
            const sceneService = await this.core.getService('scene');
            const renderer = sceneService.sceneManager.getRenderer();
            
            if (renderer) {
                // 应用渲染器优化
                this.optimizeRenderer(renderer);
                
                // 应用阴影优化
                this.optimizeShadows(renderer);
                
                console.log('🎯 初始优化已应用');
            }
            
        } catch (error) {
            console.warn('⚠️ 应用初始优化失败:', error);
        }
    }

    /**
     * 优化渲染器
     */
    optimizeRenderer(renderer) {
        // 设置像素比
        const pixelRatio = Math.min(window.devicePixelRatio, 2);
        renderer.setPixelRatio(pixelRatio);
        
        // 启用自动清理
        renderer.autoClear = true;
        renderer.autoClearColor = true;
        renderer.autoClearDepth = true;
        renderer.autoClearStencil = false;
        
        // 优化设置
        renderer.sortObjects = true;
        renderer.preserveDrawingBuffer = false;
        
        // 抗锯齿设置
        if (this.optimizations.antialiasing) {
            // 检查是否支持MSAA
            const gl = renderer.getContext();
            const samples = gl.getParameter(gl.SAMPLES);
            console.log(`🔧 渲染器优化: 像素比=${pixelRatio}, MSAA样本=${samples}`);
        }
    }

    /**
     * 优化阴影
     */
    optimizeShadows(renderer) {
        if (!renderer.shadowMap) return;
        
        // 根据质量设置调整阴影
        switch (this.optimizations.shadowQuality) {
            case 'high':
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                renderer.shadowMap.size = 2048;
                break;
            case 'medium':
                renderer.shadowMap.type = THREE.PCFShadowMap;
                renderer.shadowMap.size = 1024;
                break;
            case 'low':
                renderer.shadowMap.type = THREE.BasicShadowMap;
                renderer.shadowMap.size = 512;
                break;
            default:
                renderer.shadowMap.enabled = false;
        }
        
        console.log(`🌓 阴影优化: 质量=${this.optimizations.shadowQuality}`);
    }

    /**
     * 启动性能监控
     */
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.updateMetrics();
            this.analyzePerformance();
            if (this.config.autoOptimize) {
                this.autoOptimize();
            }
        }, 1000);
        
        console.log('📊 性能监控已启动');
    }

    /**
     * 停止性能监控
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.isMonitoring = false;
        console.log('⏹️ 性能监控已停止');
    }

    /**
     * 更新性能指标
     */
    async updateMetrics() {
        try {
            const sceneService = await this.core.getService('scene');
            const renderer = sceneService.sceneManager.getRenderer();
            
            if (renderer) {
                const info = renderer.info;
                
                // 更新渲染指标
                this.metrics.drawCalls = info.render.calls;
                this.metrics.triangles = info.render.triangles;
                
                // 计算FPS
                const now = performance.now();
                if (this.metrics.lastUpdateTime > 0) {
                    this.metrics.frameTime = now - this.metrics.lastUpdateTime;
                    this.metrics.fps = Math.round(1000 / this.metrics.frameTime);
                }
                this.metrics.lastUpdateTime = now;
                
                // 更新内存使用
                if (performance.memory) {
                    this.metrics.memoryUsage = Math.round(
                        performance.memory.usedJSHeapSize / 1024 / 1024
                    );
                }
                
                // 添加到历史记录
                this.addToHistory();
            }
            
        } catch (error) {
            console.warn('⚠️ 更新性能指标失败:', error);
        }
    }

    /**
     * 添加到性能历史
     */
    addToHistory() {
        this.performanceHistory.push({
            timestamp: Date.now(),
            fps: this.metrics.fps,
            frameTime: this.metrics.frameTime,
            memoryUsage: this.metrics.memoryUsage,
            drawCalls: this.metrics.drawCalls,
            triangles: this.metrics.triangles
        });
        
        // 限制历史记录大小
        if (this.performanceHistory.length > this.maxHistorySize) {
            this.performanceHistory.shift();
        }
    }

    /**
     * 分析性能
     */
    analyzePerformance() {
        if (this.performanceHistory.length < 10) return;
        
        const recent = this.performanceHistory.slice(-10);
        const avgFPS = recent.reduce((sum, entry) => sum + entry.fps, 0) / recent.length;
        const avgMemory = recent.reduce((sum, entry) => sum + entry.memoryUsage, 0) / recent.length;
        const avgDrawCalls = recent.reduce((sum, entry) => sum + entry.drawCalls, 0) / recent.length;
        
        // 检测性能问题
        const issues = [];
        
        if (avgFPS < this.config.targetFPS * 0.8) {
            issues.push('lowFPS');
        }
        
        if (avgMemory > this.config.maxMemoryUsage * 0.8) {
            issues.push('highMemory');
        }
        
        if (avgDrawCalls > this.config.maxDrawCalls * 0.8) {
            issues.push('highDrawCalls');
        }
        
        // 触发性能事件
        if (issues.length > 0) {
            this.core.eventBus.emit('performance:issues', {
                issues,
                metrics: {
                    avgFPS,
                    avgMemory,
                    avgDrawCalls
                }
            });
        }
    }

    /**
     * 自动优化
     */
    async autoOptimize() {
        if (this.performanceHistory.length < 5) return;
        
        const recent = this.performanceHistory.slice(-5);
        const avgFPS = recent.reduce((sum, entry) => sum + entry.fps, 0) / recent.length;
        const avgMemory = recent.reduce((sum, entry) => sum + entry.memoryUsage, 0) / recent.length;
        
        let optimized = false;
        
        // FPS过低优化
        if (avgFPS < this.config.targetFPS * 0.7) {
            await this.applyOptimizationStrategy('lowFPS');
            optimized = true;
        }
        
        // 内存过高优化
        if (avgMemory > this.config.maxMemoryUsage * 0.8) {
            await this.applyOptimizationStrategy('highMemory');
            optimized = true;
        }
        
        if (optimized) {
            console.log(`🔧 自动优化已应用: FPS=${avgFPS.toFixed(1)}, 内存=${avgMemory.toFixed(1)}MB`);
        }
    }

    /**
     * 应用优化策略
     */
    async applyOptimizationStrategy(strategyName) {
        const strategies = this.strategies.get(strategyName);
        if (!strategies) return;
        
        for (const strategy of strategies) {
            try {
                await strategy();
                // 等待一段时间观察效果
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // 检查是否有改善
                if (await this.checkPerformanceImprovement()) {
                    console.log(`✅ 优化策略生效: ${strategyName}`);
                    break;
                }
            } catch (error) {
                console.warn(`⚠️ 优化策略失败: ${strategyName}`, error);
            }
        }
    }

    /**
     * 检查性能改善
     */
    async checkPerformanceImprovement() {
        // 简单的改善检测
        const currentFPS = this.metrics.fps;
        const targetFPS = this.config.targetFPS;
        
        return currentFPS > targetFPS * 0.8;
    }

    /**
     * 优化策略实现
     */
    
    async reduceShadowQuality() {
        try {
            const sceneService = await this.core.getService('scene');
            const renderer = sceneService.sceneManager.getRenderer();
            
            if (this.optimizations.shadowQuality === 'high') {
                this.optimizations.shadowQuality = 'medium';
            } else if (this.optimizations.shadowQuality === 'medium') {
                this.optimizations.shadowQuality = 'low';
            } else {
                renderer.shadowMap.enabled = false;
                return;
            }
            
            this.optimizeShadows(renderer);
            console.log('🌓 降低阴影质量');
            
        } catch (error) {
            console.warn('⚠️ 降低阴影质量失败:', error);
        }
    }

    async enableModelLOD() {
        console.log('📐 启用模型LOD');
        // LOD实现需要根据具体模型结构
    }

    async reduceTextureQuality() {
        console.log('🖼️ 降低纹理质量');
        // 纹理质量优化实现
    }

    async disablePostProcessing() {
        this.optimizations.postProcessing = false;
        console.log('🎨 禁用后处理效果');
    }

    async disableAntialiasing() {
        this.optimizations.antialiasing = false;
        console.log('📺 禁用抗锯齿');
    }

    async clearModelCache() {
        try {
            const modelService = await this.core.getService('model');
            // 清理部分缓存而不是全部
            console.log('🧹 清理模型缓存');
        } catch (error) {
            console.warn('⚠️ 清理模型缓存失败:', error);
        }
    }

    async optimizeTextures() {
        console.log('🎯 优化纹理');
        // 纹理压缩和优化
    }

    async enableInstancedRendering() {
        console.log('🔄 启用实例化渲染');
        // 实例化渲染实现
    }

    async reduceModelDetail() {
        console.log('🔧 降低模型细节');
        // 模型细节减少
    }

    async batchGeometries() {
        console.log('📦 批处理几何体');
        // 几何体批处理
    }

    async enableInstancing() {
        console.log('🎯 启用实例化');
        // 实例化启用
    }

    async mergeStaticObjects() {
        console.log('🔗 合并静态对象');
        // 静态对象合并
    }

    async enableFrustumCulling() {
        console.log('👁️ 启用视锥体裁剪');
        // 视锥体裁剪实现
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        if (this.performanceHistory.length === 0) {
            return null;
        }
        
        const recent = this.performanceHistory.slice(-60); // 最近1分钟
        
        const avgFPS = recent.reduce((sum, entry) => sum + entry.fps, 0) / recent.length;
        const minFPS = Math.min(...recent.map(entry => entry.fps));
        const maxFPS = Math.max(...recent.map(entry => entry.fps));
        
        const avgMemory = recent.reduce((sum, entry) => sum + entry.memoryUsage, 0) / recent.length;
        const maxMemory = Math.max(...recent.map(entry => entry.memoryUsage));
        
        const avgDrawCalls = recent.reduce((sum, entry) => sum + entry.drawCalls, 0) / recent.length;
        const maxDrawCalls = Math.max(...recent.map(entry => entry.drawCalls));
        
        return {
            fps: {
                average: Math.round(avgFPS),
                min: minFPS,
                max: maxFPS,
                target: this.config.targetFPS
            },
            memory: {
                average: Math.round(avgMemory),
                max: maxMemory,
                limit: this.config.maxMemoryUsage
            },
            drawCalls: {
                average: Math.round(avgDrawCalls),
                max: maxDrawCalls,
                limit: this.config.maxDrawCalls
            },
            optimizations: { ...this.optimizations },
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        
        if (this.metrics.fps < this.config.targetFPS * 0.8) {
            recommendations.push({
                type: 'performance',
                message: '帧率较低，建议降低渲染质量或减少模型复杂度',
                priority: 'high'
            });
        }
        
        if (this.metrics.memoryUsage > this.config.maxMemoryUsage * 0.8) {
            recommendations.push({
                type: 'memory',
                message: '内存使用过高，建议清理模型缓存或降低纹理质量',
                priority: 'medium'
            });
        }
        
        if (this.metrics.drawCalls > this.config.maxDrawCalls * 0.8) {
            recommendations.push({
                type: 'drawcalls',
                message: '绘制调用过多，建议启用批处理或实例化渲染',
                priority: 'medium'
            });
        }
        
        return recommendations;
    }

    /**
     * 获取当前性能指标
     */
    getCurrentMetrics() {
        return { ...this.metrics };
    }

    /**
     * 设置优化配置
     */
    setOptimizationConfig(config) {
        Object.assign(this.config, config);
        this.core.config.set('performance', this.config);
        console.log('⚙️ 性能优化配置已更新');
    }

    /**
     * 手动触发优化
     */
    async manualOptimize(type = 'all') {
        console.log(`🔧 手动触发优化: ${type}`);
        
        switch (type) {
            case 'fps':
                await this.applyOptimizationStrategy('lowFPS');
                break;
            case 'memory':
                await this.applyOptimizationStrategy('highMemory');
                break;
            case 'drawcalls':
                await this.applyOptimizationStrategy('highDrawCalls');
                break;
            case 'all':
                await this.applyOptimizationStrategy('lowFPS');
                await this.applyOptimizationStrategy('highMemory');
                await this.applyOptimizationStrategy('highDrawCalls');
                break;
        }
        
        console.log('✅ 手动优化完成');
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('🧹 清理性能优化器资源');
        
        this.stopMonitoring();
        this.performanceHistory = [];
        this.strategies.clear();
        
        console.log('✅ 性能优化器资源清理完成');
    }
}

// 导出性能优化器
window.PerformanceOptimizer = PerformanceOptimizer;