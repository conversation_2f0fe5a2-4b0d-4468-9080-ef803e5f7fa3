/**
 * glTF模型加载器
 * 负责加载、优化和管理glTF模型
 */

class GLTFModelLoader {
    constructor() {
        this.loader = null;
        this.dracoLoader = null;
        this.ktx2Loader = null;

        this.loadedModels = new Map();
        this.loadingPromises = new Map();
        this.eventListeners = new Map();

        this.isInitialized = false;
        this.initializationPromise = null;

        // 延迟初始化，等待Three.js扩展加载
        this.delayedInit();
    }

    /**
     * 延迟初始化，等待Three.js扩展加载
     */
    delayedInit() {
        this.initializationPromise = new Promise((resolve, reject) => {
            const checkAndInit = () => {
                if (typeof THREE !== 'undefined' && typeof THREE.GLTFLoader !== 'undefined') {
                    try {
                        this.init();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                } else {
                    // 每100ms检查一次，最多等待10秒
                    setTimeout(checkAndInit, 100);
                }
            };

            // 开始检查
            checkAndInit();

            // 10秒超时
            setTimeout(() => {
                if (!this.isInitialized) {
                    reject(new Error('GLTFLoader加载超时'));
                }
            }, 10000);
        });
    }

    /**
     * 初始化加载器
     */
    init() {
        console.log('📦 初始化glTF模型加载器...');

        try {
            // 检查GLTFLoader是否可用
            if (typeof THREE.GLTFLoader === 'undefined') {
                throw new Error('THREE.GLTFLoader未定义，请确保GLTFLoader.js已加载');
            }

            // 初始化GLTFLoader
            this.loader = new THREE.GLTFLoader();

            // 初始化Draco解压缩器
            if (typeof THREE.DRACOLoader !== 'undefined') {
                this.dracoLoader = new THREE.DRACOLoader();
                this.dracoLoader.setDecoderPath('https://unpkg.com/three@0.158.0/examples/js/libs/draco/');
                this.loader.setDRACOLoader(this.dracoLoader);
                console.log('✅ Draco解压缩器已启用');
            } else {
                console.warn('⚠️ DRACOLoader未加载，Draco压缩模型可能无法正常加载');
            }

            // 初始化KTX2纹理加载器
            if (typeof THREE.KTX2Loader !== 'undefined') {
                this.ktx2Loader = new THREE.KTX2Loader();
                this.ktx2Loader.setTranscoderPath('https://unpkg.com/three@0.158.0/examples/js/libs/basis/');
                this.loader.setKTX2Loader(this.ktx2Loader);
                console.log('✅ KTX2纹理加载器已启用');
            } else {
                console.warn('⚠️ KTX2Loader未加载，KTX2纹理可能无法正常加载');
            }

            this.isInitialized = true;
            console.log('✅ glTF模型加载器初始化完成');

        } catch (error) {
            console.error('❌ 模型加载器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 确保加载器已初始化
     */
    async ensureInitialized() {
        if (this.isInitialized) {
            return;
        }

        if (this.initializationPromise) {
            await this.initializationPromise;
        } else {
            throw new Error('模型加载器未正确初始化');
        }
    }

    /**
     * 加载glTF模型
     */
    async loadModel(modelPath, options = {}) {
        // 确保加载器已初始化
        await this.ensureInitialized();

        const {
            onProgress = null,
            useCache = true,
            optimize = true,
            name = null
        } = options;

        // 检查缓存
        if (useCache && this.loadedModels.has(modelPath)) {
            console.log(`📋 从缓存加载模型: ${modelPath}`);
            const cachedModel = this.loadedModels.get(modelPath);
            return this.cloneModel(cachedModel);
        }

        // 检查是否正在加载
        if (this.loadingPromises.has(modelPath)) {
            console.log(`⏳ 等待模型加载完成: ${modelPath}`);
            return await this.loadingPromises.get(modelPath);
        }

        // 创建加载Promise
        const loadingPromise = this.performLoad(modelPath, onProgress, optimize, name);
        this.loadingPromises.set(modelPath, loadingPromise);

        try {
            const result = await loadingPromise;

            // 缓存结果
            if (useCache) {
                this.loadedModels.set(modelPath, result.scene);
            }

            // 清理加载Promise
            this.loadingPromises.delete(modelPath);

            return result;

        } catch (error) {
            this.loadingPromises.delete(modelPath);
            throw error;
        }
    }

    /**
     * 执行实际加载
     */
    async performLoad(modelPath, onProgress, optimize, name) {
        return new Promise((resolve, reject) => {
            console.log(`📥 开始加载模型: ${modelPath}`);
            
            this.loader.load(
                modelPath,
                (gltf) => {
                    console.log(`✅ 模型加载成功: ${modelPath}`);
                    
                    // 设置模型名称
                    if (name) {
                        gltf.scene.name = name;
                    }
                    
                    // 优化模型
                    if (optimize) {
                        this.optimizeModel(gltf);
                    }
                    
                    // 触发加载完成事件
                    this.emit('modelLoaded', {
                        path: modelPath,
                        model: gltf.scene,
                        animations: gltf.animations
                    });
                    
                    resolve(gltf);
                },
                (progress) => {
                    if (onProgress) {
                        const percent = (progress.loaded / progress.total) * 100;
                        onProgress(percent, progress);
                    }
                    
                    this.emit('loadProgress', {
                        path: modelPath,
                        loaded: progress.loaded,
                        total: progress.total,
                        percent: (progress.loaded / progress.total) * 100
                    });
                },
                (error) => {
                    console.error(`❌ 模型加载失败: ${modelPath}`, error);
                    this.emit('loadError', { path: modelPath, error });
                    reject(error);
                }
            );
        });
    }

    /**
     * 优化模型
     */
    optimizeModel(gltf) {
        console.log('🔧 优化模型...');
        
        gltf.scene.traverse((child) => {
            if (child.isMesh) {
                // 启用阴影
                child.castShadow = true;
                child.receiveShadow = true;
                
                // 优化材质
                if (child.material) {
                    // 确保材质支持环境贴图
                    if (child.material.isMeshStandardMaterial) {
                        child.material.envMapIntensity = 1.0;
                    }
                    
                    // 优化透明材质
                    if (child.material.transparent) {
                        child.material.alphaTest = 0.1;
                    }
                }
                
                // 优化几何体
                if (child.geometry) {
                    child.geometry.computeBoundingBox();
                    child.geometry.computeBoundingSphere();
                }
            }
        });
        
        console.log('✅ 模型优化完成');
    }

    /**
     * 克隆模型
     */
    cloneModel(model) {
        const cloned = model.clone();
        
        // 深度克隆材质
        cloned.traverse((child) => {
            if (child.isMesh && child.material) {
                child.material = child.material.clone();
            }
        });
        
        return { scene: cloned, animations: [] };
    }

    /**
     * 卸载模型
     */
    unloadModel(modelPath) {
        if (this.loadedModels.has(modelPath)) {
            const model = this.loadedModels.get(modelPath);
            
            // 清理几何体和材质
            model.traverse((child) => {
                if (child.isMesh) {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(mat => mat.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            });
            
            this.loadedModels.delete(modelPath);
            console.log(`🗑️ 模型已卸载: ${modelPath}`);
            
            this.emit('modelUnloaded', { path: modelPath });
        }
    }

    /**
     * 获取加载统计
     */
    getLoadingStats() {
        return {
            loadedCount: this.loadedModels.size,
            loadingCount: this.loadingPromises.size,
            cacheSize: this.calculateCacheSize()
        };
    }

    /**
     * 计算缓存大小
     */
    calculateCacheSize() {
        let totalSize = 0;
        this.loadedModels.forEach((model) => {
            model.traverse((child) => {
                if (child.isMesh && child.geometry) {
                    totalSize += child.geometry.attributes.position.count * 12; // 简化计算
                }
            });
        });
        return `${(totalSize / 1024 / 1024).toFixed(2)}MB`;
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 卸载所有模型
        const modelPaths = Array.from(this.loadedModels.keys());
        modelPaths.forEach(path => this.unloadModel(path));
        
        // 清理加载器
        if (this.dracoLoader) {
            this.dracoLoader.dispose();
        }
        if (this.ktx2Loader) {
            this.ktx2Loader.dispose();
        }
        
        this.eventListeners.clear();
        console.log('🧹 模型加载器资源已清理');
    }
}
