{"name": "示例大桥建设项目", "version": "1.0.0", "description": "一个完整的桥梁建设演示项目，展示从基础施工到完工的全过程", "type": "bridge", "location": {"name": "某某大桥", "coordinates": {"latitude": 39.9042, "longitude": 116.4074}, "elevation": 50}, "specifications": {"totalLength": 1200, "bridgeWidth": 32, "mainSpanLength": 400, "pierCount": 8, "designLoad": "公路-I级", "bridgeType": "斜拉桥"}, "camera": {"position": [-800, 200, 800], "target": [0, 50, 0], "fov": 60, "near": 1, "far": 10000}, "lighting": {"intensity": 1.0, "shadows": true, "ambientIntensity": 0.3, "directionalIntensity": 1.0, "sunPosition": [1000, 1000, 500]}, "environment": {"skyColor": "#87<PERSON><PERSON>", "fogEnabled": true, "fogDensity": 0.002, "fogColor": "#87<PERSON><PERSON>"}, "phases": [{"id": "preparation", "name": "前期准备", "description": "场地清理、临时设施搭建", "startDate": "2024-01-01", "endDate": "2024-01-31", "progress": 100, "color": "#95a5a6", "models": ["site-preparation.gltf"], "tasks": ["场地勘测", "临时道路建设", "施工围挡设置", "临时设施搭建"]}, {"id": "foundation", "name": "基础施工", "description": "桥墩基础、承台施工", "startDate": "2024-02-01", "endDate": "2024-04-30", "progress": 85, "color": "#e74c3c", "models": ["foundation.gltf", "piers-base.gltf"], "tasks": ["基坑开挖", "钢筋笼制作", "混凝土浇筑", "承台施工"]}, {"id": "substructure", "name": "下部结构", "description": "桥墩、桥台施工", "startDate": "2024-03-15", "endDate": "2024-06-15", "progress": 70, "color": "#f39c12", "models": ["piers.gltf", "abutments.gltf"], "tasks": ["桥墩施工", "桥台施工", "支座安装", "防撞护栏预埋"]}, {"id": "superstructure", "name": "上部结构", "description": "主梁、桥面系统施工", "startDate": "2024-05-01", "endDate": "2024-08-31", "progress": 45, "color": "#3498db", "models": ["main-beams.gltf", "bridge-deck.gltf"], "tasks": ["主梁预制", "主梁安装", "桥面板铺设", "现浇层施工"]}, {"id": "cables", "name": "缆索系统", "description": "斜拉索安装与张拉", "startDate": "2024-07-01", "endDate": "2024-09-30", "progress": 20, "color": "#9b59b6", "models": ["cables.gltf", "towers.gltf"], "tasks": ["索塔施工", "斜拉索安装", "索力张拉", "索力调整"]}, {"id": "finishing", "name": "装饰装修", "description": "桥面铺装、护栏、照明等", "startDate": "2024-08-15", "endDate": "2024-11-30", "progress": 10, "color": "#27ae60", "models": ["road-surface.gltf", "guardrails.gltf", "lighting.gltf"], "tasks": ["桥面铺装", "护栏安装", "照明系统", "标志标线"]}, {"id": "completion", "name": "竣工验收", "description": "工程验收、交付使用", "startDate": "2024-11-01", "endDate": "2024-12-31", "progress": 0, "color": "#1abc9c", "models": ["complete-bridge.gltf"], "tasks": ["工程验收", "安全检测", "试运行", "正式通车"]}], "equipment": [{"name": "塔式起重机", "model": "tower-crane.gltf", "position": [-100, 0, 50], "rotation": [0, 0, 0], "scale": [1, 1, 1], "phases": ["substructure", "superstructure"]}, {"name": "混凝土泵车", "model": "concrete-pump.gltf", "position": [200, 0, -100], "rotation": [0, 45, 0], "scale": [1, 1, 1], "phases": ["foundation", "substructure"]}, {"name": "架桥机", "model": "bridge-girder-erector.gltf", "position": [0, 60, 0], "rotation": [0, 0, 0], "scale": [1, 1, 1], "phases": ["superstructure"]}], "annotations": [{"id": "main-span", "type": "dimension", "position": [0, 100, 0], "text": "主跨 400m", "visible": true}, {"id": "pier-height", "type": "dimension", "position": [-200, 30, 0], "text": "桥墩高度 60m", "visible": true}], "viewpoints": [{"name": "总览视图", "position": [-800, 200, 800], "target": [0, 50, 0], "description": "项目整体鸟瞰视角"}, {"name": "桥梁正视图", "position": [0, 100, 1000], "target": [0, 50, 0], "description": "桥梁正面视角"}, {"name": "施工现场", "position": [-200, 50, 200], "target": [-200, 30, 0], "description": "施工现场近景视角"}], "settings": {"autoSave": true, "autoSaveInterval": 300000, "renderQuality": "high", "shadowQuality": "medium", "enablePostProcessing": true, "enablePhysics": false}, "metadata": {"createdDate": "2024-01-01T00:00:00Z", "lastModified": "2024-01-01T00:00:00Z", "version": "1.0.0", "author": "路桥演示系统", "tags": ["桥梁", "施工", "演示", "数字孪生"]}}