<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全启动 - 增强版路桥演示系统</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/ui.css">
    <link rel="stylesheet" href="styles/enhanced-ui.css">
    
    <!-- 外部字体和图标 -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- 主容器 -->
    <div id="container">
        <!-- 3D渲染画布 -->
        <canvas id="three-canvas"></canvas>
        
        <!-- 顶部导航栏 -->
        <header class="top-nav">
            <div class="nav-left">
                <h1><i class="fas fa-shield-alt"></i> 安全启动 - 增强版路桥演示系统</h1>
            </div>
            <div class="nav-center">
                <div class="project-info">
                    <span class="project-name" id="current-project-name">系统启动中...</span>
                    <span class="project-status" id="current-project-status">初始化</span>
                </div>
            </div>
            <div class="nav-right">
                <button class="nav-btn" id="status-btn" title="系统状态">
                    <i class="fas fa-heartbeat"></i>
                </button>
                <button class="nav-btn" id="fullscreen-btn" title="全屏">
                    <i class="fas fa-expand"></i>
                </button>
            </div>
        </header>

        <!-- 左侧控制面板 -->
        <aside class="left-panel">
            <div class="panel-section">
                <h3><i class="fas fa-folder-open"></i> 项目选择</h3>
                <div class="project-controls">
                    <select id="project-dropdown" class="project-select">
                        <option value="">等待系统就绪...</option>
                    </select>
                    <button id="refresh-projects" class="refresh-btn" title="刷新项目列表" disabled>
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="project-info-display" id="project-info-display" style="display: none;">
                    <div class="project-detail">
                        <span class="project-name-display" id="project-name-display"></span>
                        <span class="project-description" id="project-description"></span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-eye"></i> 视图控制</h3>
                <div class="view-controls">
                    <button class="view-btn active" data-view="overview">
                        <i class="fas fa-globe"></i>
                        <span>总览视图</span>
                    </button>
                    <button class="view-btn" data-view="bridge">
                        <i class="fas fa-bridge"></i>
                        <span>桥梁视图</span>
                    </button>
                    <button class="view-btn" data-view="construction">
                        <i class="fas fa-hard-hat"></i>
                        <span>施工视图</span>
                    </button>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-layer-group"></i> 图层管理</h3>
                <div class="layer-controls">
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="bridge">
                        <span class="checkmark"></span>
                        <span>桥梁结构</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" checked data-layer="road">
                        <span class="checkmark"></span>
                        <span>道路系统</span>
                    </label>
                    <label class="layer-item">
                        <input type="checkbox" data-layer="equipment">
                        <span class="checkmark"></span>
                        <span>施工设备</span>
                    </label>
                </div>
            </div>
        </aside>

        <!-- 右侧信息面板 -->
        <aside class="right-panel">
            <div class="panel-section">
                <h3><i class="fas fa-sliders-h"></i> 进度控制</h3>
                <div class="progress-controls">
                    <div class="overall-progress">
                        <div class="progress-header">
                            <span class="progress-label">总体进度</span>
                            <span class="progress-value" id="overall-progress-value">0%</span>
                        </div>
                        <div class="progress-bar-large">
                            <div class="progress-fill-large" id="overall-progress-fill"></div>
                        </div>
                    </div>
                    
                    <div class="timeline-container">
                        <label class="timeline-label">时间轴控制</label>
                        <div class="timeline-wrapper">
                            <input type="range" id="progress-timeline" class="timeline-slider" 
                                   min="0" max="100" value="0" step="1" disabled>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-chart-line"></i> 施工进度</h3>
                <div class="progress-list">
                    <div class="progress-item">
                        <span class="task-name">基础施工</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0%</span>
                    </div>
                    <div class="progress-item">
                        <span class="task-name">上部结构</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0%</span>
                    </div>
                    <div class="progress-item">
                        <span class="task-name">装饰装修</span>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                        <span class="progress-text">0%</span>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <h3><i class="fas fa-tools"></i> 快速操作</h3>
                <div class="quick-actions">
                    <button class="action-btn" id="screenshot-btn" disabled>
                        <i class="fas fa-camera"></i>
                        <span>截图</span>
                    </button>
                    <button class="action-btn" id="measure-btn" disabled>
                        <i class="fas fa-ruler"></i>
                        <span>测量</span>
                    </button>
                    <button class="action-btn" id="export-btn" disabled>
                        <i class="fas fa-download"></i>
                        <span>导出</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- 底部状态栏 -->
        <footer class="bottom-status">
            <div class="status-left">
                <span class="status-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>安全模式</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-folder"></i>
                    <span id="current-project-display">项目: 未选择</span>
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">
                    <i class="fas fa-tachometer-alt"></i>
                    <span id="fps-counter">FPS: --</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-cube"></i>
                    <span id="object-count">对象: 0</span>
                </span>
            </div>
        </footer>

        <!-- 加载界面 -->
        <div id="loading-screen">
            <div class="loading-content">
                <div class="loading-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2>安全启动模式</h2>
                <div class="loading-bar">
                    <div class="loading-progress"></div>
                </div>
                <p class="loading-text">正在安全初始化系统...</p>
            </div>
        </div>
    </div>

    <!-- Three.js 主库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/three.js/0.158.0/three.min.js"></script>
    
    <!-- Three.js 扩展 -->
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/loaders/DRACOLoader.js"></script>
    
    <!-- 原有系统模块 -->
    <script src="js/materials.js"></script>
    <script src="js/geometry.js"></script>
    <script src="js/scene.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/ui.js"></script>
    
    <!-- 增强版系统模块 -->
    <script src="js/managers/project-manager.js"></script>
    <script src="js/managers/model-loader.js"></script>
    <script src="js/managers/progress-manager.js"></script>
    <script src="js/managers/enhanced-scene.js"></script>
    <script src="js/managers/tool-manager.js"></script>
    <script src="js/ui/project-ui.js"></script>
    <script src="js/ui/progress-ui.js"></script>
    <script src="js/tools/measurement-tool.js"></script>
    
    <!-- 安全启动脚本 -->
    <script src="js/safe-startup.js"></script>
    
    <script>
        // 页面加载完成后的处理
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM加载完成');
            
            // 状态按钮事件
            document.getElementById('status-btn').addEventListener('click', () => {
                if (safeStartup) {
                    console.log('📊 系统状态:', safeStartup.getSystemStatus());
                }
            });
            
            // 全屏按钮事件
            document.getElementById('fullscreen-btn').addEventListener('click', () => {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            });
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('🚨 全局错误:', event.error);
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            console.error('🚨 未处理的Promise拒绝:', event.reason);
        });
    </script>
</body>
</html>
