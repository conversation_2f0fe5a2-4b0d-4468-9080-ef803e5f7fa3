/**
 * 工具服务
 * 管理测量工具、标注工具、截图导出等功能
 */

class ToolService {
    constructor(core) {
        this.core = core;
        this.eventBus = core.getEventBus();
        
        // 工具管理器
        this.toolManager = null;
        
        // 服务状态
        this.isInitialized = false;
        this.tools = new Map();
        this.activeTool = null;
        
        console.log('🛠️ 工具服务已创建');
    }

    /**
     * 初始化工具服务
     */
    async initialize() {
        try {
            console.log('🛠️ 初始化工具服务...');
            
            // 等待场景服务初始化
            const sceneService = this.core.getService('scene');
            if (!sceneService || !sceneService.isInitialized) {
                console.log('⏳ 等待场景服务初始化...');
                await new Promise(resolve => {
                    const checkScene = () => {
                        const scene = this.core.getService('scene');
                        if (scene && scene.isInitialized) {
                            resolve();
                        } else {
                            setTimeout(checkScene, 100);
                        }
                    };
                    checkScene();
                });
            }
            
            // 创建工具管理器
            await this.createToolManager();
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 工具服务初始化完成');
            
            this.eventBus.emit('toolService:initialized', {
                tools: Array.from(this.tools.keys()),
                activeTool: this.activeTool
            });
            
        } catch (error) {
            console.error('❌ 工具服务初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建工具管理器
     */
    async createToolManager() {
        const sceneService = this.core.getService('scene');
        const scene = sceneService.getScene();
        const camera = sceneService.getCamera();
        const renderer = sceneService.getRenderer();
        
        if (!scene || !camera || !renderer) {
            throw new Error('场景组件不完整，无法创建工具管理器');
        }
        
        // 检查ToolManager是否可用
        if (typeof ToolManager !== 'undefined') {
            this.toolManager = new ToolManager(scene, camera, renderer);
            console.log('✅ 标准工具管理器已创建');
        } else {
            // 创建简化版工具管理器
            this.toolManager = this.createSimplifiedToolManager(scene, camera, renderer);
            console.log('✅ 简化版工具管理器已创建');
        }
        
        // 注册工具到服务
        this.registerTools();
    }

    /**
     * 创建简化版工具管理器
     */
    createSimplifiedToolManager(scene, camera, renderer) {
        return {
            tools: {},
            activeTool: null,
            
            // 测量工具
            createMeasurementTool() {
                return {
                    isActive: false,
                    measurements: [],
                    
                    activate: (mode = 'distance') => {
                        console.log(`📏 测量工具已激活，模式: ${mode}`);
                        this.isActive = true;
                        return true;
                    },
                    
                    deactivate: () => {
                        console.log('📏 测量工具已停用');
                        this.isActive = false;
                    },
                    
                    getMeasurements: () => this.measurements,
                    
                    clearAllMeasurements: () => {
                        this.measurements = [];
                        console.log('🧹 所有测量已清理');
                    }
                };
            },
            
            // 截图工具
            createScreenshotTool() {
                return {
                    takeScreenshot: (options = {}) => {
                        try {
                            renderer.render(scene, camera);
                            const canvas = renderer.domElement;
                            const dataURL = canvas.toDataURL('image/png');
                            
                            const link = document.createElement('a');
                            link.download = `screenshot_${Date.now()}.png`;
                            link.href = dataURL;
                            link.click();
                            
                            console.log('📸 截图已保存');
                            return dataURL;
                        } catch (error) {
                            console.error('❌ 截图失败:', error);
                            throw error;
                        }
                    }
                };
            },
            
            // 标注工具
            createAnnotationTool() {
                return {
                    isActive: false,
                    annotations: [],
                    
                    activate: () => {
                        console.log('📝 标注工具已激活');
                        this.isActive = true;
                    },
                    
                    deactivate: () => {
                        console.log('📝 标注工具已停用');
                        this.isActive = false;
                    },
                    
                    addAnnotation: (position, text, type = 'info') => {
                        const annotation = {
                            id: `annotation_${Date.now()}`,
                            position: position,
                            text: text,
                            type: type,
                            timestamp: Date.now()
                        };
                        this.annotations.push(annotation);
                        return annotation;
                    },
                    
                    getAnnotations: () => this.annotations
                };
            },
            
            activateTool: (toolName, ...args) => {
                if (this.tools[toolName] && this.tools[toolName].activate) {
                    if (this.activeTool && this.activeTool !== toolName) {
                        this.deactivateTool(this.activeTool);
                    }
                    this.tools[toolName].activate(...args);
                    this.activeTool = toolName;
                    return true;
                }
                return false;
            },
            
            deactivateTool: (toolName) => {
                if (this.tools[toolName] && this.tools[toolName].deactivate) {
                    this.tools[toolName].deactivate();
                    if (this.activeTool === toolName) {
                        this.activeTool = null;
                    }
                    return true;
                }
                return false;
            },
            
            getTool: (toolName) => this.tools[toolName],
            
            getActiveTool: () => this.activeTool,
            
            cleanup: () => {
                Object.values(this.tools).forEach(tool => {
                    if (tool.cleanup) tool.cleanup();
                });
                this.tools = {};
                this.activeTool = null;
            }
        };
    }

    /**
     * 注册工具
     */
    registerTools() {
        // 创建和注册各种工具
        if (this.toolManager.tools) {
            // 标准工具管理器
            this.tools.set('measurement', this.toolManager.tools.measurement);
            this.tools.set('screenshot', this.toolManager.tools.screenshot);
            this.tools.set('annotation', this.toolManager.tools.annotation);
            this.tools.set('export', this.toolManager.tools.export);
            this.tools.set('viewpoint', this.toolManager.tools.viewpoint);
        } else {
            // 简化版工具管理器
            this.toolManager.tools.measurement = this.toolManager.createMeasurementTool();
            this.toolManager.tools.screenshot = this.toolManager.createScreenshotTool();
            this.toolManager.tools.annotation = this.toolManager.createAnnotationTool();
            
            this.tools.set('measurement', this.toolManager.tools.measurement);
            this.tools.set('screenshot', this.toolManager.tools.screenshot);
            this.tools.set('annotation', this.toolManager.tools.annotation);
        }
        
        console.log(`🔧 已注册 ${this.tools.size} 个工具`);
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听场景变化
        this.eventBus.on('sceneService:initialized', () => {
            console.log('📡 工具服务收到场景初始化事件');
        });
        
        // 监听项目切换
        this.eventBus.on('projectService:projectLoaded', () => {
            console.log('📡 工具服务收到项目加载事件');
            // 可以在这里重置工具状态
        });
    }

    /**
     * 激活工具
     */
    activateTool(toolName, ...args) {
        if (!this.isInitialized) {
            console.warn('⚠️ 工具服务未初始化');
            return false;
        }
        
        try {
            const success = this.toolManager.activateTool(toolName, ...args);
            if (success) {
                this.activeTool = toolName;
                this.eventBus.emit('toolService:toolActivated', { 
                    toolName, 
                    args 
                });
                console.log(`🔧 工具已激活: ${toolName}`);
            }
            return success;
        } catch (error) {
            console.error(`❌ 激活工具失败: ${toolName}`, error);
            return false;
        }
    }

    /**
     * 停用工具
     */
    deactivateTool(toolName) {
        if (!this.isInitialized) {
            console.warn('⚠️ 工具服务未初始化');
            return false;
        }
        
        try {
            const success = this.toolManager.deactivateTool(toolName);
            if (success && this.activeTool === toolName) {
                this.activeTool = null;
                this.eventBus.emit('toolService:toolDeactivated', { 
                    toolName 
                });
                console.log(`🔧 工具已停用: ${toolName}`);
            }
            return success;
        } catch (error) {
            console.error(`❌ 停用工具失败: ${toolName}`, error);
            return false;
        }
    }

    /**
     * 获取工具
     */
    getTool(toolName) {
        return this.tools.get(toolName);
    }

    /**
     * 获取活动工具
     */
    getActiveTool() {
        return this.activeTool;
    }

    /**
     * 获取工具管理器
     */
    getToolManager() {
        return this.toolManager;
    }

    /**
     * 截图
     */
    takeScreenshot(options = {}) {
        try {
            const screenshotTool = this.getTool('screenshot');
            if (screenshotTool && screenshotTool.takeScreenshot) {
                return screenshotTool.takeScreenshot(options);
            } else {
                throw new Error('截图工具不可用');
            }
        } catch (error) {
            console.error('❌ 截图操作失败:', error);
            throw error;
        }
    }

    /**
     * 开始测量
     */
    startMeasurement(mode = 'distance') {
        return this.activateTool('measurement', mode);
    }

    /**
     * 停止测量
     */
    stopMeasurement() {
        return this.deactivateTool('measurement');
    }

    /**
     * 获取测量结果
     */
    getMeasurements() {
        const measurementTool = this.getTool('measurement');
        if (measurementTool && measurementTool.getMeasurements) {
            return measurementTool.getMeasurements();
        }
        return [];
    }

    /**
     * 清理所有测量
     */
    clearAllMeasurements() {
        const measurementTool = this.getTool('measurement');
        if (measurementTool && measurementTool.clearAllMeasurements) {
            measurementTool.clearAllMeasurements();
            this.eventBus.emit('toolService:measurementsCleared');
            console.log('🧹 所有测量已清理');
        }
    }

    /**
     * 添加标注
     */
    addAnnotation(position, text, type = 'info') {
        const annotationTool = this.getTool('annotation');
        if (annotationTool && annotationTool.addAnnotation) {
            const annotation = annotationTool.addAnnotation(position, text, type);
            this.eventBus.emit('toolService:annotationAdded', annotation);
            return annotation;
        }
        return null;
    }

    /**
     * 获取标注列表
     */
    getAnnotations() {
        const annotationTool = this.getTool('annotation');
        if (annotationTool && annotationTool.getAnnotations) {
            return annotationTool.getAnnotations();
        }
        return [];
    }

    /**
     * 获取工具状态
     */
    getToolStates() {
        const states = {};
        this.tools.forEach((tool, name) => {
            states[name] = {
                isActive: tool.isActive || false,
                isAvailable: true
            };
        });
        return states;
    }

    /**
     * 清理资源
     */
    cleanup() {
        console.log('🧹 清理工具服务...');
        
        if (this.toolManager && this.toolManager.cleanup) {
            this.toolManager.cleanup();
        }
        
        this.tools.clear();
        this.activeTool = null;
        this.isInitialized = false;
        
        console.log('✅ 工具服务资源已清理');
    }
}