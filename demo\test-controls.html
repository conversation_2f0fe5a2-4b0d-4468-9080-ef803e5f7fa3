<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制器测试</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #2c3e50; color: white; }
        #scene-container { width: 800px; height: 600px; border: 2px solid #3498db; margin: 20px 0; position: relative; }
        #three-canvas { width: 100%; height: 100%; display: block; background: #1a252f; }
        .log { background: #34495e; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 14px; max-height: 300px; overflow-y: auto; }
        .controls { margin: 20px 0; }
        button { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        .info { background: #2980b9; color: white; padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🎮 Three.js 控制器测试</h1>
    
    <div class="info">
        <strong>测试说明:</strong> 此页面测试 OrbitControls 是否能正常工作。如果成功，你应该能够：
        <ul>
            <li><strong>左键拖拽</strong>：旋转视角</li>
            <li><strong>右键拖拽</strong>：平移视角</li>
            <li><strong>滚轮</strong>：缩放</li>
        </ul>
    </div>
    
    <div class="controls">
        <button onclick="testSceneCreation()">创建场景</button>
        <button onclick="testControlsCreation()">创建控制器</button>
        <button onclick="resetCamera()">重置相机</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="scene-container">
        <canvas id="three-canvas"></canvas>
    </div>
    
    <div id="log" class="log">
        <div>准备开始控制器测试...</div>
    </div>

    <!-- Three.js -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>

    <script>
        let scene, camera, renderer, controls;
        
        function log(message, type = 'info') {
            const container = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #95a5a6;">[${timestamp}]</span> ${message}`;
            
            switch(type) {
                case 'error': logEntry.style.color = '#e74c3c'; break;
                case 'success': logEntry.style.color = '#2ecc71'; break;
                case 'warning': logEntry.style.color = '#f39c12'; break;
                default: logEntry.style.color = '#ecf0f1';
            }
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '<div>日志已清空...</div>';
        }
        
        function testSceneCreation() {
            log('🎨 开始创建Three.js场景', 'info');
            
            try {
                // 获取canvas
                const canvas = document.getElementById('three-canvas');
                if (!canvas) {
                    throw new Error('Canvas元素未找到');
                }
                
                // 创建渲染器
                renderer = new THREE.WebGLRenderer({
                    canvas: canvas,
                    antialias: true,
                    alpha: false
                });
                
                const rect = canvas.getBoundingClientRect();
                renderer.setSize(rect.width, rect.height);
                renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                renderer.setClearColor(0x1a252f);
                
                log(`✅ 渲染器创建成功: ${rect.width}x${rect.height}`, 'success');
                
                // 创建场景
                scene = new THREE.Scene();
                log('✅ 场景创建成功', 'success');
                
                // 创建相机
                camera = new THREE.PerspectiveCamera(75, rect.width / rect.height, 0.1, 1000);
                camera.position.set(50, 50, 50);
                log('✅ 相机创建成功', 'success');
                
                // 添加一些测试对象
                addTestObjects();
                
                // 添加光源
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(50, 50, 25);
                scene.add(directionalLight);
                
                log('✅ 光源添加成功', 'success');
                
                // 开始渲染循环
                animate();
                
            } catch (error) {
                log(`❌ 场景创建失败: ${error.message}`, 'error');
            }
        }
        
        function addTestObjects() {
            // 添加一个立方体
            const cubeGeometry = new THREE.BoxGeometry(10, 10, 10);
            const cubeMaterial = new THREE.MeshLambertMaterial({ color: 0x3498db });
            const cube = new THREE.Mesh(cubeGeometry, cubeMaterial);
            cube.position.set(0, 0, 0);
            scene.add(cube);
            
            // 添加一个球体
            const sphereGeometry = new THREE.SphereGeometry(8, 32, 32);
            const sphereMaterial = new THREE.MeshLambertMaterial({ color: 0xe74c3c });
            const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
            sphere.position.set(25, 0, 0);
            scene.add(sphere);
            
            // 添加一个圆环
            const torusGeometry = new THREE.TorusGeometry(10, 3, 16, 100);
            const torusMaterial = new THREE.MeshLambertMaterial({ color: 0x2ecc71 });
            const torus = new THREE.Mesh(torusGeometry, torusMaterial);
            torus.position.set(-25, 0, 0);
            scene.add(torus);
            
            // 添加一个网格地面
            const gridHelper = new THREE.GridHelper(100, 10, 0x444444, 0x444444);
            scene.add(gridHelper);
            
            log('✅ 测试对象添加成功（立方体、球体、圆环、网格）', 'success');
        }
        
        function testControlsCreation() {
            log('🎮 开始创建OrbitControls', 'info');
            
            try {
                if (!renderer || !camera) {
                    throw new Error('请先创建场景');
                }
                
                if (typeof THREE.OrbitControls === 'undefined') {
                    throw new Error('OrbitControls未加载');
                }
                
                // 创建控制器
                controls = new THREE.OrbitControls(camera, renderer.domElement);
                
                // 配置控制器
                controls.enableDamping = true;
                controls.dampingFactor = 0.05;
                controls.screenSpacePanning = false;
                controls.minDistance = 10;
                controls.maxDistance = 200;
                controls.maxPolarAngle = Math.PI * 0.8;
                controls.minPolarAngle = Math.PI * 0.1;
                
                // 设置目标点
                controls.target.set(0, 0, 0);
                controls.update();
                
                log('✅ OrbitControls创建成功', 'success');
                log('🎯 现在可以尝试拖拽鼠标控制视角', 'info');
                log('   - 左键拖拽：旋转', 'info');
                log('   - 右键拖拽：平移', 'info');
                log('   - 滚轮：缩放', 'info');
                
                // 测试控制器功能
                testControlsFunctionality();
                
            } catch (error) {
                log(`❌ 控制器创建失败: ${error.message}`, 'error');
            }
        }
        
        function testControlsFunctionality() {
            if (!controls) return;
            
            log('🔍 测试控制器功能...', 'info');
            
            // 测试属性
            log(`   - enableDamping: ${controls.enableDamping}`, 'info');
            log(`   - enableZoom: ${controls.enableZoom}`, 'info');
            log(`   - enablePan: ${controls.enablePan}`, 'info');
            log(`   - enableRotate: ${controls.enableRotate}`, 'info');
            
            // 测试方法
            try {
                controls.saveState();
                log('   - saveState(): ✅', 'success');
            } catch (e) {
                log('   - saveState(): ❌', 'error');
            }
        }
        
        function resetCamera() {
            if (camera && controls) {
                camera.position.set(50, 50, 50);
                controls.target.set(0, 0, 0);
                controls.update();
                log('📷 相机位置已重置', 'success');
            } else {
                log('❌ 相机或控制器未初始化', 'error');
            }
        }
        
        function animate() {
            requestAnimationFrame(animate);
            
            // 更新控制器
            if (controls) {
                controls.update();
            }
            
            // 渲染场景
            if (renderer && scene && camera) {
                renderer.render(scene, camera);
            }
        }
        
        // 窗口大小变化处理
        window.addEventListener('resize', () => {
            if (camera && renderer) {
                const canvas = renderer.domElement;
                const rect = canvas.getBoundingClientRect();
                
                camera.aspect = rect.width / rect.height;
                camera.updateProjectionMatrix();
                renderer.setSize(rect.width, rect.height);
                
                log('📐 窗口大小已更新', 'info');
            }
        });
        
        // 页面加载完成后自动创建场景
        window.addEventListener('DOMContentLoaded', () => {
            log('🌐 页面已加载', 'info');
            log('点击"创建场景"开始测试', 'info');
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            log(`❌ 全局错误: ${event.error.message}`, 'error');
        });
    </script>
</body>
</html>