/**
 * 施工进度UI管理器
 * 负责进度展示、时间轴控制等界面功能
 */

class ProgressUI {
    constructor(progressManager) {
        this.progressManager = progressManager;
        
        this.elements = {};
        this.isTimelineDragging = false;
        this.phaseButtons = [];
        
        this.init();
    }

    /**
     * 初始化进度UI
     */
    init() {
        console.log('📊 初始化进度UI...');
        
        this.createProgressControlPanel();
        this.createTimelineControl();
        this.createPhaseManager();
        this.setupEventListeners();
        
        console.log('✅ 进度UI初始化完成');
    }

    /**
     * 创建进度控制面板
     */
    createProgressControlPanel() {
        const rightPanel = document.querySelector('.right-panel');
        
        // 在施工进度section之前插入新的控制面板
        const progressSection = rightPanel.querySelector('.panel-section:nth-child(2)');
        
        const controlSection = document.createElement('div');
        controlSection.className = 'panel-section progress-control-section';
        controlSection.innerHTML = `
            <h3><i class="fas fa-sliders-h"></i> 进度控制</h3>
            <div class="progress-controls">
                <div class="overall-progress">
                    <div class="progress-header">
                        <span class="progress-label">总体进度</span>
                        <span class="progress-value" id="overall-progress-value">0%</span>
                    </div>
                    <div class="progress-bar-large">
                        <div class="progress-fill-large" id="overall-progress-fill"></div>
                    </div>
                </div>
                
                <div class="timeline-container">
                    <label class="timeline-label">时间轴控制</label>
                    <div class="timeline-wrapper">
                        <input type="range" id="progress-timeline" class="timeline-slider" 
                               min="0" max="100" value="0" step="1">
                        <div class="timeline-markers" id="timeline-markers"></div>
                    </div>
                    <div class="timeline-labels">
                        <span>项目开始</span>
                        <span>项目完成</span>
                    </div>
                </div>
                
                <div class="playback-controls">
                    <button id="play-pause-btn" class="playback-btn" title="播放/暂停">
                        <i class="fas fa-play"></i>
                    </button>
                    <button id="reset-btn" class="playback-btn" title="重置">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button id="fast-forward-btn" class="playback-btn" title="快进">
                        <i class="fas fa-fast-forward"></i>
                    </button>
                </div>
            </div>
        `;
        
        rightPanel.insertBefore(controlSection, progressSection);
        
        // 缓存元素
        this.elements.overallProgressValue = document.getElementById('overall-progress-value');
        this.elements.overallProgressFill = document.getElementById('overall-progress-fill');
        this.elements.progressTimeline = document.getElementById('progress-timeline');
        this.elements.timelineMarkers = document.getElementById('timeline-markers');
        this.elements.playPauseBtn = document.getElementById('play-pause-btn');
        this.elements.resetBtn = document.getElementById('reset-btn');
        this.elements.fastForwardBtn = document.getElementById('fast-forward-btn');
    }

    /**
     * 创建时间轴控制
     */
    createTimelineControl() {
        // 时间轴已在createProgressControlPanel中创建
        // 这里设置时间轴的交互逻辑
        
        const timeline = this.elements.progressTimeline;
        
        // 时间轴拖拽事件
        timeline.addEventListener('input', (e) => {
            if (!this.isTimelineDragging) return;
            
            const progress = parseInt(e.target.value);
            this.updateTimelineProgress(progress);
        });
        
        timeline.addEventListener('mousedown', () => {
            this.isTimelineDragging = true;
        });
        
        timeline.addEventListener('mouseup', () => {
            this.isTimelineDragging = false;
        });
        
        timeline.addEventListener('change', (e) => {
            const progress = parseInt(e.target.value);
            this.progressManager.setTimelineProgress(progress);
        });
    }

    /**
     * 创建阶段管理器
     */
    createPhaseManager() {
        const rightPanel = document.querySelector('.right-panel');
        const progressSection = rightPanel.querySelector('.panel-section:nth-child(3)'); // 现在是第三个
        
        const phaseSection = document.createElement('div');
        phaseSection.className = 'panel-section phase-manager-section';
        phaseSection.innerHTML = `
            <h3><i class="fas fa-tasks"></i> 施工阶段</h3>
            <div class="phase-list" id="phase-list">
                <!-- 动态生成阶段列表 -->
            </div>
            <div class="phase-controls">
                <button id="prev-phase-btn" class="phase-nav-btn" title="上一阶段">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button id="next-phase-btn" class="phase-nav-btn" title="下一阶段">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        `;
        
        rightPanel.insertBefore(phaseSection, progressSection);
        
        // 缓存元素
        this.elements.phaseList = document.getElementById('phase-list');
        this.elements.prevPhaseBtn = document.getElementById('prev-phase-btn');
        this.elements.nextPhaseBtn = document.getElementById('next-phase-btn');
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 播放控制
        this.elements.playPauseBtn.addEventListener('click', () => {
            this.togglePlayback();
        });
        
        this.elements.resetBtn.addEventListener('click', () => {
            this.resetProgress();
        });
        
        this.elements.fastForwardBtn.addEventListener('click', () => {
            this.fastForward();
        });
        
        // 阶段导航
        this.elements.prevPhaseBtn.addEventListener('click', () => {
            this.navigatePhase(-1);
        });
        
        this.elements.nextPhaseBtn.addEventListener('click', () => {
            this.navigatePhase(1);
        });
        
        // 监听进度管理器事件（安全检查）
        if (this.progressManager && typeof this.progressManager.on === 'function') {
            this.progressManager.on('phaseProgressUpdated', (data) => {
                this.onPhaseProgressUpdated(data);
            });

            this.progressManager.on('timelineUpdated', (data) => {
                this.onTimelineUpdated(data);
            });
        } else {
            console.warn('⚠️ 进度管理器事件系统未就绪');
        }
    }

    /**
     * 更新进度数据
     */
    updateProgressData(progressData) {
        this.progressData = progressData;
        this.updatePhaseList(progressData.phases);
        this.updateTimelineMarkers(progressData.phases);
        this.updateOverallProgress(progressData.overallProgress || 0);
    }

    /**
     * 更新阶段列表
     */
    updatePhaseList(phases) {
        const phaseList = this.elements.phaseList;
        phaseList.innerHTML = '';
        
        this.phaseButtons = [];
        
        phases.forEach((phase, index) => {
            const phaseItem = document.createElement('div');
            phaseItem.className = 'phase-item';
            phaseItem.innerHTML = `
                <div class="phase-header" data-phase="${phase.id}">
                    <div class="phase-info">
                        <span class="phase-name">${phase.name}</span>
                        <span class="phase-dates">${this.formatDateRange(phase.startDate, phase.endDate)}</span>
                    </div>
                    <div class="phase-progress-display">
                        <span class="phase-progress-text">${phase.progress}%</span>
                        <div class="phase-color-indicator" style="background-color: ${phase.color}"></div>
                    </div>
                </div>
                <div class="phase-progress-bar">
                    <div class="phase-progress-fill" style="width: ${phase.progress}%; background-color: ${phase.color}"></div>
                </div>
                <div class="phase-controls">
                    <input type="range" class="phase-slider" data-phase="${phase.id}" 
                           min="0" max="100" value="${phase.progress}" step="1">
                </div>
            `;
            
            phaseList.appendChild(phaseItem);
            
            // 添加阶段按钮事件
            const phaseHeader = phaseItem.querySelector('.phase-header');
            const phaseSlider = phaseItem.querySelector('.phase-slider');
            
            phaseHeader.addEventListener('click', () => {
                this.selectPhase(phase.id);
            });
            
            phaseSlider.addEventListener('input', (e) => {
                const progress = parseInt(e.target.value);
                this.updatePhaseProgressDisplay(phase.id, progress);
            });
            
            phaseSlider.addEventListener('change', (e) => {
                const progress = parseInt(e.target.value);
                this.progressManager.updatePhaseProgress(phase.id, progress);
            });
            
            this.phaseButtons.push({
                element: phaseItem,
                phase: phase,
                slider: phaseSlider
            });
        });
    }

    /**
     * 更新时间轴标记
     */
    updateTimelineMarkers(phases) {
        const markers = this.elements.timelineMarkers;
        markers.innerHTML = '';
        
        phases.forEach((phase, index) => {
            const marker = document.createElement('div');
            marker.className = 'timeline-marker';
            marker.style.left = `${(index / (phases.length - 1)) * 100}%`;
            marker.style.backgroundColor = phase.color;
            marker.title = phase.name;
            markers.appendChild(marker);
        });
    }

    /**
     * 更新总体进度
     */
    updateOverallProgress(progress) {
        this.elements.overallProgressValue.textContent = `${progress}%`;
        this.elements.overallProgressFill.style.width = `${progress}%`;
        this.elements.progressTimeline.value = progress;
    }

    /**
     * 选择阶段
     */
    selectPhase(phaseId) {
        // 移除所有活动状态
        this.phaseButtons.forEach(btn => {
            btn.element.classList.remove('active');
        });
        
        // 添加当前阶段活动状态
        const currentButton = this.phaseButtons.find(btn => btn.phase.id === phaseId);
        if (currentButton) {
            currentButton.element.classList.add('active');
        }
        
        // 切换到该阶段
        this.progressManager.switchToPhase(phaseId);
    }

    /**
     * 阶段进度更新处理
     */
    onPhaseProgressUpdated(data) {
        // 更新阶段显示
        const phaseButton = this.phaseButtons.find(btn => btn.phase.id === data.phaseId);
        if (phaseButton) {
            const progressText = phaseButton.element.querySelector('.phase-progress-text');
            const progressFill = phaseButton.element.querySelector('.phase-progress-fill');
            
            progressText.textContent = `${data.newProgress}%`;
            progressFill.style.width = `${data.newProgress}%`;
            phaseButton.slider.value = data.newProgress;
        }
        
        // 更新总体进度
        this.updateOverallProgress(data.overallProgress);
    }

    /**
     * 时间轴更新处理
     */
    onTimelineUpdated(data) {
        this.updateOverallProgress(data.progress);
        
        // 更新所有阶段显示
        data.phases.forEach(phase => {
            this.updatePhaseProgressDisplay(phase.id, phase.progress);
        });
    }

    /**
     * 更新阶段进度显示
     */
    updatePhaseProgressDisplay(phaseId, progress) {
        const phaseButton = this.phaseButtons.find(btn => btn.phase.id === phaseId);
        if (phaseButton) {
            const progressText = phaseButton.element.querySelector('.phase-progress-text');
            const progressFill = phaseButton.element.querySelector('.phase-progress-fill');
            
            progressText.textContent = `${progress}%`;
            progressFill.style.width = `${progress}%`;
        }
    }

    /**
     * 格式化日期范围
     */
    formatDateRange(startDate, endDate) {
        const start = new Date(startDate).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
        const end = new Date(endDate).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
        return `${start} - ${end}`;
    }

    /**
     * 播放控制
     */
    togglePlayback() {
        // 实现自动播放进度功能
        console.log('▶️ 切换播放状态');
    }

    /**
     * 重置进度
     */
    resetProgress() {
        this.progressManager.setTimelineProgress(0);
        console.log('🔄 进度已重置');
    }

    /**
     * 快进
     */
    fastForward() {
        this.progressManager.setTimelineProgress(100);
        console.log('⏩ 快进到完成');
    }

    /**
     * 阶段导航
     */
    navigatePhase(direction) {
        const currentIndex = this.phaseButtons.findIndex(btn => 
            btn.element.classList.contains('active')
        );
        
        const newIndex = currentIndex + direction;
        if (newIndex >= 0 && newIndex < this.phaseButtons.length) {
            const newPhase = this.phaseButtons[newIndex].phase;
            this.selectPhase(newPhase.id);
        }
    }

    /**
     * 更新时间轴进度
     */
    updateTimelineProgress(progress) {
        // 实时更新显示，但不触发进度管理器
        this.elements.overallProgressValue.textContent = `${progress}%`;
        this.elements.overallProgressFill.style.width = `${progress}%`;
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.phaseButtons = [];
        console.log('🧹 进度UI资源已清理');
    }
}
