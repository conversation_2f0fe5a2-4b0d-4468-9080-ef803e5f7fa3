/**
 * 内存管理器
 * 提供智能内存监控、清理和优化功能
 */

class MemoryManager {
    constructor(core) {
        this.core = core;
        
        // 内存监控
        this.memoryStats = {
            jsHeapSize: 0,
            totalJSHeapSize: 0,
            jsHeapSizeLimit: 0,
            used: 0,
            total: 0,
            limit: 0
        };
        
        // 资源追踪
        this.trackedResources = new Map();
        this.resourceTypes = new Map([
            ['geometry', []],
            ['material', []],
            ['texture', []],
            ['model', []],
            ['animation', []]
        ]);
        
        // 清理配置
        this.cleanupConfig = {
            autoCleanup: true,
            memoryThreshold: 0.8, // 内存使用超过80%时清理
            cleanupInterval: 30000, // 30秒检查一次
            maxCacheAge: 300000, // 5分钟未使用的资源被清理
            aggressiveMode: false
        };
        
        // 清理策略
        this.cleanupStrategies = [];
        this.initializeStrategies();
        
        // 监控状态
        this.isMonitoring = false;
        this.monitoringInterval = null;
        
        // 垃圾回收
        this.gcStats = {
            lastGC: 0,
            gcCount: 0,
            forcedGC: 0
        };
    }

    /**
     * 初始化清理策略
     */
    initializeStrategies() {
        this.cleanupStrategies = [
            {
                name: 'clearUnusedTextures',
                priority: 1,
                action: () => this.clearUnusedTextures(),
                description: '清理未使用的纹理'
            },
            {
                name: 'clearUnusedGeometries',
                priority: 2,
                action: () => this.clearUnusedGeometries(),
                description: '清理未使用的几何体'
            },
            {
                name: 'clearUnusedMaterials',
                priority: 3,
                action: () => this.clearUnusedMaterials(),
                description: '清理未使用的材质'
            },
            {
                name: 'clearModelCache',
                priority: 4,
                action: () => this.clearModelCache(),
                description: '清理模型缓存'
            },
            {
                name: 'optimizeArrayBuffers',
                priority: 5,
                action: () => this.optimizeArrayBuffers(),
                description: '优化数组缓冲区'
            },
            {
                name: 'forceGarbageCollection',
                priority: 6,
                action: () => this.forceGarbageCollection(),
                description: '强制垃圾回收'
            }
        ];
    }

    /**
     * 初始化内存管理
     */
    async initialize() {
        console.log('💾 初始化内存管理器');
        
        // 检查内存API支持
        this.checkMemoryAPISupport();
        
        // 启动监控
        this.startMonitoring();
        
        // 设置事件监听
        this.setupEventListeners();
        
        console.log('✅ 内存管理器初始化完成');
    }

    /**
     * 检查内存API支持
     */
    checkMemoryAPISupport() {
        if (performance.memory) {
            console.log('✅ 浏览器支持内存监控API');
        } else {
            console.warn('⚠️ 浏览器不支持内存监控API，使用模拟数据');
        }
    }

    /**
     * 设置事件监听
     */
    setupEventListeners() {
        // 监听资源加载事件
        this.core.eventBus.on('model:loaded', (data) => {
            this.trackResource('model', data.gltf, data.modelInfo);
        });
        
        // 监听场景清理事件
        this.core.eventBus.on('scene:cleared', () => {
            this.clearSceneResources();
        });
        
        // 监听项目卸载事件
        this.core.eventBus.on('project:unloading', () => {
            this.clearProjectResources();
        });
        
        // 监听内存警告
        this.core.eventBus.on('memory:warning', (data) => {
            this.handleMemoryWarning(data);
        });
    }

    /**
     * 启动内存监控
     */
    startMonitoring() {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.updateMemoryStats();
            this.checkMemoryUsage();
        }, this.cleanupConfig.cleanupInterval);
        
        console.log('📊 内存监控已启动');
    }

    /**
     * 停止内存监控
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.isMonitoring = false;
        console.log('⏹️ 内存监控已停止');
    }

    /**
     * 更新内存统计
     */
    updateMemoryStats() {
        if (performance.memory) {
            const memory = performance.memory;
            this.memoryStats = {
                jsHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit,
                used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
            };
        } else {
            // 模拟内存统计
            this.memoryStats.used = Math.round(Math.random() * 200 + 100);
            this.memoryStats.total = this.memoryStats.used + Math.round(Math.random() * 100);
            this.memoryStats.limit = 1024;
        }
    }

    /**
     * 检查内存使用情况
     */
    checkMemoryUsage() {
        const usageRatio = this.memoryStats.used / this.memoryStats.limit;
        
        if (usageRatio > this.cleanupConfig.memoryThreshold) {
            console.warn(`⚠️ 内存使用过高: ${(usageRatio * 100).toFixed(1)}%`);
            
            this.core.eventBus.emit('memory:warning', {
                usageRatio,
                used: this.memoryStats.used,
                limit: this.memoryStats.limit
            });
            
            if (this.cleanupConfig.autoCleanup) {
                this.performCleanup('auto');
            }
        }
    }

    /**
     * 追踪资源
     */
    trackResource(type, resource, metadata = {}) {
        const resourceId = this.generateResourceId(type, resource);
        
        const resourceInfo = {
            id: resourceId,
            type,
            resource,
            metadata,
            createdAt: Date.now(),
            lastUsed: Date.now(),
            references: 1,
            memorySize: this.estimateResourceSize(type, resource)
        };
        
        this.trackedResources.set(resourceId, resourceInfo);
        
        if (!this.resourceTypes.has(type)) {
            this.resourceTypes.set(type, []);
        }
        this.resourceTypes.get(type).push(resourceId);
        
        console.log(`📋 追踪资源: ${type} (${resourceId}) - ${resourceInfo.memorySize}MB`);
    }

    /**
     * 生成资源ID
     */
    generateResourceId(type, resource) {
        if (resource.uuid) {
            return `${type}_${resource.uuid}`;
        }
        if (resource.name) {
            return `${type}_${resource.name}`;
        }
        return `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 估算资源大小
     */
    estimateResourceSize(type, resource) {
        let size = 0;
        
        switch (type) {
            case 'geometry':
                if (resource.attributes) {
                    Object.values(resource.attributes).forEach(attr => {
                        size += attr.array.byteLength;
                    });
                    if (resource.index) {
                        size += resource.index.array.byteLength;
                    }
                }
                break;
                
            case 'texture':
                if (resource.image) {
                    const img = resource.image;
                    size = (img.width || 512) * (img.height || 512) * 4; // RGBA
                }
                break;
                
            case 'material':
                size = 1024; // 材质基础大小
                // 添加纹理大小
                Object.values(resource).forEach(value => {
                    if (value && value.isTexture) {
                        size += this.estimateResourceSize('texture', value);
                    }
                });
                break;
                
            case 'model':
                if (resource.scene) {
                    resource.scene.traverse(child => {
                        if (child.isMesh) {
                            if (child.geometry) {
                                size += this.estimateResourceSize('geometry', child.geometry);
                            }
                            if (child.material) {
                                size += this.estimateResourceSize('material', child.material);
                            }
                        }
                    });
                }
                break;
                
            default:
                size = 1024; // 默认1KB
        }
        
        return Math.round(size / 1024 / 1024 * 100) / 100; // MB，保留两位小数
    }

    /**
     * 更新资源使用时间
     */
    updateResourceUsage(resourceId) {
        if (this.trackedResources.has(resourceId)) {
            this.trackedResources.get(resourceId).lastUsed = Date.now();
        }
    }

    /**
     * 执行清理
     */
    async performCleanup(trigger = 'manual') {
        console.log(`🧹 开始内存清理 (触发: ${trigger})`);
        
        const initialMemory = this.memoryStats.used;
        let cleanupCount = 0;
        
        // 按优先级执行清理策略
        const sortedStrategies = [...this.cleanupStrategies].sort((a, b) => a.priority - b.priority);
        
        for (const strategy of sortedStrategies) {
            try {
                console.log(`🔧 执行清理策略: ${strategy.description}`);
                const result = await strategy.action();
                
                if (result && result.cleaned > 0) {
                    cleanupCount += result.cleaned;
                    
                    // 更新内存统计
                    this.updateMemoryStats();
                    
                    // 检查是否已达到清理目标
                    const currentUsage = this.memoryStats.used / this.memoryStats.limit;
                    if (currentUsage < this.cleanupConfig.memoryThreshold * 0.9) {
                        console.log('✅ 内存使用已降至安全水平，停止清理');
                        break;
                    }
                }
                
                // 给浏览器一些时间处理
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.error(`❌ 清理策略失败: ${strategy.name}`, error);
            }
        }
        
        const finalMemory = this.memoryStats.used;
        const savedMemory = initialMemory - finalMemory;
        
        console.log(`✅ 内存清理完成: 释放${savedMemory}MB, 清理${cleanupCount}个资源`);
        
        this.core.eventBus.emit('memory:cleaned', {
            trigger,
            savedMemory,
            cleanupCount,
            initialMemory,
            finalMemory
        });
        
        return {
            savedMemory,
            cleanupCount
        };
    }

    /**
     * 清理策略实现
     */

    async clearUnusedTextures() {
        let cleaned = 0;
        const now = Date.now();
        const textureIds = this.resourceTypes.get('texture') || [];
        
        for (const id of textureIds) {
            const resource = this.trackedResources.get(id);
            if (resource && now - resource.lastUsed > this.cleanupConfig.maxCacheAge) {
                if (resource.resource && resource.resource.dispose) {
                    resource.resource.dispose();
                    this.trackedResources.delete(id);
                    cleaned++;
                }
            }
        }
        
        // 更新类型列表
        this.resourceTypes.set('texture', 
            textureIds.filter(id => this.trackedResources.has(id))
        );
        
        console.log(`🖼️ 清理未使用纹理: ${cleaned}个`);
        return { cleaned };
    }

    async clearUnusedGeometries() {
        let cleaned = 0;
        const now = Date.now();
        const geometryIds = this.resourceTypes.get('geometry') || [];
        
        for (const id of geometryIds) {
            const resource = this.trackedResources.get(id);
            if (resource && now - resource.lastUsed > this.cleanupConfig.maxCacheAge) {
                if (resource.resource && resource.resource.dispose) {
                    resource.resource.dispose();
                    this.trackedResources.delete(id);
                    cleaned++;
                }
            }
        }
        
        this.resourceTypes.set('geometry',
            geometryIds.filter(id => this.trackedResources.has(id))
        );
        
        console.log(`📐 清理未使用几何体: ${cleaned}个`);
        return { cleaned };
    }

    async clearUnusedMaterials() {
        let cleaned = 0;
        const now = Date.now();
        const materialIds = this.resourceTypes.get('material') || [];
        
        for (const id of materialIds) {
            const resource = this.trackedResources.get(id);
            if (resource && now - resource.lastUsed > this.cleanupConfig.maxCacheAge) {
                if (resource.resource && resource.resource.dispose) {
                    resource.resource.dispose();
                    this.trackedResources.delete(id);
                    cleaned++;
                }
            }
        }
        
        this.resourceTypes.set('material',
            materialIds.filter(id => this.trackedResources.has(id))
        );
        
        console.log(`🎨 清理未使用材质: ${cleaned}个`);
        return { cleaned };
    }

    async clearModelCache() {
        let cleaned = 0;
        
        try {
            const modelService = await this.core.getService('model');
            const stats = modelService.getStats();
            
            // 清理部分模型缓存
            const modelIds = this.resourceTypes.get('model') || [];
            const now = Date.now();
            
            for (const id of modelIds) {
                const resource = this.trackedResources.get(id);
                if (resource && now - resource.lastUsed > this.cleanupConfig.maxCacheAge) {
                    // 通知模型服务卸载模型
                    if (resource.metadata && resource.metadata.path) {
                        await modelService.unloadModel(resource.metadata);
                        this.trackedResources.delete(id);
                        cleaned++;
                    }
                }
            }
            
            this.resourceTypes.set('model',
                modelIds.filter(id => this.trackedResources.has(id))
            );
            
        } catch (error) {
            console.warn('⚠️ 清理模型缓存部分失败:', error);
        }
        
        console.log(`🎯 清理模型缓存: ${cleaned}个`);
        return { cleaned };
    }

    async optimizeArrayBuffers() {
        let cleaned = 0;
        
        // 查找可以优化的数组缓冲区
        this.trackedResources.forEach((resource) => {
            if (resource.type === 'geometry' && resource.resource.attributes) {
                // 检查是否有未使用的属性
                const attributes = resource.resource.attributes;
                Object.keys(attributes).forEach(key => {
                    if (key.startsWith('_') || key === 'morphTarget') {
                        // 删除未使用的属性
                        delete attributes[key];
                        cleaned++;
                    }
                });
            }
        });
        
        console.log(`📊 优化数组缓冲区: ${cleaned}个`);
        return { cleaned };
    }

    async forceGarbageCollection() {
        if (window.gc && typeof window.gc === 'function') {
            window.gc();
            this.gcStats.forcedGC++;
            console.log('🗑️ 强制垃圾回收');
            return { cleaned: 1 };
        } else {
            // 尝试触发垃圾回收
            const largeArray = new Array(1000000).fill(0);
            largeArray.length = 0;
            console.log('🗑️ 尝试触发垃圾回收');
            return { cleaned: 1 };
        }
    }

    /**
     * 清理场景资源
     */
    clearSceneResources() {
        console.log('🧹 清理场景相关资源');
        
        let cleaned = 0;
        this.trackedResources.forEach((resource, id) => {
            if (resource.type === 'model' || 
                resource.metadata?.source === 'scene') {
                
                if (resource.resource && resource.resource.dispose) {
                    resource.resource.dispose();
                }
                this.trackedResources.delete(id);
                cleaned++;
            }
        });
        
        // 更新类型列表
        this.resourceTypes.forEach((ids, type) => {
            this.resourceTypes.set(type, 
                ids.filter(id => this.trackedResources.has(id))
            );
        });
        
        console.log(`✅ 场景资源清理完成: ${cleaned}个`);
    }

    /**
     * 清理项目资源
     */
    clearProjectResources() {
        console.log('🧹 清理项目相关资源');
        
        let cleaned = 0;
        this.trackedResources.forEach((resource, id) => {
            if (resource.metadata?.source === 'project') {
                if (resource.resource && resource.resource.dispose) {
                    resource.resource.dispose();
                }
                this.trackedResources.delete(id);
                cleaned++;
            }
        });
        
        console.log(`✅ 项目资源清理完成: ${cleaned}个`);
    }

    /**
     * 处理内存警告
     */
    async handleMemoryWarning(data) {
        console.warn(`⚠️ 内存警告: 使用率${(data.usageRatio * 100).toFixed(1)}%`);
        
        // 自动切换到激进清理模式
        if (data.usageRatio > 0.9) {
            this.cleanupConfig.aggressiveMode = true;
            this.cleanupConfig.maxCacheAge = 60000; // 1分钟
            console.log('🔥 启用激进清理模式');
        }
        
        // 强制清理
        await this.performCleanup('warning');
    }

    /**
     * 获取内存报告
     */
    getMemoryReport() {
        // 按类型统计资源
        const resourceStats = {};
        this.resourceTypes.forEach((ids, type) => {
            const resources = ids.map(id => this.trackedResources.get(id)).filter(Boolean);
            const totalSize = resources.reduce((sum, r) => sum + r.memorySize, 0);
            
            resourceStats[type] = {
                count: resources.length,
                totalSize: Math.round(totalSize * 100) / 100,
                averageAge: resources.length > 0 ? 
                    Math.round((Date.now() - resources.reduce((sum, r) => sum + r.createdAt, 0) / resources.length) / 1000) : 0
            };
        });
        
        return {
            memory: { ...this.memoryStats },
            usage: {
                ratio: this.memoryStats.used / this.memoryStats.limit,
                status: this.getMemoryStatus()
            },
            resources: resourceStats,
            totalTracked: this.trackedResources.size,
            cleanupConfig: { ...this.cleanupConfig },
            gcStats: { ...this.gcStats },
            recommendations: this.getMemoryRecommendations()
        };
    }

    /**
     * 获取内存状态
     */
    getMemoryStatus() {
        const ratio = this.memoryStats.used / this.memoryStats.limit;
        
        if (ratio > 0.9) return 'critical';
        if (ratio > 0.8) return 'warning';
        if (ratio > 0.6) return 'caution';
        return 'good';
    }

    /**
     * 获取内存优化建议
     */
    getMemoryRecommendations() {
        const recommendations = [];
        const ratio = this.memoryStats.used / this.memoryStats.limit;
        
        if (ratio > 0.8) {
            recommendations.push({
                type: 'cleanup',
                message: '内存使用过高，建议执行清理操作',
                priority: 'high',
                action: 'performCleanup'
            });
        }
        
        if (this.trackedResources.size > 1000) {
            recommendations.push({
                type: 'resources',
                message: '追踪的资源过多，考虑降低缓存大小',
                priority: 'medium',
                action: 'reduceCache'
            });
        }
        
        const oldResources = Array.from(this.trackedResources.values())
            .filter(r => Date.now() - r.lastUsed > this.cleanupConfig.maxCacheAge);
            
        if (oldResources.length > 50) {
            recommendations.push({
                type: 'cache',
                message: '存在大量未使用资源，建议清理缓存',
                priority: 'medium',
                action: 'clearUnusedResources'
            });
        }
        
        return recommendations;
    }

    /**
     * 设置清理配置
     */
    setCleanupConfig(config) {
        Object.assign(this.cleanupConfig, config);
        console.log('⚙️ 内存清理配置已更新');
    }

    /**
     * 手动清理
     */
    async manualCleanup() {
        return await this.performCleanup('manual');
    }

    /**
     * 获取当前内存统计
     */
    getCurrentMemoryStats() {
        return { ...this.memoryStats };
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('🧹 清理内存管理器资源');
        
        this.stopMonitoring();
        
        // 清理所有追踪的资源
        this.trackedResources.forEach((resource) => {
            if (resource.resource && resource.resource.dispose) {
                try {
                    resource.resource.dispose();
                } catch (error) {
                    console.warn('⚠️ 资源清理失败:', error);
                }
            }
        });
        
        this.trackedResources.clear();
        this.resourceTypes.clear();
        
        console.log('✅ 内存管理器资源清理完成');
    }
}

// 导出内存管理器
window.MemoryManager = MemoryManager;