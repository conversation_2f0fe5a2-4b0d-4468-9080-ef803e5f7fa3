/**
 * 项目服务
 * 重构后的项目管理，完全基于配置驱动
 */

class ProjectService {
    constructor(core) {
        this.core = core;
        this.currentProject = null;
        this.projectCache = new Map();
        this.configCache = new Map();
        
        // 预定义项目列表
        this.availableProjects = [
            {
                id: 'bridge-demo',
                name: '示例大桥项目',
                path: 'projects/bridge-demo',
                description: '一个完整的桥梁建设演示项目',
                thumbnail: 'assets/thumbnails/bridge-demo.jpg'
            },
            {
                id: 'highway-project',
                name: '高速公路项目',
                path: 'projects/highway-project',
                description: '高速公路建设项目演示',
                thumbnail: 'assets/thumbnails/highway-project.jpg'
            },
            {
                id: 'gddwroad',
                name: '公路道路工程项目',
                path: 'projects/gddwroad',
                description: '公路道路工程建设项目演示，包含完整的道路建设模型',
                thumbnail: 'assets/thumbnails/gddwroad.jpg'
            }
        ];
    }

    /**
     * 初始化服务
     */
    async initialize() {
        console.log('📂 初始化项目服务');
        
        // 验证所有项目
        await this.validateProjects();
        
        // 加载默认项目
        if (this.availableProjects.length > 0) {
            await this.loadProject(this.availableProjects[0].id);
        }
        
        console.log('✅ 项目服务初始化完成');
    }

    /**
     * 验证项目
     */
    async validateProjects() {
        const validProjects = [];
        
        for (const project of this.availableProjects) {
            try {
                const config = await this.loadProjectConfig(project.id);
                if (config) {
                    project.config = config;
                    project.isValid = true;
                    validProjects.push(project);
                    console.log(`✅ 项目验证成功: ${project.name}`);
                }
            } catch (error) {
                console.warn(`⚠️ 项目验证失败: ${project.name}`, error);
                project.isValid = false;
            }
        }
        
        this.availableProjects = validProjects;
    }

    /**
     * 加载项目配置
     */
    async loadProjectConfig(projectId) {
        // 检查缓存
        if (this.configCache.has(projectId)) {
            return this.configCache.get(projectId);
        }

        try {
            // 根据项目ID生成配置
            const config = this.generateProjectConfig(projectId);
            
            // 缓存配置
            this.configCache.set(projectId, config);
            
            return config;
            
        } catch (error) {
            console.error(`❌ 加载项目配置失败: ${projectId}`, error);
            throw error;
        }
    }

    /**
     * 生成项目配置（基于项目类型）
     */
    generateProjectConfig(projectId) {
        const baseConfig = {
            name: '默认项目',
            version: '1.0.0',
            description: '默认项目描述',
            camera: {
                position: [-800, 200, 800],
                target: [0, 50, 0],
                fov: 75,
                near: 0.1,
                far: 10000
            },
            lighting: {
                intensity: 1.0,
                shadows: true,
                ambientLight: 0x404040,
                directionalLight: {
                    color: 0xffffff,
                    position: [100, 100, 100],
                    castShadow: true
                }
            },
            phases: []
        };

        // 根据项目ID定制配置
        switch (projectId) {
            case 'gddwroad':
                return this.generateGddwroadConfig(baseConfig);
            case 'bridge-demo':
                return this.generateBridgeDemoConfig(baseConfig);
            case 'highway-project':
                return this.generateHighwayConfig(baseConfig);
            default:
                return baseConfig;
        }
    }

    /**
     * 生成gddwroad项目配置
     */
    generateGddwroadConfig(baseConfig) {
        return {
            ...baseConfig,
            name: '公路道路工程项目',
            description: '完整的公路道路工程建设演示',
            type: 'road',
            camera: {
                position: [0, 100, 300],
                target: [0, 0, 0],
                fov: 60,
                near: 0.1,
                far: 5000
            },
            phases: [
                {
                    id: 'phase1',
                    name: '基础建设',
                    description: '道路基础设施建设',
                    progress: 0,
                    color: '#3498db',
                    models: [
                        {
                            name: 'gsdx1.glb',
                            path: 'projects/gddwroad/models/gsdx1.glb',
                            type: 'road',
                            phase: 'phase1',
                            description: '公路道路工程完整模型',
                            visible: true,
                            position: [0, 0, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: [
                        '地面平整',
                        '基础铺设',
                        '排水系统'
                    ],
                    duration: 30,
                    equipment: []
                },
                {
                    id: 'phase2',
                    name: '路面建设',
                    description: '主要道路铺设',
                    progress: 0,
                    color: '#2ecc71',
                    models: [],
                    tasks: [
                        '路面铺装',
                        '标线绘制',
                        '护栏安装'
                    ],
                    duration: 20,
                    equipment: []
                },
                {
                    id: 'phase3',
                    name: '完善工程',
                    description: '配套设施建设',
                    progress: 0,
                    color: '#e74c3c',
                    models: [],
                    tasks: [
                        '交通标志',
                        '绿化工程',
                        '验收测试'
                    ],
                    duration: 15,
                    equipment: []
                }
            ],
            specifications: {
                totalLength: '5.2km',
                roadWidth: '12m',
                designSpeed: '60km/h',
                surfaceType: '沥青混凝土'
            },
            viewpoints: [
                {
                    name: '鸟瞰视角',
                    position: [0, 200, 400],
                    target: [0, 0, 0]
                },
                {
                    name: '侧面视角',
                    position: [300, 50, 0],
                    target: [0, 0, 0]
                },
                {
                    name: '道路视角',
                    position: [0, 5, 100],
                    target: [0, 5, -100]
                }
            ]
        };
    }

    /**
     * 生成桥梁演示配置
     */
    generateBridgeDemoConfig(baseConfig) {
        return {
            ...baseConfig,
            name: '示例大桥项目',
            description: '大型桥梁建设演示项目',
            type: 'bridge',
            phases: [
                {
                    id: 'foundation',
                    name: '基础工程',
                    description: '桥梁基础建设',
                    progress: 0,
                    color: '#8b4513',
                    models: [
                        {
                            name: 'foundation.gltf',
                            path: 'projects/bridge-demo/models/foundation.gltf',
                            type: 'foundation',
                            phase: 'foundation',
                            description: '桥梁基础结构',
                            visible: true,
                            position: [0, -20, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: ['挖掘基坑', '浇筑基础', '养护'],
                    duration: 45
                },
                {
                    id: 'pier',
                    name: '桥墩建设',
                    description: '主要支撑结构',
                    progress: 0,
                    color: '#708090',
                    models: [
                        {
                            name: 'piers.gltf',
                            path: 'projects/bridge-demo/models/piers.gltf',
                            type: 'pier',
                            phase: 'pier',
                            description: '桥墩结构',
                            visible: true,
                            position: [0, 0, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: ['模板搭建', '钢筋绑扎', '混凝土浇筑'],
                    duration: 30
                },
                {
                    id: 'beam',
                    name: '主梁安装',
                    description: '桥梁主体结构',
                    progress: 0,
                    color: '#4682b4',
                    models: [
                        {
                            name: 'main-beam.gltf',
                            path: 'projects/bridge-demo/models/main-beam.gltf',
                            type: 'beam',
                            phase: 'beam',
                            description: '主梁结构',
                            visible: true,
                            position: [0, 50, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: ['预制梁运输', '梁体吊装', '连接施工'],
                    duration: 25
                },
                {
                    id: 'deck',
                    name: '桥面工程',
                    description: '桥面铺装和附属设施',
                    progress: 0,
                    color: '#2e8b57',
                    models: [
                        {
                            name: 'deck.gltf',
                            path: 'projects/bridge-demo/models/deck.gltf',
                            type: 'deck',
                            phase: 'deck',
                            description: '桥面结构',
                            visible: true,
                            position: [0, 55, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: ['桥面铺装', '护栏安装', '标线绘制'],
                    duration: 20
                }
            ],
            specifications: {
                totalLength: '1200m',
                bridgeWidth: '28m',
                mainSpan: '300m',
                bridgeType: '预应力混凝土连续梁桥'
            }
        };
    }

    /**
     * 生成高速公路配置
     */
    generateHighwayConfig(baseConfig) {
        return {
            ...baseConfig,
            name: '高速公路项目',
            description: '高速公路建设演示',
            type: 'highway',
            phases: [
                {
                    id: 'earthwork',
                    name: '土石方工程',
                    description: '道路基础建设',
                    progress: 0,
                    color: '#a0522d',
                    models: [
                        {
                            name: 'earthwork.gltf',
                            path: 'projects/highway-project/models/earthwork.gltf',
                            type: 'earthwork',
                            phase: 'earthwork',
                            description: '土石方工程',
                            visible: true,
                            position: [0, -10, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: ['路基开挖', '填料压实', '排水建设'],
                    duration: 40
                },
                {
                    id: 'pavement',
                    name: '路面工程',
                    description: '路面结构建设',
                    progress: 0,
                    color: '#36454f',
                    models: [
                        {
                            name: 'pavement.gltf',
                            path: 'projects/highway-project/models/pavement.gltf',
                            type: 'pavement',
                            phase: 'pavement',
                            description: '路面结构',
                            visible: true,
                            position: [0, 0, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: ['基层铺设', '面层摊铺', '标线施工'],
                    duration: 30
                },
                {
                    id: 'facilities',
                    name: '配套设施',
                    description: '交通设施安装',
                    progress: 0,
                    color: '#ff6347',
                    models: [
                        {
                            name: 'facilities.gltf',
                            path: 'projects/highway-project/models/facilities.gltf',
                            type: 'facilities',
                            phase: 'facilities',
                            description: '配套设施',
                            visible: true,
                            position: [0, 2, 0],
                            rotation: [0, 0, 0],
                            scale: [1, 1, 1]
                        }
                    ],
                    tasks: ['护栏安装', '标志设置', '绿化工程'],
                    duration: 25
                }
            ],
            specifications: {
                totalLength: '25km',
                laneCount: '6车道',
                designSpeed: '120km/h',
                roadType: '高速公路'
            }
        };
    }

    /**
     * 加载项目
     */
    async loadProject(projectId) {
        try {
            console.log(`📂 加载项目: ${projectId}`);
            
            const project = this.availableProjects.find(p => p.id === projectId);
            if (!project) {
                throw new Error(`项目不存在: ${projectId}`);
            }

            // 确保配置已加载
            if (!project.config) {
                project.config = await this.loadProjectConfig(projectId);
            }

            this.currentProject = project;
            
            console.log(`✅ 项目加载成功: ${project.name}`);
            this.core.eventBus.emit('project:loaded', { project });
            
            return project;
            
        } catch (error) {
            console.error(`❌ 项目加载失败: ${projectId}`, error);
            this.core.errorHandler.handleError('项目加载失败', error);
            throw error;
        }
    }

    /**
     * 获取项目模型列表（从配置中提取）
     */
    getProjectModels(projectId = null) {
        const project = projectId ? 
            this.availableProjects.find(p => p.id === projectId) : 
            this.currentProject;

        if (!project || !project.config) {
            return [];
        }

        // 从所有阶段中提取模型
        const models = [];
        project.config.phases.forEach(phase => {
            if (phase.models) {
                phase.models.forEach(model => {
                    models.push({
                        ...model,
                        phase: phase.id,
                        phaseColor: phase.color
                    });
                });
            }
        });

        return models;
    }

    /**
     * 切换项目
     */
    async switchProject(projectId) {
        if (this.currentProject && this.currentProject.id === projectId) {
            console.log('项目已经是当前项目');
            return this.currentProject;
        }

        // 触发项目卸载事件
        if (this.currentProject) {
            this.core.eventBus.emit('project:unloading', { project: this.currentProject });
        }

        // 加载新项目
        return await this.loadProject(projectId);
    }

    /**
     * 保存项目配置
     */
    async saveProjectConfig(config = null) {
        if (!this.currentProject) {
            throw new Error('没有当前项目');
        }

        const configToSave = config || this.currentProject.config;
        
        try {
            // 更新内存中的配置
            this.currentProject.config = configToSave;
            this.configCache.set(this.currentProject.id, configToSave);
            
            console.log('💾 项目配置已保存');
            this.core.eventBus.emit('project:configSaved', { config: configToSave });
            
        } catch (error) {
            console.error('❌ 保存项目配置失败:', error);
            this.core.errorHandler.handleError('保存配置失败', error);
            throw error;
        }
    }

    /**
     * 获取当前项目
     */
    getCurrentProject() {
        return this.currentProject;
    }

    /**
     * 获取可用项目列表
     */
    getAvailableProjects() {
        return this.availableProjects;
    }

    /**
     * 获取项目统计
     */
    getProjectStats() {
        if (!this.currentProject) return null;

        const models = this.getProjectModels();
        return {
            name: this.currentProject.name,
            type: this.currentProject.config.type,
            modelCount: models.length,
            phases: this.currentProject.config.phases.length,
            overallProgress: this.calculateOverallProgress()
        };
    }

    /**
     * 计算总体进度
     */
    calculateOverallProgress() {
        if (!this.currentProject || !this.currentProject.config.phases) return 0;
        
        const phases = this.currentProject.config.phases;
        const totalProgress = phases.reduce((sum, phase) => sum + (phase.progress || 0), 0);
        
        return Math.round(totalProgress / phases.length);
    }

    /**
     * 清理资源
     */
    async cleanup() {
        this.projectCache.clear();
        this.configCache.clear();
        this.currentProject = null;
        console.log('🧹 项目服务资源已清理');
    }
}