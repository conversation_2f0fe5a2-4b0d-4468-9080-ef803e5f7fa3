# 增强版路桥修建演示系统

基于Three.js的Web 3D路桥建设项目演示系统，支持glTF模型加载、施工进度可视化和交互式项目管理。

## ✨ 主要特性

### 🗂️ 项目管理
- **多项目支持**：支持多个路桥项目的管理和切换
- **glTF模型加载**：完整支持glTF 2.0格式，包括Draco压缩
- **智能缓存**：模型缓存机制，提升加载性能
- **项目配置**：灵活的项目配置系统

### 📊 施工进度展示
- **动态可视化**：实时展示施工进度，支持透明度和颜色变化
- **时间轴控制**：拖拽时间轴查看不同施工阶段
- **阶段管理**：独立控制各个施工阶段的进度
- **平滑动画**：进度变化的平滑过渡动画

### 🛠️ 通用工具
- **测量工具**：距离、面积、体积、角度测量
- **标注系统**：3D场景标注和信息展示
- **视角控制**：预设视角和自定义视角保存
- **导出功能**：截图、数据导出、报告生成

### 🎨 用户界面
- **响应式设计**：适配不同屏幕尺寸
- **直观操作**：简洁明了的操作界面
- **实时反馈**：操作状态和进度的实时显示
- **键盘快捷键**：提升操作效率

## 🚀 快速开始

### 1. 启动系统
```bash
# 克隆或下载项目文件
# 启动本地服务器
python -m http.server 8000

# 在浏览器中访问
http://localhost:8000/enhanced-index.html
```

### 2. 选择项目
- 在左侧面板选择"示例大桥项目"或"高速公路项目"
- 系统会自动加载项目配置和模型列表

### 3. 加载模型
- 点击"加载全部"按钮加载所有模型
- 或单独选择需要的模型进行加载

### 4. 控制进度
- 使用右侧的时间轴控制整体进度
- 点击不同阶段查看详细进度
- 调整单个阶段的进度滑块

## 📁 项目结构

### 原有系统文件
- `index.html` - 原版系统主页面
- `js/scene.js` - 原版场景管理
- `js/ui.js` - 原版UI控制
- `js/controls.js` - 原版交互控制
- `js/materials.js` - 材质系统
- `js/geometry.js` - 几何体系统

### 增强版文件
- `enhanced-index.html` - 增强版主页面
- `js/enhanced-app.js` - 增强版主应用
- `js/managers/` - 各种管理器模块
- `js/ui/` - 增强版UI组件
- `js/tools/` - 工具模块
- `styles/enhanced-ui.css` - 增强版样式

## 🔧 配置说明

### 项目配置文件
每个项目都有一个 `config.json` 配置文件，包含：

```json
{
  "name": "项目名称",
  "description": "项目描述",
  "phases": [
    {
      "id": "phase1",
      "name": "施工阶段名称",
      "progress": 0,
      "color": "#颜色代码",
      "models": ["模型文件.gltf"],
      "startDate": "2024-01-01",
      "endDate": "2024-03-31"
    }
  ],
  "camera": {
    "position": [-800, 200, 800],
    "target": [0, 50, 0]
  }
}
```

### 模型文件要求
- **格式**：glTF 2.0 (.gltf 或 .glb)
- **大小**：建议单个文件 < 50MB
- **坐标系**：Y轴向上，原点为项目中心
- **材质**：使用PBR材质获得最佳效果

## 🎮 操作指南

### 键盘快捷键
- `Ctrl+S` - 保存项目状态
- `Ctrl+O` - 打开项目选择器
- `空格键` - 播放/暂停进度动画
- `1-9` - 快速切换施工阶段
- `F11` - 全屏模式
- `Esc` - 取消当前操作

### 鼠标操作
- **左键拖拽** - 旋转视角
- **滚轮** - 缩放视图
- **右键拖拽** - 平移视图（部分浏览器）

### 测量操作
1. 点击"测量"按钮激活测量工具
2. 选择测量模式（距离/面积/角度）
3. 在3D场景中点击测量点
4. 按`Enter`完成测量，`Esc`取消

## 📊 系统监控

### 性能指标
- **FPS**：实时帧率显示
- **对象数量**：场景中的3D对象统计
- **内存使用**：JavaScript堆内存使用情况

### 调试功能
在开发环境中可使用：
```javascript
// 获取系统信息
window.debugEnhancedBridge.getSystemInfo()

// 系统控制
window.debugEnhancedBridge.restart()
window.debugEnhancedBridge.pause()
window.debugEnhancedBridge.resume()
```

## 🔧 开发说明

### 添加新项目
1. 在 `projects/` 文件夹创建新项目文件夹
2. 添加 `config.json` 配置文件
3. 创建 `models/` 文件夹并放入glTF文件
4. 在 `ProjectManager` 中添加项目信息

### 自定义样式
- 修改 `styles/enhanced-ui.css` 调整界面样式
- 保持与原有样式的兼容性
- 确保响应式设计

### 扩展功能
- 参考现有模块的实现方式
- 保持模块化和可维护性
- 添加适当的错误处理
- 更新相关文档

## 📚 文档

- [详细设计说明书](docs/路桥修建演示系统详细设计说明书.md)
- [使用说明书](docs/使用说明书.md)
- [开发指南](docs/开发指南.md)
- [模型文件说明](projects/bridge-demo/models/README.md)

## 🐛 故障排除

### 常见问题
1. **系统无法启动**
   - 检查浏览器WebGL支持
   - 确认网络连接正常
   - 查看控制台错误信息

2. **模型加载失败**
   - 检查模型文件路径
   - 确认glTF文件格式正确
   - 查看网络请求状态

3. **性能问题**
   - 降低渲染质量设置
   - 减少同时加载的模型数量
   - 关闭不必要的视觉效果

## 📄 许可证

本项目基于原有的路桥建设数字孪生系统开发，保持原有的许可证协议。

## 🤝 贡献

欢迎提交问题报告和功能建议。在贡献代码前，请阅读[开发指南](docs/开发指南.md)。

## 📞 支持

如有问题或需要技术支持，请查看文档或联系开发团队。

---

**版本**: 1.0.0  
**最后更新**: 2024-01-01  
**兼容性**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
