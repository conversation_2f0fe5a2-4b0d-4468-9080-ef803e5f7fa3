/**
 * 进度服务
 * 重构后的进度管理，提供精确的阶段控制和可视化
 */

class ProgressService {
    constructor(core) {
        this.core = core;
        
        // 进度数据
        this.currentProject = null;
        this.phases = [];
        this.currentPhase = null;
        this.overallProgress = 0;
        
        // 阶段模型映射
        this.phaseModelMap = new Map();
        
        // 进度动画
        this.progressAnimations = new Map();
        this.animationConfig = {
            duration: 1000,
            easing: 'easeInOutCubic'
        };
        
        // 可视化设置
        this.visualConfig = {
            completedOpacity: 1.0,
            inProgressOpacity: 0.8,
            plannedOpacity: 0.3,
            highlightIntensity: 1.5,
            colorTransitions: true
        };
        
        // 播放控制
        this.playback = {
            isPlaying: false,
            speed: 1.0,
            direction: 1, // 1 = forward, -1 = backward
            interval: null,
            stepSize: 2 // 每次增加的进度百分比
        };
    }

    /**
     * 初始化服务
     */
    async initialize() {
        console.log('📊 初始化进度服务');
        
        // 监听项目变化
        this.core.eventBus.on('project:loaded', (data) => {
            this.onProjectLoaded(data.project);
        });
        
        this.core.eventBus.on('scene:projectLoaded', (data) => {
            this.onSceneProjectLoaded(data);
        });
        
        console.log('✅ 进度服务初始化完成');
    }

    /**
     * 项目加载事件处理
     */
    onProjectLoaded(project) {
        console.log(`📨 进度服务收到项目加载: ${project.name}`);
        this.initializeProjectProgress(project);
    }

    /**
     * 场景项目加载完成事件处理
     */
    onSceneProjectLoaded(data) {
        console.log(`📨 场景项目加载完成，注册模型到进度管理`);
        this.registerSceneModels();
    }

    /**
     * 初始化项目进度
     */
    initializeProjectProgress(project) {
        this.currentProject = project;
        this.phases = project.config.phases || [];
        this.currentPhase = this.phases[0] || null;
        
        // 初始化阶段模型映射
        this.initPhaseModelMap();
        
        // 计算总体进度
        this.updateOverallProgress();
        
        console.log(`📊 项目进度已初始化: ${this.phases.length}个阶段`);
        
        this.core.eventBus.emit('progress:initialized', {
            project,
            phases: this.phases,
            overallProgress: this.overallProgress
        });
    }

    /**
     * 初始化阶段模型映射
     */
    initPhaseModelMap() {
        this.phaseModelMap.clear();
        
        this.phases.forEach(phase => {
            this.phaseModelMap.set(phase.id, {
                phase: phase,
                models: [],
                registered: false
            });
        });
    }

    /**
     * 注册场景模型到进度管理
     */
    async registerSceneModels() {
        try {
            const sceneService = await this.core.getService('scene');
            const phaseModels = sceneService.getPhaseModels();
            
            phaseModels.forEach(({ phaseId, count }) => {
                if (this.phaseModelMap.has(phaseId)) {
                    const phaseData = this.phaseModelMap.get(phaseId);
                    phaseData.registered = true;
                    console.log(`📋 阶段 ${phaseId} 已注册 ${count} 个模型`);
                }
            });
            
            // 更新初始可视化状态
            this.updateAllPhaseVisuals();
            
            console.log('✅ 场景模型已注册到进度管理');
            
        } catch (error) {
            console.error('❌ 注册场景模型失败:', error);
        }
    }

    /**
     * 更新阶段进度
     */
    updatePhaseProgress(phaseId, progress, options = {}) {
        const {
            animate = true,
            updateVisuals = true,
            triggerEvents = true
        } = options;
        
        const phaseData = this.phaseModelMap.get(phaseId);
        if (!phaseData) {
            console.warn(`⚠️ 阶段不存在: ${phaseId}`);
            return;
        }
        
        const phase = phaseData.phase;
        const oldProgress = phase.progress || 0;
        const newProgress = Math.max(0, Math.min(100, progress));
        
        phase.progress = newProgress;
        
        console.log(`📈 更新阶段进度 ${phase.name}: ${oldProgress}% → ${newProgress}%`);
        
        // 更新可视化
        if (updateVisuals && phaseData.registered) {
            this.updatePhaseVisuals(phaseId, animate);
        }
        
        // 更新总体进度
        this.updateOverallProgress();
        
        // 触发事件
        if (triggerEvents) {
            this.core.eventBus.emit('progress:phaseUpdated', {
                phaseId,
                phase,
                oldProgress,
                newProgress,
                overallProgress: this.overallProgress
            });
        }
    }

    /**
     * 更新阶段可视化
     */
    async updatePhaseVisuals(phaseId, animate = true) {
        try {
            const sceneService = await this.core.getService('scene');
            const phaseData = this.phaseModelMap.get(phaseId);
            
            if (!phaseData) return;
            
            const phase = phaseData.phase;
            const targetState = this.calculatePhaseVisualState(phase);
            
            // 切换阶段可见性
            sceneService.togglePhaseVisibility(phaseId, targetState.visible);
            
            // 如果需要动画效果，可以在这里添加材质动画
            if (animate && targetState.visible) {
                this.animatePhaseProgress(phaseId, targetState);
            }
            
        } catch (error) {
            console.error(`❌ 更新阶段可视化失败: ${phaseId}`, error);
        }
    }

    /**
     * 计算阶段可视化状态
     */
    calculatePhaseVisualState(phase) {
        const progress = phase.progress || 0;
        
        return {
            visible: progress > 0,
            opacity: this.calculatePhaseOpacity(progress),
            color: phase.color || '#ffffff',
            highlight: phase.id === this.currentPhase?.id
        };
    }

    /**
     * 计算阶段透明度
     */
    calculatePhaseOpacity(progress) {
        if (progress >= 100) {
            return this.visualConfig.completedOpacity;
        } else if (progress > 0) {
            return this.visualConfig.inProgressOpacity;
        } else {
            return this.visualConfig.plannedOpacity;
        }
    }

    /**
     * 动画化阶段进度
     */
    animatePhaseProgress(phaseId, targetState) {
        // 取消之前的动画
        if (this.progressAnimations.has(phaseId)) {
            clearInterval(this.progressAnimations.get(phaseId));
        }
        
        const startTime = Date.now();
        const duration = this.animationConfig.duration;
        
        const animation = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const eased = this.easeInOutCubic(progress);
            
            // 这里可以添加具体的材质动画逻辑
            // 例如：改变材质颜色、透明度等
            
            if (progress >= 1) {
                clearInterval(animation);
                this.progressAnimations.delete(phaseId);
            }
        }, 16); // 60fps
        
        this.progressAnimations.set(phaseId, animation);
    }

    /**
     * 更新所有阶段可视化
     */
    updateAllPhaseVisuals(animate = true) {
        this.phases.forEach(phase => {
            if (this.phaseModelMap.get(phase.id)?.registered) {
                this.updatePhaseVisuals(phase.id, animate);
            }
        });
    }

    /**
     * 设置时间轴进度
     */
    setTimelineProgress(progress) {
        const newProgress = Math.max(0, Math.min(100, progress));
        this.overallProgress = newProgress;
        
        // 根据时间轴进度分配到各阶段
        this.distributeProgressToPhases(newProgress);
        
        // 更新所有阶段可视化
        this.updateAllPhaseVisuals(true);
        
        this.core.eventBus.emit('progress:timelineUpdated', {
            progress: newProgress,
            phases: this.phases
        });
        
        console.log(`⏰ 时间轴进度更新: ${newProgress}%`);
    }

    /**
     * 将总体进度分配到各阶段
     */
    distributeProgressToPhases(overallProgress) {
        const totalPhases = this.phases.length;
        if (totalPhases === 0) return;
        
        const progressPerPhase = 100 / totalPhases;
        
        this.phases.forEach((phase, index) => {
            const phaseStart = index * progressPerPhase;
            const phaseEnd = (index + 1) * progressPerPhase;
            
            let phaseProgress = 0;
            
            if (overallProgress >= phaseEnd) {
                phaseProgress = 100;
            } else if (overallProgress > phaseStart) {
                phaseProgress = ((overallProgress - phaseStart) / progressPerPhase) * 100;
            }
            
            // 更新阶段进度，但不触发事件避免循环
            this.updatePhaseProgress(phase.id, Math.round(phaseProgress), {
                animate: true,
                updateVisuals: true,
                triggerEvents: false
            });
        });
    }

    /**
     * 切换到指定阶段
     */
    switchToPhase(phaseId) {
        const phaseData = this.phaseModelMap.get(phaseId);
        if (!phaseData) {
            console.warn(`⚠️ 阶段不存在: ${phaseId}`);
            return;
        }
        
        const previousPhase = this.currentPhase;
        this.currentPhase = phaseData.phase;
        
        // 高亮当前阶段
        this.highlightCurrentPhase();
        
        console.log(`🎯 切换到阶段: ${this.currentPhase.name}`);
        
        this.core.eventBus.emit('progress:phaseChanged', {
            currentPhase: this.currentPhase,
            previousPhase
        });
    }

    /**
     * 高亮当前阶段
     */
    async highlightCurrentPhase() {
        if (!this.currentPhase) return;
        
        try {
            // 这里可以添加高亮效果
            // 例如：改变材质发光、边框等
            console.log(`✨ 高亮阶段: ${this.currentPhase.name}`);
            
        } catch (error) {
            console.error('❌ 高亮阶段失败:', error);
        }
    }

    /**
     * 播放控制
     */
    startPlayback(options = {}) {
        const {
            speed = 1.0,
            direction = 1,
            stepSize = 2
        } = options;
        
        this.playback.speed = speed;
        this.playback.direction = direction;
        this.playback.stepSize = stepSize;
        this.playback.isPlaying = true;
        
        const intervalTime = 100 / speed; // 基础间隔100ms
        
        this.playback.interval = setInterval(() => {
            const currentProgress = this.overallProgress;
            const newProgress = currentProgress + (stepSize * direction);
            
            if (newProgress > 100 || newProgress < 0) {
                this.stopPlayback();
                return;
            }
            
            this.setTimelineProgress(newProgress);
        }, intervalTime);
        
        console.log(`▶️ 开始播放进度动画 (速度: ${speed}x, 方向: ${direction > 0 ? '前进' : '后退'})`);
        
        this.core.eventBus.emit('progress:playbackStarted', {
            speed,
            direction,
            stepSize
        });
    }

    /**
     * 停止播放
     */
    stopPlayback() {
        if (this.playback.interval) {
            clearInterval(this.playback.interval);
            this.playback.interval = null;
        }
        
        this.playback.isPlaying = false;
        
        console.log('⏹️ 停止播放进度动画');
        
        this.core.eventBus.emit('progress:playbackStopped');
    }

    /**
     * 暂停/恢复播放
     */
    togglePlayback() {
        if (this.playback.isPlaying) {
            this.stopPlayback();
        } else {
            this.startPlayback({
                speed: this.playback.speed,
                direction: this.playback.direction,
                stepSize: this.playback.stepSize
            });
        }
    }

    /**
     * 更新总体进度
     */
    updateOverallProgress() {
        if (this.phases.length === 0) {
            this.overallProgress = 0;
            return;
        }
        
        const totalProgress = this.phases.reduce((sum, phase) => {
            return sum + (phase.progress || 0);
        }, 0);
        
        this.overallProgress = Math.round(totalProgress / this.phases.length);
    }

    /**
     * 获取进度数据
     */
    getProgressData() {
        return {
            currentProject: this.currentProject?.name || null,
            phases: this.phases.map(phase => ({
                ...phase,
                isActive: phase.id === this.currentPhase?.id
            })),
            currentPhase: this.currentPhase,
            overallProgress: this.overallProgress,
            playback: {
                isPlaying: this.playback.isPlaying,
                speed: this.playback.speed,
                direction: this.playback.direction
            }
        };
    }

    /**
     * 获取阶段统计
     */
    getPhaseStats() {
        return this.phases.map(phase => ({
            id: phase.id,
            name: phase.name,
            progress: phase.progress || 0,
            models: this.phaseModelMap.get(phase.id)?.models?.length || 0,
            registered: this.phaseModelMap.get(phase.id)?.registered || false,
            duration: phase.duration || 0,
            tasks: phase.tasks?.length || 0
        }));
    }

    /**
     * 缓动函数
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }

    /**
     * 重置进度
     */
    resetProgress() {
        this.stopPlayback();
        
        this.phases.forEach(phase => {
            phase.progress = 0;
        });
        
        this.overallProgress = 0;
        this.updateAllPhaseVisuals(true);
        
        console.log('🔄 进度已重置');
        
        this.core.eventBus.emit('progress:reset');
    }

    /**
     * 清理资源
     */
    async cleanup() {
        console.log('🧹 清理进度服务资源');
        
        // 停止播放
        this.stopPlayback();
        
        // 清理动画
        this.progressAnimations.forEach(animation => clearInterval(animation));
        this.progressAnimations.clear();
        
        // 清理数据
        this.phaseModelMap.clear();
        this.phases = [];
        this.currentProject = null;
        this.currentPhase = null;
        this.overallProgress = 0;
        
        console.log('✅ 进度服务资源清理完成');
    }
}