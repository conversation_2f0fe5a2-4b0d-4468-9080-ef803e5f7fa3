/**
 * 施工进度管理器
 * 负责施工进度的可视化、时间轴控制和阶段管理
 */

class ProgressManager {
    constructor(scene) {
        this.scene = scene;
        this.progressData = null;
        this.currentPhase = null;
        this.overallProgress = 0;
        
        this.phaseModels = new Map();
        this.progressAnimations = new Map();
        this.eventListeners = new Map();
        
        // 进度可视化设置
        this.visualSettings = {
            completedOpacity: 1.0,
            inProgressOpacity: 0.7,
            plannedOpacity: 0.3,
            animationDuration: 1000,
            highlightIntensity: 1.5
        };
    }

    /**
     * 初始化进度管理器
     */
    init(progressData) {
        console.log('📊 初始化施工进度管理器...');
        
        this.progressData = progressData;
        this.currentPhase = progressData.phases[0];
        this.overallProgress = this.calculateOverallProgress();
        
        // 初始化阶段模型映射
        this.initPhaseModels();
        
        console.log('✅ 施工进度管理器初始化完成');
        this.emit('initialized', { progressData: this.progressData });
    }

    /**
     * 初始化阶段模型映射
     */
    initPhaseModels() {
        this.progressData.phases.forEach(phase => {
            this.phaseModels.set(phase.id, []);
        });
    }

    /**
     * 注册阶段模型
     */
    registerPhaseModel(phaseId, model) {
        if (!this.phaseModels.has(phaseId)) {
            this.phaseModels.set(phaseId, []);
        }
        
        this.phaseModels.get(phaseId).push(model);
        
        // 设置初始显示状态
        this.updateModelVisibility(phaseId);
        
        console.log(`📋 模型已注册到阶段 ${phaseId}:`, model.name);
    }

    /**
     * 更新阶段进度
     */
    updatePhaseProgress(phaseId, progress, animate = true) {
        const phase = this.progressData.phases.find(p => p.id === phaseId);
        if (!phase) {
            console.warn(`⚠️ 阶段不存在: ${phaseId}`);
            return;
        }

        const oldProgress = phase.progress;
        phase.progress = Math.max(0, Math.min(100, progress));
        
        console.log(`📈 更新阶段进度 ${phase.name}: ${oldProgress}% → ${phase.progress}%`);
        
        // 更新模型可视化
        this.updateModelVisibility(phaseId, animate);
        
        // 重新计算总体进度
        this.overallProgress = this.calculateOverallProgress();
        
        // 触发事件
        this.emit('phaseProgressUpdated', {
            phaseId: phaseId,
            phase: phase,
            oldProgress: oldProgress,
            newProgress: phase.progress,
            overallProgress: this.overallProgress
        });
    }

    /**
     * 更新模型可视化
     */
    updateModelVisibility(phaseId, animate = true) {
        const phase = this.progressData.phases.find(p => p.id === phaseId);
        const models = this.phaseModels.get(phaseId);
        
        if (!phase || !models) return;

        const targetOpacity = this.calculatePhaseOpacity(phase);
        const targetColor = new THREE.Color(phase.color || '#ffffff');

        models.forEach(model => {
            if (animate) {
                this.animateModelProgress(model, targetOpacity, targetColor);
            } else {
                this.setModelProgress(model, targetOpacity, targetColor);
            }
        });
    }

    /**
     * 计算阶段透明度
     */
    calculatePhaseOpacity(phase) {
        const progress = phase.progress / 100;
        
        if (progress >= 1.0) {
            return this.visualSettings.completedOpacity;
        } else if (progress > 0) {
            return this.visualSettings.inProgressOpacity;
        } else {
            return this.visualSettings.plannedOpacity;
        }
    }

    /**
     * 动画更新模型进度
     */
    animateModelProgress(model, targetOpacity, targetColor) {
        // 取消之前的动画
        if (this.progressAnimations.has(model.uuid)) {
            clearInterval(this.progressAnimations.get(model.uuid));
        }

        const startTime = Date.now();
        const duration = this.visualSettings.animationDuration;
        
        // 获取当前状态
        const startOpacity = this.getCurrentModelOpacity(model);
        const startColor = this.getCurrentModelColor(model);

        const animation = setInterval(() => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const eased = this.easeInOutCubic(progress);
            
            // 插值计算
            const currentOpacity = startOpacity + (targetOpacity - startOpacity) * eased;
            const currentColor = startColor.clone().lerp(targetColor, eased);
            
            // 应用到模型
            this.setModelProgress(model, currentOpacity, currentColor);
            
            // 动画完成
            if (progress >= 1) {
                clearInterval(animation);
                this.progressAnimations.delete(model.uuid);
            }
        }, 16); // 60fps
        
        this.progressAnimations.set(model.uuid, animation);
    }

    /**
     * 设置模型进度状态
     */
    setModelProgress(model, opacity, color) {
        model.traverse(child => {
            if (child.isMesh && child.material) {
                // 设置透明度
                child.material.opacity = opacity;
                child.material.transparent = opacity < 1;
                
                // 设置颜色（如果是基础材质）
                if (child.material.color) {
                    child.material.color.copy(color);
                }
                
                // 设置发光效果（进行中的阶段）
                if (opacity === this.visualSettings.inProgressOpacity) {
                    child.material.emissive = color.clone().multiplyScalar(0.1);
                } else {
                    child.material.emissive.setHex(0x000000);
                }
            }
        });
    }

    /**
     * 获取当前模型透明度
     */
    getCurrentModelOpacity(model) {
        let opacity = 1.0;
        model.traverse(child => {
            if (child.isMesh && child.material) {
                opacity = child.material.opacity;
                return; // 只取第一个找到的
            }
        });
        return opacity;
    }

    /**
     * 获取当前模型颜色
     */
    getCurrentModelColor(model) {
        let color = new THREE.Color(0xffffff);
        model.traverse(child => {
            if (child.isMesh && child.material && child.material.color) {
                color = child.material.color.clone();
                return; // 只取第一个找到的
            }
        });
        return color;
    }

    /**
     * 设置时间轴进度
     */
    setTimelineProgress(progress) {
        this.overallProgress = Math.max(0, Math.min(100, progress));
        
        // 根据总体进度计算各阶段进度
        this.updatePhasesFromTimeline(this.overallProgress);
        
        this.emit('timelineUpdated', {
            progress: this.overallProgress,
            phases: this.progressData.phases
        });
    }

    /**
     * 根据时间轴更新各阶段进度
     */
    updatePhasesFromTimeline(overallProgress) {
        const totalPhases = this.progressData.phases.length;
        const progressPerPhase = 100 / totalPhases;
        
        this.progressData.phases.forEach((phase, index) => {
            const phaseStart = index * progressPerPhase;
            const phaseEnd = (index + 1) * progressPerPhase;
            
            let phaseProgress = 0;
            if (overallProgress > phaseEnd) {
                phaseProgress = 100;
            } else if (overallProgress > phaseStart) {
                phaseProgress = ((overallProgress - phaseStart) / progressPerPhase) * 100;
            }
            
            phase.progress = Math.round(phaseProgress);
            this.updateModelVisibility(phase.id, true);
        });
    }

    /**
     * 切换到指定阶段
     */
    switchToPhase(phaseId) {
        const phase = this.progressData.phases.find(p => p.id === phaseId);
        if (!phase) {
            console.warn(`⚠️ 阶段不存在: ${phaseId}`);
            return;
        }

        this.currentPhase = phase;
        
        // 高亮当前阶段
        this.highlightPhase(phaseId);
        
        console.log(`🎯 切换到阶段: ${phase.name}`);
        this.emit('phaseChanged', { phase: phase });
    }

    /**
     * 高亮阶段
     */
    highlightPhase(phaseId) {
        // 重置所有阶段高亮
        this.progressData.phases.forEach(phase => {
            const models = this.phaseModels.get(phase.id);
            if (models) {
                models.forEach(model => this.setModelHighlight(model, false));
            }
        });
        
        // 高亮当前阶段
        const models = this.phaseModels.get(phaseId);
        if (models) {
            models.forEach(model => this.setModelHighlight(model, true));
        }
    }

    /**
     * 设置模型高亮
     */
    setModelHighlight(model, highlight) {
        const intensity = highlight ? this.visualSettings.highlightIntensity : 1.0;
        
        model.traverse(child => {
            if (child.isMesh && child.material) {
                if (highlight) {
                    child.material.emissiveIntensity = intensity;
                } else {
                    child.material.emissiveIntensity = 0;
                }
            }
        });
    }

    /**
     * 计算总体进度
     */
    calculateOverallProgress() {
        if (!this.progressData || !this.progressData.phases.length) return 0;
        
        const totalProgress = this.progressData.phases.reduce((sum, phase) => {
            return sum + (phase.progress || 0);
        }, 0);
        
        return Math.round(totalProgress / this.progressData.phases.length);
    }

    /**
     * 缓动函数
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 获取进度数据
     */
    getProgressData() {
        return {
            phases: this.progressData.phases,
            currentPhase: this.currentPhase,
            overallProgress: this.overallProgress
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 清理动画
        this.progressAnimations.forEach(animation => clearInterval(animation));
        this.progressAnimations.clear();
        
        this.phaseModels.clear();
        this.eventListeners.clear();
        
        console.log('🧹 进度管理器资源已清理');
    }
}
