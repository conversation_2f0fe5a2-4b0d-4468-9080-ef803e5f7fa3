/**
 * 增强版场景管理器
 * 基于原有BridgeScene，增加模型管理和进度展示功能
 */

class EnhancedBridgeScene extends BridgeScene {
    constructor() {
        super();

        // 新增属性
        this.modelLoader = null;
        this.progressManager = null;
        this.loadedGLTFModels = new Map();
        this.modelGroups = new Map();

        // 场景状态
        this.isLoading = false;
        this.loadingProgress = 0;
        this.isEnhancementsReady = false;

        // 事件系统
        this.eventListeners = new Map();

        // 异步初始化增强功能
        this.initEnhancements().then(() => {
            this.isEnhancementsReady = true;
            console.log('✅ 增强场景已就绪');
            this.emit('enhancementsReady');
        }).catch(error => {
            console.error('❌ 增强场景初始化失败:', error);
            this.emit('enhancementsError', { error });
        });
    }

    /**
     * 初始化增强功能
     */
    async initEnhancements() {
        console.log('🚀 初始化增强场景功能...');

        try {
            // 初始化模型加载器
            this.modelLoader = new GLTFModelLoader();

            // 等待模型加载器初始化完成
            if (this.modelLoader.initializationPromise) {
                await this.modelLoader.initializationPromise;
            }

            // 初始化进度管理器
            this.progressManager = new ProgressManager(this);

            // 设置事件监听
            this.setupEnhancedEventListeners();

            console.log('✅ 增强场景功能初始化完成');

        } catch (error) {
            console.error('❌ 增强场景功能初始化失败:', error);

            // 创建一个简化的模型加载器作为后备
            this.createFallbackLoader();
        }
    }

    /**
     * 创建后备加载器
     */
    createFallbackLoader() {
        console.log('🔄 创建后备模型加载器...');

        this.modelLoader = {
            isInitialized: true,
            loadedModels: new Map(),
            eventListeners: new Map(),

            async loadModel(modelPath, options = {}) {
                console.warn('⚠️ 使用后备加载器，glTF功能受限');

                // 返回一个简单的立方体作为占位符
                const geometry = new THREE.BoxGeometry(50, 50, 50);
                const material = new THREE.MeshStandardMaterial({ color: 0x888888 });
                const mesh = new THREE.Mesh(geometry, material);
                mesh.name = options.name || 'Placeholder';

                const scene = new THREE.Group();
                scene.add(mesh);

                return { scene: scene, animations: [] };
            },

            on(event, callback) {
                if (!this.eventListeners.has(event)) {
                    this.eventListeners.set(event, []);
                }
                this.eventListeners.get(event).push(callback);
            },

            emit(event, data) {
                if (this.eventListeners.has(event)) {
                    this.eventListeners.get(event).forEach(callback => callback(data));
                }
            },

            cleanup() {
                this.loadedModels.clear();
                this.eventListeners.clear();
            }
        };

        // 初始化进度管理器
        this.progressManager = new ProgressManager(this);
        this.setupEnhancedEventListeners();

        console.log('✅ 后备加载器创建完成');
    }

    /**
     * 设置增强事件监听
     */
    setupEnhancedEventListeners() {
        // 监听模型加载事件
        this.modelLoader.on('modelLoaded', (data) => {
            this.onModelLoaded(data);
        });
        
        this.modelLoader.on('loadProgress', (data) => {
            this.onLoadProgress(data);
        });
        
        this.modelLoader.on('loadError', (data) => {
            this.onLoadError(data);
        });
        
        // 监听进度管理事件
        this.progressManager.on('phaseProgressUpdated', (data) => {
            this.onPhaseProgressUpdated(data);
        });
    }

    /**
     * 加载项目模型
     */
    async loadProjectModels(project) {
        console.log(`🏗️ 开始加载项目模型: ${project.name}`);
        
        this.isLoading = true;
        this.loadingProgress = 0;
        
        try {
            // 清理现有模型
            this.clearGLTFModels();
            
            // 初始化进度管理器
            this.progressManager.init(project.config);
            
            // 加载所有模型
            const modelPromises = project.models.map(async (modelInfo, index) => {
                try {
                    const gltf = await this.modelLoader.loadModel(
                        modelInfo.path,
                        {
                            onProgress: (percent) => {
                                this.updateLoadingProgress(index, percent, project.models.length);
                            },
                            name: modelInfo.name,
                            optimize: true
                        }
                    );
                    
                    // 添加到场景
                    this.addGLTFModel(modelInfo, gltf);
                    
                    return gltf;
                } catch (error) {
                    console.error(`❌ 模型加载失败: ${modelInfo.name}`, error);
                    return null;
                }
            });
            
            // 等待所有模型加载完成
            const results = await Promise.allSettled(modelPromises);
            
            // 统计加载结果
            const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
            const failed = results.length - successful;
            
            console.log(`✅ 模型加载完成: 成功 ${successful}, 失败 ${failed}`);
            
            // 更新场景
            this.updateObjectCount();
            this.enableShadows();
            
            this.isLoading = false;
            this.loadingProgress = 100;
            
            // 触发事件
            this.emit('projectModelsLoaded', {
                project: project,
                successful: successful,
                failed: failed
            });
            
        } catch (error) {
            console.error('❌ 项目模型加载失败:', error);
            this.isLoading = false;
            throw error;
        }
    }

    /**
     * 添加glTF模型到场景
     */
    addGLTFModel(modelInfo, gltf) {
        const model = gltf.scene;
        model.name = modelInfo.name;
        model.userData = {
            type: modelInfo.type,
            phase: modelInfo.phase,
            description: modelInfo.description,
            originalPath: modelInfo.path
        };
        
        // 添加到场景
        this.scene.add(model);
        
        // 缓存模型
        this.loadedGLTFModels.set(modelInfo.name, {
            model: model,
            gltf: gltf,
            info: modelInfo
        });
        
        // 注册到进度管理器
        if (modelInfo.phase && modelInfo.phase !== 'all') {
            this.progressManager.registerPhaseModel(modelInfo.phase, model);
        }
        
        // 按类型分组
        if (!this.modelGroups.has(modelInfo.type)) {
            this.modelGroups.set(modelInfo.type, []);
        }
        this.modelGroups.get(modelInfo.type).push(model);
        
        console.log(`➕ 模型已添加到场景: ${modelInfo.name}`);
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(modelIndex, modelProgress, totalModels) {
        const baseProgress = (modelIndex / totalModels) * 100;
        const currentModelProgress = (modelProgress / totalModels);
        this.loadingProgress = baseProgress + currentModelProgress;
        
        this.emit('loadingProgress', {
            progress: this.loadingProgress,
            currentModel: modelIndex + 1,
            totalModels: totalModels
        });
    }

    /**
     * 清理glTF模型
     */
    clearGLTFModels() {
        console.log('🧹 清理现有glTF模型...');
        
        this.loadedGLTFModels.forEach((modelData, name) => {
            // 从场景中移除
            this.scene.remove(modelData.model);
            
            // 清理几何体和材质
            modelData.model.traverse(child => {
                if (child.isMesh) {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(mat => mat.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                }
            });
        });
        
        this.loadedGLTFModels.clear();
        this.modelGroups.clear();
    }

    /**
     * 获取模型组
     */
    getModelGroup(type) {
        return this.modelGroups.get(type) || [];
    }

    /**
     * 切换模型组显示
     */
    toggleModelGroup(type, visible) {
        const models = this.getModelGroup(type);
        models.forEach(model => {
            model.visible = visible;
        });
        
        console.log(`👁️ ${visible ? '显示' : '隐藏'}模型组: ${type}`);
    }

    /**
     * 获取加载的模型信息
     */
    getLoadedModelsInfo() {
        const modelsInfo = [];
        this.loadedGLTFModels.forEach((modelData, name) => {
            modelsInfo.push({
                name: name,
                type: modelData.info.type,
                phase: modelData.info.phase,
                visible: modelData.model.visible,
                triangles: this.countTriangles(modelData.model)
            });
        });
        return modelsInfo;
    }

    /**
     * 计算模型三角形数量
     */
    countTriangles(model) {
        let triangles = 0;
        model.traverse(child => {
            if (child.isMesh && child.geometry) {
                const positions = child.geometry.attributes.position;
                if (positions) {
                    triangles += positions.count / 3;
                }
            }
        });
        return Math.round(triangles);
    }

    /**
     * 事件处理器
     */
    onModelLoaded(data) {
        console.log(`📦 模型加载完成: ${data.path}`);
    }

    onLoadProgress(data) {
        // 可以在这里更新UI进度条
    }

    onLoadError(data) {
        console.error(`❌ 模型加载错误: ${data.path}`, data.error);
    }

    onPhaseProgressUpdated(data) {
        console.log(`📊 阶段进度更新: ${data.phase.name} - ${data.newProgress}%`);
    }

    /**
     * 获取进度管理器
     */
    getProgressManager() {
        return this.progressManager;
    }

    /**
     * 获取模型加载器
     */
    getModelLoader() {
        return this.modelLoader;
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`增强场景事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const callbacks = this.eventListeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 重写父类的清理方法
     */
    cleanup() {
        // 清理glTF模型
        this.clearGLTFModels();

        // 清理管理器
        if (this.modelLoader) {
            this.modelLoader.cleanup();
        }
        if (this.progressManager) {
            this.progressManager.cleanup();
        }

        // 清理事件监听器
        this.eventListeners.clear();

        // 调用父类清理
        if (super.cleanup) {
            super.cleanup();
        }

        console.log('🧹 增强场景管理器资源已清理');
    }
}
