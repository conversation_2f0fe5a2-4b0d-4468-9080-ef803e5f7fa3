/**
 * 工具管理器
 * 统一管理测量、标注、导出等工具
 */

class ToolManager {
    constructor(scene, camera, renderer) {
        this.scene = scene;
        this.camera = camera;
        this.renderer = renderer;
        
        // 工具实例
        this.tools = {};
        this.activeTool = null;
        
        // 工具状态
        this.toolStates = new Map();
        this.eventListeners = new Map();
        
        this.init();
    }

    /**
     * 初始化工具管理器
     */
    init() {
        console.log('🛠️ 初始化工具管理器...');
        
        try {
            // 初始化各种工具
            this.initializeTools();
            
            // 设置工具间的协调
            this.setupToolCoordination();
            
            console.log('✅ 工具管理器初始化完成');
            this.emit('initialized', { tools: Object.keys(this.tools) });
            
        } catch (error) {
            console.error('❌ 工具管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化各种工具
     */
    initializeTools() {
        // 测量工具
        this.tools.measurement = new MeasurementTool(this.scene, this.camera, this.renderer);
        
        // 标注工具（简化版）
        this.tools.annotation = this.createAnnotationTool();
        
        // 截图工具
        this.tools.screenshot = this.createScreenshotTool();
        
        // 导出工具
        this.tools.export = this.createExportTool();
        
        // 视角工具
        this.tools.viewpoint = this.createViewpointTool();
        
        console.log(`🔧 已初始化 ${Object.keys(this.tools).length} 个工具`);
    }

    /**
     * 创建标注工具（简化版）
     */
    createAnnotationTool() {
        return {
            isActive: false,
            annotations: [],
            
            activate: () => {
                console.log('📝 标注工具已激活');
                this.setActiveTool('annotation');
            },
            
            deactivate: () => {
                console.log('📝 标注工具已停用');
                this.clearActiveTool();
            },
            
            addAnnotation: (position, text, type = 'info') => {
                const annotation = {
                    id: `annotation_${Date.now()}`,
                    position: position.clone(),
                    text: text,
                    type: type,
                    timestamp: Date.now()
                };
                
                this.tools.annotation.annotations.push(annotation);
                console.log('📝 标注已添加:', annotation);
                return annotation;
            },
            
            removeAnnotation: (id) => {
                const index = this.tools.annotation.annotations.findIndex(a => a.id === id);
                if (index >= 0) {
                    this.tools.annotation.annotations.splice(index, 1);
                    console.log('🗑️ 标注已删除:', id);
                }
            },
            
            getAnnotations: () => {
                return this.tools.annotation.annotations;
            },
            
            cleanup: () => {
                this.tools.annotation.annotations = [];
            }
        };
    }

    /**
     * 创建截图工具
     */
    createScreenshotTool() {
        return {
            takeScreenshot: (options = {}) => {
                const domElement = this.renderer.domElement;
                const {
                    width = domElement ? domElement.width : window.innerWidth,
                    height = domElement ? domElement.height : window.innerHeight,
                    format = 'png',
                    quality = 1.0
                } = options;
                
                try {
                    // 渲染当前帧
                    this.renderer.render(this.scene, this.camera);
                    
                    // 获取画布数据
                    const canvas = this.renderer.domElement;
                    const dataURL = canvas.toDataURL(`image/${format}`, quality);
                    
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.download = `bridge_screenshot_${Date.now()}.${format}`;
                    link.href = dataURL;
                    link.click();
                    
                    console.log('📸 截图已保存');
                    this.emit('screenshotTaken', { dataURL, format, quality });
                    
                    return dataURL;
                    
                } catch (error) {
                    console.error('❌ 截图失败:', error);
                    throw error;
                }
            }
        };
    }

    /**
     * 创建导出工具
     */
    createExportTool() {
        return {
            exportScene: (format = 'gltf') => {
                console.log(`📤 导出场景，格式: ${format}`);
                // 这里需要实现场景导出逻辑
                this.emit('sceneExported', { format });
            },
            
            exportMeasurements: () => {
                if (this.tools.measurement) {
                    this.tools.measurement.exportMeasurements();
                }
            },
            
            exportReport: (options = {}) => {
                const report = this.generateReport(options);
                this.downloadReport(report, options.format || 'json');
            }
        };
    }

    /**
     * 创建视角工具
     */
    createViewpointTool() {
        return {
            savedViewpoints: [],
            
            saveCurrentViewpoint: (name) => {
                const viewpoint = {
                    id: `viewpoint_${Date.now()}`,
                    name: name,
                    position: this.camera.position.clone(),
                    target: this.getControlsTarget(),
                    timestamp: Date.now()
                };
                
                this.tools.viewpoint.savedViewpoints.push(viewpoint);
                console.log('👁️ 视角已保存:', viewpoint);
                this.emit('viewpointSaved', viewpoint);
                
                return viewpoint;
            },
            
            loadViewpoint: (id) => {
                const viewpoint = this.tools.viewpoint.savedViewpoints.find(v => v.id === id);
                if (viewpoint) {
                    this.camera.position.copy(viewpoint.position);
                    // 这里需要更新控制器目标
                    console.log('👁️ 视角已加载:', viewpoint);
                    this.emit('viewpointLoaded', viewpoint);
                }
            },
            
            getViewpoints: () => {
                return this.tools.viewpoint.savedViewpoints;
            }
        };
    }

    /**
     * 设置工具协调
     */
    setupToolCoordination() {
        // 确保同时只有一个工具处于活动状态
        Object.keys(this.tools).forEach(toolName => {
            const tool = this.tools[toolName];
            
            if (tool.on) {
                tool.on('activated', () => {
                    this.setActiveTool(toolName);
                });
                
                tool.on('deactivated', () => {
                    if (this.activeTool === toolName) {
                        this.clearActiveTool();
                    }
                });
            }
        });
    }

    /**
     * 激活工具
     */
    activateTool(toolName, ...args) {
        if (!this.tools[toolName]) {
            console.warn(`⚠️ 工具不存在: ${toolName}`);
            return false;
        }
        
        // 停用当前活动工具
        if (this.activeTool && this.activeTool !== toolName) {
            this.deactivateTool(this.activeTool);
        }
        
        const tool = this.tools[toolName];
        
        try {
            if (tool.activate) {
                tool.activate(...args);
            }
            
            this.setActiveTool(toolName);
            console.log(`🔧 工具已激活: ${toolName}`);
            
            return true;
            
        } catch (error) {
            console.error(`❌ 工具激活失败: ${toolName}`, error);
            return false;
        }
    }

    /**
     * 停用工具
     */
    deactivateTool(toolName) {
        if (!this.tools[toolName]) {
            console.warn(`⚠️ 工具不存在: ${toolName}`);
            return false;
        }
        
        const tool = this.tools[toolName];
        
        try {
            if (tool.deactivate) {
                tool.deactivate();
            }
            
            if (this.activeTool === toolName) {
                this.clearActiveTool();
            }
            
            console.log(`🔧 工具已停用: ${toolName}`);
            return true;
            
        } catch (error) {
            console.error(`❌ 工具停用失败: ${toolName}`, error);
            return false;
        }
    }

    /**
     * 设置活动工具
     */
    setActiveTool(toolName) {
        this.activeTool = toolName;
        this.emit('toolActivated', { toolName });
    }

    /**
     * 清除活动工具
     */
    clearActiveTool() {
        const previousTool = this.activeTool;
        this.activeTool = null;
        
        if (previousTool) {
            this.emit('toolDeactivated', { toolName: previousTool });
        }
    }

    /**
     * 获取工具
     */
    getTool(toolName) {
        return this.tools[toolName];
    }

    /**
     * 获取活动工具
     */
    getActiveTool() {
        return this.activeTool;
    }

    /**
     * 获取所有工具状态
     */
    getToolStates() {
        const states = {};
        Object.keys(this.tools).forEach(toolName => {
            const tool = this.tools[toolName];
            states[toolName] = {
                isActive: tool.isActive || false,
                isAvailable: true
            };
        });
        return states;
    }

    /**
     * 生成报告
     */
    generateReport(options = {}) {
        const report = {
            timestamp: Date.now(),
            project: options.projectName || '未知项目',
            measurements: this.tools.measurement ? this.tools.measurement.getMeasurements() : [],
            annotations: this.tools.annotation ? this.tools.annotation.getAnnotations() : [],
            viewpoints: this.tools.viewpoint ? this.tools.viewpoint.getViewpoints() : [],
            summary: {
                totalMeasurements: this.tools.measurement ? this.tools.measurement.measurements.length : 0,
                totalAnnotations: this.tools.annotation ? this.tools.annotation.annotations.length : 0,
                totalViewpoints: this.tools.viewpoint ? this.tools.viewpoint.savedViewpoints.length : 0
            }
        };
        
        return report;
    }

    /**
     * 下载报告
     */
    downloadReport(report, format = 'json') {
        let content, mimeType, extension;
        
        switch (format) {
            case 'json':
                content = JSON.stringify(report, null, 2);
                mimeType = 'application/json';
                extension = 'json';
                break;
            case 'csv':
                content = this.convertToCSV(report);
                mimeType = 'text/csv';
                extension = 'csv';
                break;
            default:
                throw new Error(`不支持的导出格式: ${format}`);
        }
        
        const blob = new Blob([content], { type: mimeType });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `bridge_report_${Date.now()}.${extension}`;
        link.click();
        
        console.log(`📊 报告已导出: ${format.toUpperCase()}`);
    }

    /**
     * 转换为CSV格式
     */
    convertToCSV(report) {
        // 简化的CSV转换
        let csv = 'Type,Value,Unit,Timestamp\n';
        
        report.measurements.forEach(measurement => {
            csv += `${measurement.type},${measurement.value},${measurement.unit},${new Date(measurement.timestamp).toISOString()}\n`;
        });
        
        return csv;
    }

    /**
     * 获取控制器目标点
     */
    getControlsTarget() {
        // 这里需要从实际的控制器获取目标点
        // 简化实现
        return new THREE.Vector3(0, 0, 0);
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`工具管理器事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 停用所有工具
        Object.keys(this.tools).forEach(toolName => {
            this.deactivateTool(toolName);
        });
        
        // 清理工具实例
        Object.values(this.tools).forEach(tool => {
            if (tool.cleanup) {
                tool.cleanup();
            }
        });
        
        this.tools = {};
        this.activeTool = null;
        this.toolStates.clear();
        this.eventListeners.clear();
        
        console.log('🧹 工具管理器资源已清理');
    }
}
