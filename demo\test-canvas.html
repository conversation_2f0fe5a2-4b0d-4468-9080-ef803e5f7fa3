<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas创建测试</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #2c3e50; color: white; }
        #scene-container { width: 800px; height: 600px; border: 2px solid #3498db; margin: 20px 0; position: relative; }
        .log { background: #34495e; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 14px; }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .success { background: #27ae60; }
        .error { background: #e74c3c; }
        .info { background: #3498db; }
        button { background: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
    </style>
</head>
<body>
    <h1>🎨 Canvas创建和渲染测试</h1>
    
    <div>
        <button onclick="testCanvasCreation()">测试Canvas创建</button>
        <button onclick="testSceneInit()">测试Scene初始化</button>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div id="scene-container"></div>
    
    <div id="log" class="log">
        <div>准备开始测试...</div>
    </div>
    
    <div id="status"></div>

    <!-- Three.js -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    
    <!-- 场景管理 -->
    <script src="js/materials.js"></script>
    <script src="js/geometry.js"></script>
    <script src="js/scene.js"></script>
    <script src="js/controls.js"></script>

    <script>
        function log(message, type = 'info') {
            const container = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #95a5a6;">[${timestamp}]</span> ${message}`;
            
            switch(type) {
                case 'error': logEntry.style.color = '#e74c3c'; break;
                case 'success': logEntry.style.color = '#2ecc71'; break;
                case 'warning': logEntry.style.color = '#f39c12'; break;
                default: logEntry.style.color = '#ecf0f1';
            }
            
            container.appendChild(logEntry);
            container.scrollTop = container.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status');
            container.innerHTML = `<div class="status ${type}"><strong>${type.toUpperCase()}:</strong> ${message}</div>`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '<div>日志已清空...</div>';
        }
        
        function testCanvasCreation() {
            log('🎨 开始测试Canvas创建', 'info');
            
            try {
                // 测试1: 检查容器
                const container = document.getElementById('scene-container');
                if (!container) {
                    throw new Error('scene-container not found');
                }
                log('✅ 容器找到: scene-container', 'success');
                
                // 测试2: 检查现有canvas
                let canvas = document.getElementById('three-canvas');
                log(`📋 现有canvas: ${canvas ? '存在' : '不存在'}`, 'info');
                
                // 测试3: 创建canvas
                if (!canvas) {
                    canvas = document.createElement('canvas');
                    canvas.id = 'three-canvas';
                    canvas.style.width = '100%';
                    canvas.style.height = '100%';
                    canvas.style.display = 'block';
                    canvas.style.background = '#1a252f';
                    
                    container.appendChild(canvas);
                    log('✅ Canvas创建并添加到容器', 'success');
                }
                
                // 测试4: 检查canvas属性
                const rect = canvas.getBoundingClientRect();
                log(`📐 Canvas尺寸: ${rect.width}x${rect.height}`, 'info');
                
                if (rect.width === 0 || rect.height === 0) {
                    throw new Error('Canvas尺寸为0');
                }
                
                showStatus('Canvas创建测试通过', 'success');
                
            } catch (error) {
                log(`❌ Canvas创建失败: ${error.message}`, 'error');
                showStatus('Canvas创建测试失败', 'error');
            }
        }
        
        function testSceneInit() {
            log('🌟 开始测试Scene初始化', 'info');
            
            try {
                // 确保canvas存在
                testCanvasCreation();
                
                // 测试Three.js可用性
                if (typeof THREE === 'undefined') {
                    throw new Error('Three.js未加载');
                }
                log('✅ Three.js可用', 'success');
                
                // 创建场景实例
                log('创建BridgeScene实例...', 'info');
                const scene = new BridgeScene();
                
                // 初始化
                log('初始化场景...', 'info');
                scene.init();
                
                log('✅ Scene初始化成功', 'success');
                
                // 检查场景组件
                if (scene.renderer) {
                    log('✅ 渲染器已创建', 'success');
                    
                    const canvas = scene.renderer.domElement;
                    if (canvas) {
                        log(`📐 渲染器Canvas: ${canvas.width}x${canvas.height}`, 'info');
                    }
                }
                
                if (scene.camera) {
                    log('✅ 相机已创建', 'success');
                }
                
                if (scene.scene) {
                    log('✅ 场景已创建', 'success');
                }
                
                if (scene.controls) {
                    log('✅ 控制器已创建', 'success');
                }
                
                showStatus('Scene初始化测试通过', 'success');
                
                // 保存到全局以便调试
                window.testScene = scene;
                
            } catch (error) {
                log(`❌ Scene初始化失败: ${error.message}`, 'error');
                console.error('详细错误:', error);
                showStatus('Scene初始化测试失败', 'error');
            }
        }
        
        // 页面加载完成后自动测试canvas创建
        window.addEventListener('DOMContentLoaded', () => {
            log('🌐 页面已加载，自动测试Canvas创建', 'info');
            setTimeout(testCanvasCreation, 100);
        });
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            log(`❌ 全局错误: ${event.error.message}`, 'error');
            if (event.error.stack) {
                log(`Stack: ${event.error.stack}`, 'error');
            }
        });
    </script>
</body>
</html>