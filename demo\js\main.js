/**
 * 路桥建设数字孪生系统主应用程序
 * 整合所有模块，启动应用程序
 */

class BridgeDigitalTwin {
    constructor() {
        this.scene = null;
        this.controls = null;
        this.ui = null;
        
        this.isInitialized = false;
        this.isRunning = false;
        
        this.performanceMonitor = {
            frameCount: 0,
            lastTime: 0,
            fps: 0
        };
        
        this.init();
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('🌉 路桥数字孪生系统启动中...');
            
            // 检查浏览器兼容性（简化版）
            console.log('🔍 浏览器:', navigator.userAgent);
            console.log('🔍 WebGL支持:', this.hasWebGLSupport());

            if (!this.hasWebGLSupport()) {
                this.showCompatibilityError();
                return;
            }
            
            // 检查Three.js是否已加载
            if (typeof THREE === 'undefined') {
                throw new Error('Three.js库未加载，请检查网络连接或CDN状态');
            }
            console.log('✅ Three.js已就绪，版本:', THREE.REVISION);

            // 显示加载进度
            this.updateLoadingProgress(10, '初始化Three.js场景...');

            // 初始化场景
            this.scene = new BridgeScene();
            this.updateLoadingProgress(40, '创建3D模型...');
            
            // 等待场景完全加载
            await this.waitForSceneLoad();
            this.updateLoadingProgress(60, '设置交互控制...');
            
            // 初始化控制系统
            this.controls = new BridgeControls(
                this.scene.getScene(),
                this.scene.getCamera(),
                this.scene.getRenderer()
            );
            
            // 将控制器传递给场景
            this.scene.setControls(this.controls.getOrbitControls());
            this.updateLoadingProgress(80, '初始化用户界面...');
            
            // 初始化UI系统
            this.ui = new BridgeUI(this.scene, this.controls);
            this.updateLoadingProgress(90, '完成初始化...');
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 启动性能监控
            this.startPerformanceMonitoring();
            
            this.updateLoadingProgress(100, '系统就绪');
            
            // 标记为已初始化
            this.isInitialized = true;
            this.isRunning = true;
            
            console.log('✅ 路桥数字孪生系统启动完成');
            
            // 显示欢迎消息
            setTimeout(() => {
                this.ui.showNotification('success', '系统启动', '路桥数字孪生系统已成功启动');
            }, 2500);
            
        } catch (error) {
            console.error('❌ 系统启动失败:', error);
            this.showInitializationError(error);
        }
    }

    /**
     * 检查WebGL支持
     */
    hasWebGLSupport() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                console.log('✅ WebGL支持正常');
                return true;
            } else {
                console.error('❌ WebGL不受支持');
                return false;
            }
        } catch (e) {
            console.error('❌ WebGL检查失败:', e);
            return false;
        }
    }



    /**
     * 等待场景加载完成
     */
    waitForSceneLoad() {
        return new Promise((resolve) => {
            // 模拟加载时间
            setTimeout(resolve, 1000);
        });
    }

    /**
     * 更新加载进度
     */
    updateLoadingProgress(percentage, text) {
        const progressBar = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('.loading-text');
        
        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
        }
        
        if (loadingText) {
            loadingText.textContent = text;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 窗口事件
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
        
        window.addEventListener('error', (event) => {
            console.error('全局错误:', event.error);
            this.ui.showNotification('error', '系统错误', '发生了一个错误，请刷新页面重试');
        });
        
        // 可见性变化事件（页面切换时暂停/恢复）
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
        
        // 自定义事件监听
        this.ui.addEventListener('objectSelected', (event) => {
            console.log('对象被选中:', event.detail.object);
        });
        
        this.ui.addEventListener('objectDeselected', () => {
            console.log('对象取消选中');
        });
        
        // 键盘快捷键
        window.addEventListener('keydown', (event) => {
            this.handleGlobalKeyboard(event);
        });
    }

    /**
     * 处理全局键盘事件
     */
    handleGlobalKeyboard(event) {
        // 防止在输入框中触发快捷键
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return;
        }
        
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.togglePause();
                break;
            case 'KeyP':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.ui.takeScreenshot();
                }
                break;
            case 'KeyS':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.saveProject();
                }
                break;
            case 'KeyL':
                if (event.ctrlKey) {
                    event.preventDefault();
                    this.loadProject();
                }
                break;
            case 'F11':
                event.preventDefault();
                this.ui.toggleFullscreen();
                break;
        }
    }

    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        const monitor = () => {
            const currentTime = performance.now();
            this.performanceMonitor.frameCount++;
            
            if (currentTime - this.performanceMonitor.lastTime >= 1000) {
                this.performanceMonitor.fps = Math.round(
                    (this.performanceMonitor.frameCount * 1000) / 
                    (currentTime - this.performanceMonitor.lastTime)
                );
                
                this.performanceMonitor.frameCount = 0;
                this.performanceMonitor.lastTime = currentTime;
                
                // 检查性能警告
                this.checkPerformanceWarnings();
            }
            
            if (this.isRunning) {
                requestAnimationFrame(monitor);
            }
        };
        
        monitor();
    }

    /**
     * 检查性能警告
     */
    checkPerformanceWarnings() {
        const fps = this.performanceMonitor.fps;
        
        if (fps < 30 && fps > 0) {
            console.warn(`性能警告: FPS过低 (${fps})`);
            
            // 可以在这里实施性能优化措施
            this.optimizePerformance();
        }
    }

    /**
     * 性能优化
     */
    optimizePerformance() {
        // 降低阴影质量
        this.scene.updateSettings({
            shadows: false
        });
        
        // 显示性能警告
        this.ui.showNotification('warning', '性能优化', '检测到性能问题，已自动降低渲染质量');
    }

    /**
     * 暂停应用程序
     */
    pause() {
        this.isRunning = false;
        console.log('⏸️ 应用程序已暂停');
    }

    /**
     * 恢复应用程序
     */
    resume() {
        if (this.isInitialized && !this.isRunning) {
            this.isRunning = true;
            this.startPerformanceMonitoring();
            console.log('▶️ 应用程序已恢复');
        }
    }

    /**
     * 切换暂停状态
     */
    togglePause() {
        if (this.isRunning) {
            this.pause();
            this.ui.showNotification('info', '系统状态', '应用程序已暂停');
        } else {
            this.resume();
            this.ui.showNotification('info', '系统状态', '应用程序已恢复');
        }
    }

    /**
     * 保存项目
     */
    saveProject() {
        try {
            const projectData = {
                timestamp: Date.now(),
                camera: {
                    position: this.scene.getCamera().position.toArray(),
                    target: this.controls.getOrbitControls().target.toArray()
                },
                settings: this.scene.settings,
                version: '1.0.0'
            };
            
            const dataStr = JSON.stringify(projectData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `bridge_project_${Date.now()}.json`;
            link.click();
            
            this.ui.showNotification('success', '项目保存', '项目数据已保存');
            
        } catch (error) {
            console.error('保存项目失败:', error);
            this.ui.showNotification('error', '保存失败', '无法保存项目数据');
        }
    }

    /**
     * 加载项目
     */
    loadProject() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const projectData = JSON.parse(e.target.result);
                        this.applyProjectData(projectData);
                        this.ui.showNotification('success', '项目加载', '项目数据已加载');
                    } catch (error) {
                        console.error('加载项目失败:', error);
                        this.ui.showNotification('error', '加载失败', '无法解析项目文件');
                    }
                };
                reader.readAsText(file);
            }
        };
        
        input.click();
    }

    /**
     * 应用项目数据
     */
    applyProjectData(projectData) {
        // 恢复相机位置
        if (projectData.camera) {
            const camera = this.scene.getCamera();
            const controls = this.controls.getOrbitControls();
            
            camera.position.fromArray(projectData.camera.position);
            controls.target.fromArray(projectData.camera.target);
            controls.update();
        }
        
        // 恢复设置
        if (projectData.settings) {
            this.scene.updateSettings(projectData.settings);
        }
    }

    /**
     * 显示兼容性错误
     */
    showCompatibilityError() {
        const errorHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                        background: #2c3e50; color: white; display: flex;
                        align-items: center; justify-content: center; z-index: 10000;">
                <div style="text-align: center; max-width: 600px; padding: 40px;">
                    <h2>⚠️ WebGL 不支持</h2>
                    <p>您的浏览器或设备不支持 WebGL，这是运行 3D 应用程序所必需的。</p>
                    <div style="margin: 20px 0; text-align: left;">
                        <h3>解决方案：</h3>
                        <ul>
                            <li>确保您使用的是最新版本的浏览器</li>
                            <li>在浏览器设置中启用硬件加速</li>
                            <li>更新您的显卡驱动程序</li>
                            <li>尝试使用其他浏览器（Chrome、Firefox、Edge）</li>
                        </ul>
                    </div>
                    <button onclick="location.reload()"
                            style="margin-top: 20px; padding: 10px 20px;
                                   background: #3498db; color: white; border: none;
                                   border-radius: 5px; cursor: pointer;">
                        重新检测
                    </button>
                    <button onclick="window.open('test.html', '_blank')"
                            style="margin-top: 20px; margin-left: 10px; padding: 10px 20px;
                                   background: #e67e22; color: white; border: none;
                                   border-radius: 5px; cursor: pointer;">
                        运行兼容性测试
                    </button>
                </div>
            </div>
        `;

        document.body.innerHTML = errorHTML;
    }

    /**
     * 显示初始化错误
     */
    showInitializationError(error) {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div class="loading-content">
                    <div class="loading-logo" style="color: #e74c3c;">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h2>系统启动失败</h2>
                    <p style="color: #e74c3c; margin: 20px 0;">
                        ${error.message || '未知错误'}
                    </p>
                    <button onclick="location.reload()" 
                            style="padding: 10px 20px; background: #3498db; 
                                   color: white; border: none; border-radius: 5px; 
                                   cursor: pointer;">
                        重新加载
                    </button>
                </div>
            `;
        }
    }

    /**
     * 获取系统信息
     */
    getSystemInfo() {
        return {
            isInitialized: this.isInitialized,
            isRunning: this.isRunning,
            fps: this.performanceMonitor.fps,
            renderer: this.scene ? this.scene.getRenderer().info : null,
            memory: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1048576),
                total: Math.round(performance.memory.totalJSHeapSize / 1048576),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576)
            } : null
        };
    }

    /**
     * 清理资源
     */
    cleanup() {
        console.log('🧹 清理系统资源...');
        
        this.isRunning = false;
        
        if (this.ui) {
            this.ui.dispose();
        }
        
        if (this.controls) {
            this.controls.dispose();
        }
        
        if (this.scene) {
            // Three.js场景清理会在BridgeScene类中处理
        }
        
        console.log('✅ 资源清理完成');
    }

    /**
     * 重启应用程序
     */
    restart() {
        this.cleanup();
        setTimeout(() => {
            location.reload();
        }, 1000);
    }
}

// 全局变量
let bridgeApp = null;

// 等待DOM加载完成后启动应用程序
document.addEventListener('DOMContentLoaded', () => {
    bridgeApp = new BridgeDigitalTwin();
});

// 导出到全局作用域（用于调试）
window.BridgeDigitalTwin = BridgeDigitalTwin;
window.getBridgeApp = () => bridgeApp;

// 开发模式下的调试功能
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    window.debugBridge = {
        getSystemInfo: () => bridgeApp ? bridgeApp.getSystemInfo() : null,
        restart: () => bridgeApp ? bridgeApp.restart() : null,
        pause: () => bridgeApp ? bridgeApp.pause() : null,
        resume: () => bridgeApp ? bridgeApp.resume() : null
    };
    
    console.log('🔧 调试模式已启用，使用 window.debugBridge 访问调试功能');
}
