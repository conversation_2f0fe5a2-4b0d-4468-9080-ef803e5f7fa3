/**
 * 项目管理器
 * 负责项目选择、模型文件管理和项目配置
 */

class ProjectManager {
    constructor() {
        this.currentProject = null;
        this.availableProjects = [];
        this.projectConfigs = new Map();
        this.modelCache = new Map();
        
        // 事件系统
        this.eventListeners = new Map();
        
        this.init();
    }

    /**
     * 初始化项目管理器
     */
    async init() {
        console.log('🗂️ 初始化项目管理器...');
        
        try {
            // 扫描可用项目
            await this.scanAvailableProjects();
            
            // 加载默认项目（如果存在）
            if (this.availableProjects.length > 0) {
                await this.loadProject(this.availableProjects[0].path);
            }
            
            console.log('✅ 项目管理器初始化完成');
            this.emit('initialized', { projects: this.availableProjects });
            
        } catch (error) {
            console.error('❌ 项目管理器初始化失败:', error);
            this.emit('error', { message: '项目管理器初始化失败', error });
        }
    }

    /**
     * 扫描可用项目
     */
    async scanAvailableProjects() {
        // 预定义项目列表（实际部署时可通过API获取）
        const predefinedProjects = [
            {
                id: 'bridge-demo',
                name: '示例大桥项目',
                path: 'projects/bridge-demo',
                description: '一个完整的桥梁建设演示项目',
                thumbnail: 'assets/thumbnails/bridge-demo.jpg'
            },
            {
                id: 'highway-project',
                name: '高速公路项目',
                path: 'projects/highway-project',
                description: '高速公路建设项目演示',
                thumbnail: 'assets/thumbnails/highway-project.jpg'
            },
            {
                id: 'gddwroad',
                name: '公路道路工程项目',
                path: 'projects/gddwroad',
                description: '公路道路工程建设项目演示，包含完整的道路建设模型',
                thumbnail: 'assets/thumbnails/gddwroad.jpg'
            }
        ];

        this.availableProjects = [];

        for (const projectInfo of predefinedProjects) {
            try {
                const project = await this.validateProject(projectInfo);
                if (project) {
                    this.availableProjects.push(project);
                }
            } catch (error) {
                console.warn(`⚠️ 项目 ${projectInfo.name} 验证失败:`, error);
            }
        }

        console.log(`📁 发现 ${this.availableProjects.length} 个可用项目`);
    }

    /**
     * 验证项目有效性
     */
    async validateProject(projectInfo) {
        try {
            // 检查项目配置文件
            const configPath = `${projectInfo.path}/config.json`;
            const config = await this.loadProjectConfig(configPath);
            
            // 检查模型文件夹
            const modelList = await this.scanModelFiles(`${projectInfo.path}/models`);
            
            return {
                ...projectInfo,
                config: config,
                models: modelList,
                isValid: true
            };
        } catch (error) {
            console.warn(`项目 ${projectInfo.name} 验证失败:`, error);
            return null;
        }
    }

    /**
     * 加载项目配置
     */
    async loadProjectConfig(configPath) {
        // 默认配置
        const defaultConfig = {
            name: '未命名项目',
            version: '1.0.0',
            description: '',
            camera: {
                position: [-800, 200, 800],
                target: [0, 50, 0]
            },
            lighting: {
                intensity: 1.0,
                shadows: true
            },
            phases: [
                {
                    id: 'phase1',
                    name: '基础施工',
                    progress: 0,
                    models: []
                }
            ]
        };

        try {
            // 在实际部署中，这里应该通过fetch加载配置文件
            // const response = await fetch(configPath);
            // const config = await response.json();
            
            // 目前返回默认配置
            return defaultConfig;
        } catch (error) {
            console.warn('使用默认项目配置:', error);
            return defaultConfig;
        }
    }

    /**
     * 扫描模型文件
     */
    async scanModelFiles(modelPath) {
        // 根据项目路径确定项目类型
        const projectType = this.getProjectTypeFromPath(modelPath);
        
        let predefinedModels = [];
        
        if (projectType === 'gddwroad') {
            // gddwroad项目的模型配置
            predefinedModels = [
                {
                    name: 'gsdx1.glb',
                    path: `${modelPath}/gsdx1.glb`,
                    type: 'road',
                    size: '97.6MB',
                    description: '公路道路工程完整模型'
                }
            ];
        } else {
            // 其他项目的预定义模型列表
            predefinedModels = [
                {
                    name: 'bridge-main.gltf',
                    path: `${modelPath}/bridge-main.gltf`,
                    type: 'bridge',
                    size: '15.2MB',
                    description: '主桥结构模型'
                },
                {
                    name: 'road-system.gltf',
                    path: `${modelPath}/road-system.gltf`,
                    type: 'road',
                    size: '8.5MB',
                    description: '道路系统模型'
                },
                {
                    name: 'construction-equipment.gltf',
                    path: `${modelPath}/construction-equipment.gltf`,
                    type: 'equipment',
                    size: '12.1MB',
                    description: '施工设备模型'
                }
            ];
        }

        // 验证模型文件存在性（简化版）
        const validModels = [];
        for (const model of predefinedModels) {
            validModels.push({
                ...model,
                isAvailable: true,
                lastModified: new Date().toISOString()
            });
        }

        return validModels;
    }

    /**
     * 根据路径获取项目类型
     */
    getProjectTypeFromPath(modelPath) {
        if (modelPath.includes('gddwroad')) {
            return 'gddwroad';
        } else if (modelPath.includes('bridge-demo')) {
            return 'bridge-demo';
        } else if (modelPath.includes('highway-project')) {
            return 'highway-project';
        }
        return 'unknown';
    }

    /**
     * 加载项目
     */
    async loadProject(projectPath) {
        try {
            console.log(`📂 加载项目: ${projectPath}`);
            
            // 查找项目信息
            const project = this.availableProjects.find(p => p.path === projectPath);
            if (!project) {
                throw new Error(`项目不存在: ${projectPath}`);
            }

            // 设置当前项目
            this.currentProject = project;
            
            // 缓存项目配置
            this.projectConfigs.set(projectPath, project.config);
            
            console.log(`✅ 项目加载成功: ${project.name}`);
            this.emit('projectLoaded', { project: project });
            
            return project;
            
        } catch (error) {
            console.error('❌ 项目加载失败:', error);
            this.emit('error', { message: '项目加载失败', error });
            throw error;
        }
    }

    /**
     * 获取当前项目
     */
    getCurrentProject() {
        return this.currentProject;
    }

    /**
     * 获取可用项目列表
     */
    getAvailableProjects() {
        return this.availableProjects;
    }

    /**
     * 获取项目模型列表
     */
    getProjectModels(projectPath = null) {
        const project = projectPath ? 
            this.availableProjects.find(p => p.path === projectPath) : 
            this.currentProject;
            
        return project ? project.models : [];
    }

    /**
     * 切换项目
     */
    async switchProject(projectPath) {
        if (this.currentProject && this.currentProject.path === projectPath) {
            console.log('项目已经是当前项目');
            return this.currentProject;
        }

        // 清理当前项目资源
        if (this.currentProject) {
            this.emit('projectUnloading', { project: this.currentProject });
        }

        // 加载新项目
        return await this.loadProject(projectPath);
    }

    /**
     * 保存项目配置
     */
    async saveProjectConfig(config = null) {
        if (!this.currentProject) {
            throw new Error('没有当前项目');
        }

        const configToSave = config || this.currentProject.config;
        
        try {
            // 更新内存中的配置
            this.currentProject.config = configToSave;
            this.projectConfigs.set(this.currentProject.path, configToSave);
            
            // 在实际部署中，这里应该保存到服务器
            console.log('💾 项目配置已保存');
            this.emit('configSaved', { config: configToSave });
            
        } catch (error) {
            console.error('❌ 保存项目配置失败:', error);
            this.emit('error', { message: '保存配置失败', error });
            throw error;
        }
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }

    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理器错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 获取项目统计信息
     */
    getProjectStats() {
        if (!this.currentProject) return null;

        return {
            name: this.currentProject.name,
            modelCount: this.currentProject.models.length,
            totalSize: this.calculateTotalSize(),
            phases: this.currentProject.config.phases.length,
            overallProgress: this.calculateOverallProgress()
        };
    }

    /**
     * 计算模型总大小
     */
    calculateTotalSize() {
        if (!this.currentProject) return '0MB';
        
        let totalBytes = 0;
        this.currentProject.models.forEach(model => {
            // 简单的大小解析（实际应该从文件系统获取）
            const sizeStr = model.size || '0MB';
            const size = parseFloat(sizeStr.replace('MB', ''));
            totalBytes += size;
        });
        
        return `${totalBytes.toFixed(1)}MB`;
    }

    /**
     * 计算总体进度
     */
    calculateOverallProgress() {
        if (!this.currentProject || !this.currentProject.config.phases) return 0;
        
        const phases = this.currentProject.config.phases;
        const totalProgress = phases.reduce((sum, phase) => sum + (phase.progress || 0), 0);
        
        return Math.round(totalProgress / phases.length);
    }

    /**
     * 清理资源
     */
    cleanup() {
        this.modelCache.clear();
        this.projectConfigs.clear();
        this.eventListeners.clear();
        this.currentProject = null;
        console.log('🧹 项目管理器资源已清理');
    }
}
